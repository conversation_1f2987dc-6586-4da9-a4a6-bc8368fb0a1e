### Test 401 handling for different endpoints

### 1. First, login to get a valid session
POST {{baseUrl}}/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}

### 2. Wait for token to expire (5 seconds), then test getCurrentUser endpoint
### This should be handled by AuthContext (no automatic redirect)
GET {{baseUrl}}/me

### 3. Wait for token to expire (5 seconds), then test other endpoint
### This should trigger automatic redirect to login page
GET {{baseUrl}}/members?page=1&itemsPerPage=10

### 4. Test another endpoint after token expiration
### This should also trigger automatic redirect
GET {{baseUrl}}/cvs?page=1&itemsPerPage=10&status=draft