import {
  behance,
  dribbble,
  facebook,
  github,
  globe,
  instagram,
  linkedin,
  medium,
  pinterest,
  reddit,
  tiktok,
  twitch,
  youtube,
} from './icons';

import { parseHostName } from '@/helpers/urlUtils';

const SOCIAL_ICONS: { [key: string]: JSX.Element } = {
  facebook: facebook,
  instagram: instagram,
  linkedin: linkedin,
  youtube: youtube,
  tiktok: tiktok,
  github: github,
  pinterest: pinterest,
  dribbble: dribbble,
  twitch: twitch,
  reddit: reddit,
  behance: behance,
  medium: medium,
};

export function getSocialIcon(url: string) {
  const hostname = parseHostName(url);

  const match = Object.keys(SOCIAL_ICONS).find((key) =>
    hostname?.includes(key),
  );

  return match ? SOCIAL_ICONS[match] : globe;
}
