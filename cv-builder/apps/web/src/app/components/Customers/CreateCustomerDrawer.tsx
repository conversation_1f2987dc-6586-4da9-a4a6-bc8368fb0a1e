import { zodResolver } from '@hookform/resolvers/zod';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { AxiosError } from 'axios';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { ButtonPrimary } from '@/components/common/ButtonPrimary';
import { Drawer } from '@/components/common/Drawer';
import { Input } from '@/components/common/Input';
import { createCustomerRequest } from '@/helpers/requests';

const createCustomerSchema = z.object({
  name: z
    .string()
    .min(3, 'Client name must be at least 3 characters')
    .regex(
      /^[a-zA-Z0-9\s]+$/,
      'Client name can only contain letters, numbers, and spaces',
    )
    .trim(),
});

type CreateCustomerFormData = z.infer<typeof createCustomerSchema>;

interface CreateCustomerDrawerProps {
  isOpen: boolean;
  onClose: () => void;
}

export function CreateCustomerDrawer({
  isOpen,
  onClose,
}: CreateCustomerDrawerProps) {
  const queryClient = useQueryClient();

  const {
    register,
    handleSubmit,
    reset,
    setError,
    formState: { errors },
  } = useForm<CreateCustomerFormData>({
    resolver: zodResolver(createCustomerSchema),
    mode: 'onSubmit',
    reValidateMode: 'onChange',
  });

  const renderField = (label: string, children: React.ReactNode) => (
    <div className="grid grid-cols-[150px_1fr] md:grid-cols-[200px_1fr] items-center gap-x-4 py-2">
      <div className="font-bold text-smalldoge-3 text-msGray-3">{label}</div>
      <div className="px-1">{children}</div>
    </div>
  );

  const { mutate: createCustomer, isPending: isCreatingCustomer } = useMutation(
    {
      mutationFn: createCustomerRequest,
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: ['customers'] });
        reset();
        onClose();
      },
      onError: (error: AxiosError) => {
        const message = (error?.response?.data as { message: string })?.message;
        setError('root', {
          type: 'server',
          message: message || error.message || 'Failed to create client',
        });
      },
    },
  );

  const onSubmit = (data: CreateCustomerFormData) => {
    createCustomer({ name: data.name });
  };

  const handleClose = () => {
    reset();
    onClose();
  };

  return (
    <Drawer active={isOpen} onClose={handleClose} title="Add new client">
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
        {renderField(
          'Client Name',
          <Input
            id="customerName"
            type="text"
            {...register('name')}
            placeholder="Enter client name"
            className="w-full font-bold text-smalldoge-3 text-msBlack"
            error={errors.name?.message}
          />,
        )}
        {errors.root && (
          <p className="text-smalldoge-2 text-msRed-1">{errors.root.message}</p>
        )}
        <div className="flex justify-end pt-4">
          <ButtonPrimary
            type="submit"
            disabled={isCreatingCustomer}
            className="px-2 py-1"
            variant="blackCompact"
          >
            {isCreatingCustomer ? 'Creating...' : 'Create Client'}
          </ButtonPrimary>
        </div>
      </form>
    </Drawer>
  );
}
