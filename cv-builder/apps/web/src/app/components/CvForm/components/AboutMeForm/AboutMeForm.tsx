import { memo } from 'react';
import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css';
import { AboutMeData, SectionData } from 'shared/types';

import { FormReducerAction, ReducerActionType } from '@/pages/CVEditPage';

interface AboutMeFormProps {
  sectionId: string;
  data: AboutMeData;
  onDataChange: React.Dispatch<FormReducerAction>;
}

export const AboutMeForm = memo(
  ({ sectionId, data, onDataChange }: AboutMeFormProps) => {
    function handleChange(data: SectionData) {
      onDataChange({
        type: ReducerActionType.updateField,
        sectionId: sectionId,
        data,
      });
    }

    return (
      <div className="flex flex-col space-y-5 pt-4 p-px">
        <ReactQuill
          modules={{
            toolbar: [
              ['bold', 'italic', 'underline'],
              [{ list: 'ordered' }, { list: 'bullet' }],
            ],
          }}
          theme="snow"
          value={data.description}
          onChange={(value, delta, source) => {
            if (source === 'user') {
              handleChange({ ...data, description: value });
            }
          }}
        />
      </div>
    );
  },
);
