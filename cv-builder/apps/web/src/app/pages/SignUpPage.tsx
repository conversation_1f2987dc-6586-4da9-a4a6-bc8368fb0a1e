import { zodResolver } from '@hookform/resolvers/zod';
import { useMutation } from '@tanstack/react-query';
import { AxiosError } from 'axios';
import { useForm } from 'react-hook-form';
import { Link, useNavigate, useSearchParams } from 'react-router-dom';
import { SignUpDto } from 'shared/dto/auth/sign-up.dto';
import { z } from 'zod';

import { Input, ButtonPrimary, Button } from '../components';

import { NAVIGATE_PATH } from '@/helpers/constants';
import { signUpRequest } from '@/helpers/requests';

export function SignUpPage() {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const inviteToken = searchParams.get('token');
  const visualization = searchParams.get('visualization');

  // In test environment, only enable sign up if visualization param is present
  const isSignUpEnabled =
    import.meta.env.VITE_DEPLOYMENT_TYPE !== 'test' || visualization;

  const signUpSchema = z.object({
    email: z.string().email('Please enter a valid email address'),
    password: z.string().min(6, 'Password must be at least 6 characters'),
  });

  type SignUpFormValues = z.infer<typeof signUpSchema>;

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    setError: setFormError,
    getValues,
  } = useForm<SignUpFormValues>({
    resolver: zodResolver(signUpSchema),
    defaultValues: {
      email: '',
      password: '',
    },
  });

  const mutation = useMutation({
    mutationFn: (dto: SignUpDto) =>
      signUpRequest(dto, inviteToken || undefined),
    onSuccess: (data) => {
      if (!data.token) {
        navigate(NAVIGATE_PATH.emailVerification, {
          state: { email: getValues('email') },
        });
      } else {
        navigate(NAVIGATE_PATH.home);
      }
    },
    onError: (error: AxiosError) => {
      setFormError('root', {
        message: (error?.response?.data as { message: string })?.message,
      });
    },
  });

  const onSubmit = (data: SignUpFormValues) => {
    mutation.mutate({ email: data.email, password: data.password });
  };

  return (
    <div className="relative flex flex-col items-center justify-center min-h-screen p-4 bg-msGray-6">
      {/* Header elements */}
      <div className="absolute top-4 left-4 right-4 md:top-8 md:left-8 md:right-8 flex items-center justify-between">
        <img
          src="/images/logo-with-text.png"
          alt="CV Inventory"
          className="w-48"
        />
        <Link to={NAVIGATE_PATH.login}>
          <Button variant="default" size="sm">
            Login
          </Button>
        </Link>
      </div>

      {/* Sign Up Form Container */}
      <div className="w-full max-w-md p-10 mt-16 rounded-lg shadow-xl bg-card md:p-20 md:mt-0">
        <div className="flex justify-center">
          <img width={40} src="/images/logo.png" alt="logo" />
        </div>

        <div className="mb-8 text-center">
          <h1 className="mb-2 text-2xl font-black text-foreground">
            Create an account
          </h1>
          <p className="text-sm text-muted-foreground">
            Enter your email and password below to create your account
          </p>
        </div>

        {/* Form with react-hook-form */}
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          <div>
            <Input
              type="email"
              placeholder="<EMAIL>"
              className="w-full"
              {...register('email')}
              disabled={mutation.isPending || !isSignUpEnabled}
            />
            {errors.email && (
              <p className="mt-1 text-sm text-destructive">
                {errors.email.message}
              </p>
            )}
          </div>
          <div>
            <Input
              type="password"
              placeholder="Password"
              className="w-full"
              {...register('password')}
              disabled={mutation.isPending || !isSignUpEnabled}
            />
            {errors.password && (
              <p className="mt-1 text-sm text-destructive">
                {errors.password.message}
              </p>
            )}
          </div>

          {/* Display general error messages */}
          {errors.root && (
            <p className="text-sm text-destructive">{errors.root.message}</p>
          )}

          <ButtonPrimary
            type="submit"
            className="w-full rounded-md"
            disabled={mutation.isPending || isSubmitting || !isSignUpEnabled}
          >
            {mutation.isPending
              ? 'Signing up...'
              : inviteToken
                ? 'Sign up and accept invite'
                : 'Sign up with email'}
          </ButtonPrimary>
        </form>

        {!isSignUpEnabled && (
          <p className="mt-4 text-sm text-center text-muted-foreground">
            Sign up is disabled in test environment. Add query parameter to
            enable.
          </p>
        )}

        <p className="px-6 mt-8 text-xs text-center text-muted-foreground">
          By clicking continue, you agree to our{' '}
          <Link to="/terms" className="underline hover:text-foreground">
            Terms of Service
          </Link>{' '}
          and{' '}
          <Link to="/privacy" className="underline hover:text-foreground">
            Privacy Policy
          </Link>
          .
        </p>
      </div>
    </div>
  );
}
