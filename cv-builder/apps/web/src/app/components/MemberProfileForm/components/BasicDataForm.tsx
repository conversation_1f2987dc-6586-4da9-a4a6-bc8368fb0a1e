import { useFormContext } from 'react-hook-form';
import {
  Currency,
  currencyData,
  MemberSource,
  TimeRange,
  timeRangeData,
  UserType,
  userTypeData,
} from 'shared/types';

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuTrigger,
  ImageInput,
  Input,
  InputAutoWidth,
  MultiInput,
  TagsInput,
} from '../../common';
import { RowWrapper } from '../components';
import { MemberProfileFormValues } from '../types';

import { cn } from '@/lib/utils';

// const orgLevels = [
//   { id: 'l1', name: 'Level 1', color: '#278f24' },
//   { id: 'l2', name: 'Level 2', color: '#4ECDC4' },
//   { id: 'l3', name: 'Level 3', color: '#45B7D1' },
//   { id: 'l4', name: 'Level 4', color: '#FFBE0B' },
//   { id: 'l5', name: 'Level 5', color: '#FB5607' },
//   { id: 'l6', name: 'Level 6', color: '#8338EC' },
//   { id: 'l7', name: 'Level 7', color: '#3A86FF' },
//   { id: 'l8', name: 'Level 8', color: '#06D6A0' },
//   { id: 'l9', name: 'Level 9', color: '#FF006E' },
// ];

interface BasicDataFormProps {
  memberSource?: MemberSource;
}

export function BasicDataForm({ memberSource }: BasicDataFormProps) {
  const {
    register,
    setValue,
    watch,
    formState: { errors },
  } = useFormContext<MemberProfileFormValues>();
  const profileData = watch();
  // const selectedLevel = useMemo(
  //   () => orgLevels.find((l) => l.id === profileData.currentLevel),
  //   [profileData.currentLevel, orgLevels],
  // );

  return (
    <div className="flex flex-col space-y-2">
      <RowWrapper label="Profile Photo">
        <ImageInput
          url={profileData.avatarPreview}
          name={`${profileData.firstName} ${profileData.lastName}`}
          disabled={memberSource === MemberSource.muchskills}
          onImageUpdate={(file, previewUrl) => {
            setValue('avatarFile', file, { shouldDirty: true });
            setValue('avatarPreview', previewUrl, { shouldDirty: true });
          }}
        />
      </RowWrapper>

      <RowWrapper label="First name" required={true}>
        <div className="w-full">
          <Input
            {...register('firstName')}
            value={profileData.firstName}
            className="text-smalldoge-3"
            wrapperClassName="flex-grow w-full"
            disabled={memberSource === MemberSource.muchskills}
            error={errors.firstName?.message}
          />
        </div>
      </RowWrapper>

      <RowWrapper label="Last name">
        <Input
          {...register('lastName')}
          value={profileData.lastName}
          className="text-smalldoge-3"
          wrapperClassName="flex-grow w-full"
          disabled={memberSource === MemberSource.muchskills}
        />
      </RowWrapper>

      <RowWrapper label="Email">
        <Input
          {...register('email')}
          value={profileData.email}
          className="text-smalldoge-3"
          wrapperClassName="flex-grow w-full"
          disabled={memberSource === MemberSource.muchskills}
          error={errors.email?.message}
        />
      </RowWrapper>

      <RowWrapper label="Socials">
        <div className="flex-grow w-full">
          <MultiInput
            readOnly={memberSource === MemberSource.muchskills}
            links={profileData.socials}
            onChange={(links) =>
              setValue('socials', links, { shouldDirty: true })
            }
          />
        </div>
      </RowWrapper>

      <RowWrapper label="Location">
        <Input
          {...register('location')}
          value={profileData.location}
          className="text-smalldoge-3"
          wrapperClassName="flex-grow w-full"
          disabled={memberSource === MemberSource.muchskills}
          error={errors.location?.message}
        />
      </RowWrapper>

      <RowWrapper label="Telephone">
        <Input
          {...register('telephone')}
          value={profileData.telephone}
          className="text-smalldoge-3"
          wrapperClassName="flex-grow w-full"
          error={errors.telephone?.message}
        />
      </RowWrapper>

      <RowWrapper label="Current position">
        <Input
          {...register('currentPosition')}
          value={profileData.currentPosition}
          className="text-smalldoge-3"
          wrapperClassName="flex-grow w-full"
          disabled={memberSource === MemberSource.muchskills}
        />
      </RowWrapper>

      {/* <RowWrapper label="Current level">
        <DropdownMenu modal={false}>
          <DropdownMenuTrigger asChild>
            <div className="flex items-center h-9">
              <div className="flex items-center space-x-1 cursor-pointer">
                <span
                  className="w-4 h-4 rounded-full"
                  style={{
                    backgroundColor: selectedLevel?.color || '#AFAFAF',
                  }}
                />
                <span className="font-bold text-smalldoge-3 text-msGray-2">
                  {selectedLevel ? selectedLevel.name : 'Select level'}
                </span>
              </div>
            </div>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            collisionPadding={10}
            align="start"
            className="overflow-auto prevent-drawer-outside-click max-h-48"
          >
            <DropdownMenuGroup>
              {orgLevels.map((level) => (
                <DropdownMenuItem
                  key={level.id}
                  onClick={() =>
                    onUpdate({ type: 'currentLevel', value: level.id })
                  }
                >
                  <div className="flex items-center space-x-1">
                    <span
                      className="w-4 h-4 rounded-full"
                      style={{ backgroundColor: level.color }}
                    />
                    <span className="font-bold text-smalldoge-3">
                      {level.name}
                    </span>
                  </div>
                </DropdownMenuItem>
              ))}
            </DropdownMenuGroup>
          </DropdownMenuContent>
        </DropdownMenu>
      </RowWrapper> */}

      <RowWrapper label="Type">
        <DropdownMenu modal={false}>
          <DropdownMenuTrigger asChild>
            <div className="flex items-center h-9">
              <div
                className={cn(
                  'w-fit max-w-full bg-msGray-6 rounded-[100px] px-2 cursor-pointer',
                  profileData.type &&
                    userTypeData[profileData.type as UserType].color,
                )}
              >
                <span className="font-bold truncate text-smalldoge-3">
                  {profileData.type
                    ? userTypeData[profileData.type as UserType].name
                    : 'Select type'}
                </span>
              </div>
            </div>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            collisionPadding={10}
            align="start"
            className="overflow-auto prevent-drawer-outside-click max-h-48"
          >
            <DropdownMenuGroup>
              {Object.entries(userTypeData).map(([key, value]) => (
                <DropdownMenuItem
                  key={key}
                  onClick={() =>
                    setValue('type', key as UserType, { shouldDirty: true })
                  }
                >
                  <div
                    className={cn(
                      'w-fit max-w-full bg-msGray-6 rounded-[100px] px-2',
                      userTypeData[key as UserType].color,
                    )}
                  >
                    <span className="font-bold truncate text-smalldoge-3">
                      {value.name}
                    </span>
                  </div>
                </DropdownMenuItem>
              ))}
            </DropdownMenuGroup>
          </DropdownMenuContent>
        </DropdownMenu>
      </RowWrapper>

      {/* <RowWrapper label="Associate clients">
        <div className="flex items-center flex-grow w-full space-x-1 h-9">
          {profileData.clients.map((client) => (
            <div
              key={client}
              className="w-fit max-w-full bg-msGray-6 rounded-[100px] px-2"
            >
              <span className="font-bold truncate text-smalldoge-3">
                {client}
              </span>
            </div>
          ))}
        </div>
      </RowWrapper> */}

      <RowWrapper label="Default rate">
        <div className="flex items-center flex-grow w-full h-9">
          <div className="flex items-center space-x-2">
            <InputAutoWidth
              classNames="pl-4 pr-2"
              inputProps={{
                maxLength: 6,
                value: profileData.costRate.amount || '',
                placeholder: '0',
                prefixElement: (
                  <span className="text-smalldoge-3 text-msGray-2 font-bold absolute transform leading-5 -translate-y-2.5 translate-x-1.5 top-[50%]">
                    {currencyData[profileData.costRate.currency].sign}
                  </span>
                ),
                onChange: (e) => {
                  if (e.target.value === '') {
                    setValue(
                      'costRate',
                      {
                        ...profileData.costRate,
                        amount: 0,
                      },
                      { shouldDirty: true },
                    );
                    return;
                  }
                  if (isNaN(+e.target.value)) return;
                  if (+e.target.value < 0) return;

                  setValue(
                    'costRate',
                    {
                      ...profileData.costRate,
                      amount: +e.target.value,
                    },
                    { shouldDirty: true },
                  );
                },
                onFocus: (e) => {
                  e.target.select();
                },
              }}
            />
            <span>/</span>
            <DropdownMenu modal={false}>
              <DropdownMenuTrigger asChild>
                <div className="flex items-center h-6 rounded-sm cursor-pointer bg-msGray-6">
                  <span className="px-2 font-bold text-smalldoge-3 text-msGray-2">
                    {timeRangeData[
                      profileData.costRate.timeRange
                    ].name.toUpperCase()}
                  </span>
                </div>
              </DropdownMenuTrigger>
              <DropdownMenuContent
                collisionPadding={10}
                align="start"
                className="overflow-auto prevent-drawer-outside-click max-h-48"
              >
                <DropdownMenuGroup>
                  {Object.entries(timeRangeData).map(([key, value]) => (
                    <DropdownMenuItem
                      key={key}
                      onClick={() =>
                        setValue(
                          'costRate',
                          {
                            ...profileData.costRate,
                            timeRange: key as TimeRange,
                          },
                          { shouldDirty: true },
                        )
                      }
                    >
                      <span className="font-bold truncate text-smalldoge-3">
                        {value.name.toUpperCase()}
                      </span>
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuGroup>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
          <div className="flex items-center ml-auto space-x-2">
            {/* <span
              className="font-bold cursor-pointer text-smalldoge-4 text-msBlue-1"
              onClick={() => {
                //TODO: Find out what exactly this button should do
              }}
            >
              Reset to default
            </span> */}
            <DropdownMenu modal={false}>
              <DropdownMenuTrigger asChild>
                <div className="flex items-center h-6 rounded-sm cursor-pointer bg-msGray-6">
                  <span className="px-2 font-bold text-smalldoge-3 text-msGray-2">
                    {currencyData[profileData.costRate.currency].name}
                  </span>
                </div>
              </DropdownMenuTrigger>
              <DropdownMenuContent
                collisionPadding={10}
                align="start"
                className="overflow-auto prevent-drawer-outside-click max-h-48"
              >
                <DropdownMenuGroup>
                  {Object.entries(currencyData).map(([key, value]) => (
                    <DropdownMenuItem
                      key={key}
                      onClick={() =>
                        setValue(
                          'costRate',
                          {
                            ...profileData.costRate,
                            currency: key as Currency,
                          },
                          { shouldDirty: true },
                        )
                      }
                    >
                      <span className="font-bold truncate text-smalldoge-3">
                        {value.name}
                      </span>
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuGroup>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </RowWrapper>

      <RowWrapper label="Avg. cost to company">
        <div className="flex items-center flex-grow w-full h-9">
          <div className="flex items-center space-x-2">
            <InputAutoWidth
              classNames="pl-4 pr-2"
              inputProps={{
                maxLength: 6,
                value: profileData.costToCompany.amount || '',
                placeholder: '0',
                prefixElement: (
                  <span className="text-smalldoge-3 text-msGray-2 font-bold absolute transform leading-5 -translate-y-2.5 translate-x-1.5 top-[50%]">
                    {currencyData[profileData.costToCompany.currency].sign}
                  </span>
                ),
                onChange: (e) => {
                  if (e.target.value === '') {
                    setValue(
                      'costToCompany',
                      {
                        ...profileData.costToCompany,
                        amount: 0,
                      },
                      { shouldDirty: true },
                    );
                    return;
                  }
                  if (isNaN(+e.target.value)) return;
                  if (+e.target.value < 0) return;

                  setValue(
                    'costToCompany',
                    {
                      ...profileData.costToCompany,
                      amount: +e.target.value,
                    },
                    { shouldDirty: true },
                  );
                },
                onFocus: (e) => {
                  e.target.select();
                },
              }}
            />
            <span>/</span>
            <DropdownMenu modal={false}>
              <DropdownMenuTrigger asChild>
                <div className="flex items-center h-6 rounded-sm cursor-pointer bg-msGray-6">
                  <span className="px-2 font-bold text-smalldoge-3 text-msGray-2">
                    {timeRangeData[
                      profileData.costToCompany.timeRange
                    ].name.toUpperCase()}
                  </span>
                </div>
              </DropdownMenuTrigger>
              <DropdownMenuContent
                collisionPadding={10}
                align="start"
                className="overflow-auto prevent-drawer-outside-click max-h-48"
              >
                <DropdownMenuGroup>
                  {Object.entries(timeRangeData).map(([key, value]) => (
                    <DropdownMenuItem
                      key={key}
                      onClick={() =>
                        setValue(
                          'costToCompany',
                          {
                            ...profileData.costToCompany,
                            timeRange: key as TimeRange,
                          },
                          { shouldDirty: true },
                        )
                      }
                    >
                      <span className="font-bold truncate text-smalldoge-3">
                        {value.name.toUpperCase()}
                      </span>
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuGroup>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
          <DropdownMenu modal={false}>
            <DropdownMenuTrigger asChild>
              <div className="flex items-center h-6 ml-auto rounded-sm cursor-pointer bg-msGray-6">
                <span className="px-2 font-bold text-smalldoge-3 text-msGray-2">
                  {currencyData[profileData.costToCompany.currency].name}
                </span>
              </div>
            </DropdownMenuTrigger>
            <DropdownMenuContent
              collisionPadding={10}
              align="start"
              className="overflow-auto prevent-drawer-outside-click max-h-48"
            >
              <DropdownMenuGroup>
                {Object.entries(currencyData).map(([key, value]) => (
                  <DropdownMenuItem
                    key={key}
                    onClick={() =>
                      setValue(
                        'costToCompany',
                        {
                          ...profileData.costToCompany,
                          currency: key as Currency,
                        },
                        { shouldDirty: true },
                      )
                    }
                  >
                    <span className="font-bold truncate text-smalldoge-3">
                      {value.name}
                    </span>
                  </DropdownMenuItem>
                ))}
              </DropdownMenuGroup>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </RowWrapper>

      {/* <RowWrapper label="Muchskills profile">
        <Input
          value={muchskillsLink}
          className="text-smalldoge-3"
          wrapperClassName="flex-grow w-full"
          onChange={(e) => setMuchskillsLink(e.target.value)}
        />
      </RowWrapper>

      <RowWrapper label="LinkedIn profile">
        <Input
          value={linkedinLink}
          className="text-smalldoge-3"
          wrapperClassName="flex-grow w-full"
          onChange={(e) => setLinkedinLink(e.target.value)}
        />
      </RowWrapper> */}

      <RowWrapper label="Years of experience">
        <div className="flex items-center space-x-2 h-9">
          <InputAutoWidth
            inputProps={{
              maxLength: 2,
              value: profileData.yearsOfExperience || '',
              placeholder: '0',
              onChange: (e) => {
                if (e.target.value === '') {
                  setValue('yearsOfExperience', 0, {
                    shouldDirty: true,
                  });
                  return;
                }
                if (isNaN(+e.target.value)) return;
                if (+e.target.value < 0) return;

                setValue('yearsOfExperience', +e.target.value, {
                  shouldDirty: true,
                });
              },
              onFocus: (e) => {
                e.target.select();
              },
            }}
          />
          <span className="font-bold text-smalldoge-3 text-msGray-2">
            years
          </span>
        </div>
      </RowWrapper>
      <RowWrapper label="Languages">
        <TagsInput
          placeholder="Languages..."
          className="flex-grow w-full text-smalldoge-3"
          value={profileData.languages}
          onValueChange={(value) =>
            setValue('languages', value, { shouldDirty: true })
          }
        />
      </RowWrapper>
    </div>
  );
}
