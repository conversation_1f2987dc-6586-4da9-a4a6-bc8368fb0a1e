'use client';

import { useTheme } from 'next-themes';
import { useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { Toaster as Sonner, toast } from 'sonner';

import { getQueryParams } from '@/helpers/urlUtils';

type ToasterProps = React.ComponentProps<typeof Sonner>;

const Toaster = ({ ...props }: ToasterProps) => {
  const { theme = 'system' } = useTheme();
  const location = useLocation();
  const navigate = useNavigate();

  useEffect(() => {
    const params = getQueryParams();

    if (params.message) {
      toast.success(params.message);
    }
  }, [location, navigate]);

  return (
    <Sonner
      theme={theme as ToasterProps['theme']}
      className="toaster group"
      toastOptions={{
        classNames: {
          toast:
            'group toast group-[.toaster]:bg-background group-[.toaster]:text-foreground group-[.toaster]:border-border group-[.toaster]:shadow-lg',
          description: 'group-[.toast]:text-muted-foreground',
          actionButton:
            'group-[.toast]:bg-primary group-[.toast]:text-primary-foreground',
          cancelButton:
            'group-[.toast]:bg-muted group-[.toast]:text-muted-foreground',
        },
      }}
      {...props}
    />
  );
};

export { Toaster };
