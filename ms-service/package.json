{"name": "dusl", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "deploy": "sh deploy-dev.sh", "deploy-prod": "sh deploy-prod.sh"}, "dependencies": {"@nestjs/common": "^10.4.9", "@nestjs/config": "^3.3.0", "@nestjs/core": "^10.4.9", "@nestjs/platform-express": "^10.4.9", "axios": "^1.7.7", "date-fns": "^4.1.0", "geoip-lite": "^1.4.7", "metascraper": "^5.45.25", "metascraper-description": "^5.45.25", "metascraper-image": "^5.45.25", "metascraper-logo-favicon": "^5.45.25", "metascraper-title": "^5.45.25", "metascraper-url": "^5.45.25", "openai": "^5.8.2", "playwright": "^1.49.1", "reflect-metadata": "^0.1.13", "rxjs": "^7.2.0"}, "devDependencies": {"@nestjs/cli": "^10.4.8", "@nestjs/schematics": "^10.2.3", "@nestjs/testing": "^10.4.9", "@types/express": "^5.0.0", "@types/geoip-lite": "^1.4.1", "@types/jest": "29.2.4", "@types/node": "18.11.18", "@types/supertest": "^2.0.11", "@typescript-eslint/eslint-plugin": "^5.0.0", "@typescript-eslint/parser": "^5.0.0", "eslint": "^8.0.1", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "jest": "29.3.1", "prettier": "^2.3.2", "source-map-support": "^0.5.20", "supertest": "^6.1.3", "ts-jest": "29.0.3", "ts-loader": "^9.2.3", "ts-node": "^10.0.0", "tsconfig-paths": "4.1.1", "typescript": "^4.7.4"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}