{"mcpServers": {"playwright": {"command": "npx", "args": ["@playwright/mcp@latest"]}, "shadcn-ui": {"command": "npx", "args": ["@jpisnice/shadcn-ui-mcp-server", "--github-api-key", "*********************************************************************************************"]}, "github.com/GLips/Figma-Context-MCP": {"timeout": 60, "command": "npx", "args": ["-y", "figma-developer-mcp", "--figma-api-key=*********************************************", "--st<PERSON>"], "env": {}}, "github.com/upstash/context7-mcp": {"command": "npx", "args": ["-y", "@upstash/context7-mcp@latest"]}, "MongoDB": {"command": "npx", "args": ["-y", "mongodb-mcp-server", "--connectionString", "mongodb://localhost:27017/cv"]}}}