import { format } from 'date-fns';
import { CalendarIcon, Ellipsis } from 'lucide-react';
import { useFormContext } from 'react-hook-form';

import {
  Label,
  MonthPicker,
  Popover,
  PopoverTrigger,
  Button,
  PopoverContent,
} from '../../../../../../components';
import {
  Textarea,
  Input,
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  ButtonSecondary,
} from '../../../../../common';

import {
  EducationItemType,
  MemberProfileFormValues,
} from '@/components/MemberProfileForm/types';
import { cn } from '@/lib/utils';

interface EducationItemProps {
  index: number;
  dropDownItems: { title: string; onClick: () => void }[];
  readonly?: boolean;
}

export function EducationItem({
  index,
  dropDownItems,
  readonly,
}: EducationItemProps) {
  const {
    register,
    setValue,
    formState: { errors },
    watch,
  } = useFormContext<MemberProfileFormValues>();
  const data = watch(`education.${index}`) as EducationItemType;
  const itemErrors = errors.education?.[index];

  return (
    <div className="relative p-4 border rounded-sm">
      <div className="absolute flex space-x-2 top-1 right-1">
        <DropdownMenu modal={false}>
          <DropdownMenuTrigger asChild disabled={readonly}>
            <ButtonSecondary variant="icon">
              <Ellipsis size={14} />
            </ButtonSecondary>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            collisionPadding={10}
            align="end"
            className="w-32 prevent-drawer-outside-click"
          >
            <DropdownMenuGroup>
              {dropDownItems.map((item, i) => (
                <DropdownMenuItem key={i} onClick={item.onClick}>
                  <span className="text-smalldoge-3">{item.title}</span>
                </DropdownMenuItem>
              ))}
            </DropdownMenuGroup>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
      <div className="flex flex-col space-y-5">
        <div className="flex flex-col space-y-2">
          <Label
            mandatory={true}
            label="School Name"
            labelProps={{ htmlFor: `schoolName-${index}` }}
          />
          <Input
            id={`schoolName-${index}`}
            type="text"
            {...register(`education.${index}.schoolName`)}
            disabled={readonly}
            error={itemErrors?.schoolName?.message}
          />
        </div>
        <div className="flex flex-col space-y-2">
          <Label label="Degree" labelProps={{ htmlFor: `degree-${index}` }} />
          <Input
            id={`degree-${index}`}
            type="text"
            {...register(`education.${index}.degree`)}
            disabled={readonly}
          />
        </div>
        <div className="flex flex-col space-y-2">
          <Label
            label="Description"
            labelProps={{ htmlFor: `roleDescription-${index}` }}
          />
          <Textarea
            id={`roleDescription-${index}`}
            placeholder="Describe what you studied briefly"
            {...register(`education.${index}.description`)}
            disabled={readonly}
            className="min-h-28"
          />
        </div>

        <div className="flex space-x-5">
          <div className="flex flex-col w-1/2 space-y-2">
            <Label label="Start" />
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant={'outline'}
                  disabled={readonly}
                  className={cn(
                    'justify-start text-left font-normal',
                    !data?.startDate && 'text-muted-foreground',
                  )}
                >
                  <CalendarIcon className="w-4 h-4 mr-2" />
                  {data?.startDate ? (
                    format(new Date(data.startDate), 'MMM yyyy')
                  ) : (
                    <span>Pick start month</span>
                  )}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0 prevent-drawer-outside-click">
                <MonthPicker
                  maxDate={data?.endDate}
                  selectedMonth={data?.startDate}
                  onMonthSelect={(date) =>
                    setValue(`education.${index}.startDate`, date, {
                      shouldDirty: true,
                    })
                  }
                />
              </PopoverContent>
            </Popover>
          </div>
          <div className="flex flex-col w-1/2 space-y-2">
            <Label label="End" />
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant={'outline'}
                  disabled={readonly}
                  className={cn(
                    'justify-start text-left font-normal',
                    !data?.endDate && 'text-muted-foreground',
                  )}
                >
                  <CalendarIcon className="w-4 h-4 mr-2" />
                  {data?.endDate ? (
                    format(new Date(data.endDate), 'MMM yyyy')
                  ) : (
                    <span>Pick end month</span>
                  )}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0 prevent-drawer-outside-click">
                <MonthPicker
                  minDate={data?.startDate}
                  selectedMonth={data?.endDate}
                  onMonthSelect={(date) =>
                    setValue(`education.${index}.endDate`, date, {
                      shouldDirty: true,
                    })
                  }
                />
              </PopoverContent>
            </Popover>
          </div>
        </div>
      </div>
    </div>
  );
}
