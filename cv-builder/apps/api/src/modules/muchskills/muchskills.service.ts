import { HttpService } from '@nestjs/axios';
import {
  Injectable,
  UnauthorizedException,
  Logger,
  BadRequestException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { AxiosError } from 'axios';
import { escapeRegExp, omitBy, isNil } from 'lodash';
import { Model } from 'mongoose';
import { catchError, firstValueFrom } from 'rxjs';
import {
  CertificateFromMuchskills,
  Certification,
  CvProfileFilter,
  ExpertiseLevelEnum,
  MemberSource,
  MuchskillsMember,
  Skill,
  SkillFromMuchskills,
} from 'shared/types';

import { CVProfile } from './muchskills.schema';
import { AiService } from '../ai/ai.service';
import { Member, MemberDocument, MemberSkill } from '../members/member.schema';
import { MembersService } from '../members/members.service';
import {
  Organization,
  OrganizationDocument,
} from '../organization/organization.schema';
import { OrganizationService } from '../organization/organization.service';

@Injectable()
export class MuchskillsService {
  private readonly logger = new Logger(MuchskillsService.name);
  constructor(
    //Models
    @InjectModel(Member.name)
    private memberModel: Model<MemberDocument>,
    @InjectModel(Organization.name)
    private organizationModel: Model<OrganizationDocument>,
    //Services
    private readonly httpService: HttpService,
    private readonly membersService: MembersService,
    private readonly organizationService: OrganizationService,
    private readonly aiService: AiService,
  ) {}

  async syncProfiles(orgId: string) {
    const authToken =
      await this.organizationService.getMuchskillsAuthToken(orgId);

    if (!authToken) {
      throw new UnauthorizedException(
        'MuchSkills auth token not found. Please connect with MuchSkills first.',
      );
    }

    const { data } = await firstValueFrom(
      this.httpService
        .post<CVProfile[]>(
          `${process.env.MUCHSKILLS_API_URL || 'https://test.muchskills.com'}/api/v1/profile/search`,
          {},
          {
            params: { showValidatedSkills: 'true' },
            headers: {
              'Content-Type': 'application/json',
              Authorization: `Bearer ${authToken}`,
              'Ms-Private-Token': process.env.MS_API_PRIVATE_TOKEN,
            },
          },
        )
        .pipe(
          catchError(async (error: AxiosError) => {
            if (error.response?.status === 401) {
              await this.organizationModel.findOneAndUpdate(
                { _id: orgId },
                { $set: { 'muchskillsIntegration.connected': false } },
              );
            }
            this.logger.error(error.response?.data);
            throw new BadRequestException(
              'Your MS API token has expired or no longer valid',
            );
          }),
        ),
    );

    const preparedMsMembersData = data.map((member) => {
      const skillsMap = member.skillTypes
        .sort(
          (a, b) =>
            Number(a.type === 'Technical skills') -
            Number(b.type === 'Technical skills'),
        )
        .reduce((res, st) => {
          st.skills.forEach((skill) => {
            const getLevel = (level?: number) => {
              if (!level || level <= 0) return ExpertiseLevelEnum.noLevel;
              if (level < 4) return ExpertiseLevelEnum.beginner;
              if (level < 7) return ExpertiseLevelEnum.intermediate;
              return ExpertiseLevelEnum.expert;
            };

            return res.set(`${skill.name}${skill.level}`, {
              msId: skill.id,
              name: skill.name,
              level: getLevel(skill.level || skill.validatedLevel),
              isSoftware: st.type === 'Technical skills',
            });
          });

          return res;
        }, new Map<string, MemberSkill>());

      return {
        avatar: member.profile.image,
        name: member.profile.name,
        email: member.profile.email,
        location: member.profile.location,
        currentPosition: member.profile.title,
        socials: member.profile.links,
        source: MemberSource.muchskills,
        workExperience: member.profile.experience?.map((workRec) =>
          omitBy(
            {
              companyName: workRec.name,
              roleTitle: workRec.role,
              description: workRec.description,
              startDate: workRec.startTime,
              endDate: workRec.endTime,
              isCurrent: !workRec.endTime,
            },
            isNil,
          ),
        ),
        education: member.profile.education?.map((educationRec) =>
          omitBy(
            {
              schoolName: educationRec.name,
              degree: educationRec.degree,
              description: educationRec.description,
              startDate: educationRec.startTime,
              endDate: educationRec.endTime,
            },
            isNil,
          ),
        ),
        certifications: member.certifications.map((cert) =>
          omitBy(
            {
              msId: cert.id,
              name: cert.name,
              organization: cert.issuingOrganization,
            },
            isNil,
          ),
        ),
        skills: Array.from(skillsMap.values()),
      };
    });

    //Set ms members to org
    await this.organizationModel.findOneAndUpdate(
      { _id: orgId },
      { $set: { muchskillsMembers: preparedMsMembersData } },
    );

    //Update existed members
    const existedMembers = await this.memberModel.find({
      organization: orgId,
      email: { $in: preparedMsMembersData.map((m) => m.email) },
    });
    //TODO: put this update into the queue
    const updates = existedMembers.flatMap((existedMember) => {
      const msMemberData = preparedMsMembersData.find(
        (msMem) => msMem.email === existedMember.email,
      );

      if (!msMemberData) return [];

      const [firstName, ...lastNameParts] = msMemberData.name.split(' ');
      const lastName = lastNameParts.join(' ');

      return {
        updateOne: {
          filter: { organization: orgId, email: existedMember.email },
          update: {
            $set: { ...msMemberData, firstName, lastName },
          },
        },
      };
    });

    await this.memberModel.bulkWrite(updates);
    await this.organizationModel.findOneAndUpdate(
      { _id: orgId },
      { $set: { 'muchskillsIntegration.lastSync': new Date() } },
    );
  }

  async getProfiles(
    orgId: string,
    page: number,
    itemsPerPage: number,
    searchValue: string,
    filter: CvProfileFilter,
  ) {
    const orgMembers = await this.memberModel.find({ organization: orgId });
    const filteredMuchskillsMembers = await this.organizationModel
      .findOne({ _id: orgId })
      .then((org) => org?.muchskillsMembers || [])
      .then((members) => {
        if (searchValue) {
          const regex = new RegExp(escapeRegExp(searchValue), 'i');
          return members.filter((member) => regex.test(member.name));
        }

        return members;
      })
      .then((members) => {
        if (filter === CvProfileFilter.all) return members;

        const orgMembersEmails = new Set(orgMembers.map((mem) => mem.email));
        if (filter === CvProfileFilter.hasCvProf)
          return members.filter((m) => orgMembersEmails.has(m.email));
        if (filter === CvProfileFilter.hasNoCvProf)
          return members.filter((m) => !orgMembersEmails.has(m.email));

        return members;
      });

    const startIndex = (page - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;

    const membersToReturn = filteredMuchskillsMembers
      .sort((a, b) => a.name.localeCompare(b.name))
      .slice(startIndex, endIndex)
      .reduce((res: MuchskillsMember[], member) => {
        const orgMember = orgMembers.find(
          (orgMem) => orgMem.email === member.email,
        );

        res.push({
          email: member.email,
          name: member.name,
          image: member.avatar,
          title: member.currentPosition,
          location: member.location,
          competencesCount: member.skills.length + member.certifications.length,
          cvsCount: orgMember?.cvs.length || 0,
          profileExist: !!orgMember,
          localId: orgMember?._id,
        });

        return res;
      }, []);

    return {
      members: membersToReturn,
      total: filteredMuchskillsMembers.length,
    };
  }

  async createMemberFromMs(orgId: string, email: string) {
    const msMemberData = await this.organizationModel
      .findOne({ _id: orgId })
      .then((org) =>
        org?.muchskillsMembers?.find((member) => member.email === email),
      );

    if (!msMemberData) {
      throw new Error('Member is not found');
    }

    const [firstName, ...lastNameParts] = msMemberData.name.split(' ');
    const lastName = lastNameParts.join(' ');

    // Generate skills summary using AI service
    let skillsSummary = '';
    try {
      if (msMemberData.skills && msMemberData.skills.length > 0) {
        skillsSummary = await this.aiService.generateSkillsSummary(
          msMemberData.skills,
        );
      }
    } catch (error) {
      this.logger.warn('Failed to generate skills summary:', error);
      // Continue with member creation even if skills summary generation fails
    }

    await this.membersService.createMember(
      {
        firstName,
        lastName,
        avatar: msMemberData.avatar,
        email: msMemberData.email,
        location: msMemberData.location,
        currentPosition: msMemberData.currentPosition,
        socials: msMemberData.socials,
        source: msMemberData.source,
        workExperience: msMemberData.workExperience,
        education: msMemberData.education,
        certifications: msMemberData.certifications,
        skills: msMemberData.skills,
        skillsSummary,
      },
      orgId,
    );
  }

  async getSkills(
    orgId: string,
    searchValue: string,
    skillsToExclude?: string[],
  ): Promise<Skill[]> {
    const orgMsToken = await this.organizationModel
      .findById(orgId)
      .select('+muchskillsIntegration.token')
      .then((org) => org?.muchskillsIntegration?.token);

    const { data } = await firstValueFrom(
      this.httpService
        .post<SkillFromMuchskills[]>(
          `${process.env.MUCHSKILLS_API_URL || 'https://test.muchskills.com'}/api/v1/private/skills/search`,
          {
            searchValue,
            skillsToExclude,
            limit: 50,
          },
          {
            headers: {
              'Content-Type': 'application/json',
              Authorization: `Bearer ${process.env.MS_API_PRIVATE_TOKEN}`,
              'Muchskills-Integration-Api-Token': orgMsToken,
            },
          },
        )
        .pipe(
          catchError(async (error: AxiosError) => {
            this.logger.error(error.response?.data);
            throw 'An error happened!';
          }),
        ),
    );

    return data.map((skill) => ({
      msId: skill._id,
      name: skill.name,
      description: skill.description,
      image: skill.image,
      isSoftware: skill.type === 'software',
    }));
  }

  async getCertificates(
    searchValue: string,
    certificatesToExclude?: string[],
  ): Promise<Certification[]> {
    const { data } = await firstValueFrom(
      this.httpService
        .post<CertificateFromMuchskills[]>(
          `${process.env.MUCHSKILLS_API_URL || 'https://test.muchskills.com'}/api/v1/private/certifications/search`,
          {
            searchValue,
            certificatesToExclude,
            limit: 50,
          },
          {
            headers: {
              'Content-Type': 'application/json',
              Authorization: `Bearer ${process.env.MS_API_PRIVATE_TOKEN}`,
            },
          },
        )
        .pipe(
          catchError(async (error: AxiosError) => {
            this.logger.error(error.response?.data);
            throw 'An error happened!';
          }),
        ),
    );

    return data.map((certificate) => ({
      msId: certificate._id,
      name: certificate.name,
      organization: certificate.certificationOrganization.name,
      image: certificate.certificationOrganization.image,
      code: certificate.code,
    }));
  }
}
