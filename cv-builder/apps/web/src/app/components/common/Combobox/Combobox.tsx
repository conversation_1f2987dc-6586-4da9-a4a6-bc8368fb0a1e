'use client';

import { Check, ChevronsUpDown, X } from 'lucide-react';
import * as React from 'react';

import { Badge } from '../Badge';
import { Button } from '../Button';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '../Command';
import { Popover, PopoverContent, PopoverTrigger } from '../Popover';
import { ScrollArea } from '../ScrollArea';

import { cn } from '@/lib/utils';

export type ComboboxOption = {
  value: string;
  label: string;
};

type Mode = 'single' | 'multiple';

interface ComboboxProps {
  mode?: Mode;
  options: ComboboxOption[];
  selected: string | string[];
  className?: string;
  placeholder?: string;
  searchPlaceholder?: string;
  emptyMessage?: string;
  onChange?: (value: string | string[]) => void;
  onCreate?: (value: string) => void;
  disabled?: boolean;
  keepOpen?: boolean;
}

export function Combobox({
  options,
  selected,
  className,
  placeholder = 'Select items...',
  searchPlaceholder = 'Search items...',
  emptyMessage = 'No items found.',
  mode = 'single',
  onChange,
  onCreate,
  disabled = false,
  keepOpen = false,
}: ComboboxProps) {
  const [open, setOpen] = React.useState(false);
  const [query, setQuery] = React.useState<string>('');

  const selectedArray = Array.isArray(selected)
    ? selected
    : selected
      ? [selected]
      : [];
  const selectedOptions = selectedArray
    .map((value) => options.find((option) => option.value === value))
    .filter(Boolean) as ComboboxOption[];

  const handleSelect = (optionValue: string) => {
    if (!onChange) return;

    if (mode === 'multiple') {
      const newSelected = selectedArray.includes(optionValue)
        ? selectedArray.filter((item) => item !== optionValue)
        : [...selectedArray, optionValue];
      onChange(newSelected);
    } else {
      onChange(optionValue === selected ? '' : optionValue);
      if (!keepOpen) {
        setOpen(false);
      }
    }
  };

  const handleRemove = (optionValue: string) => {
    if (!onChange || mode !== 'multiple') return;

    const newSelected = selectedArray.filter((item) => item !== optionValue);
    onChange(newSelected);
  };

  const handleCreate = () => {
    if (!onCreate || !query.trim()) return;

    onCreate(query.trim());
    setQuery('');
  };

  const displayValue = React.useMemo(() => {
    if (selectedOptions.length === 0) return placeholder;

    if (mode === 'single') {
      return selectedOptions[0]?.label || placeholder;
    }

    // For multiple mode, show tags in trigger
    return null;
  }, [selectedOptions, placeholder, mode]);

  return (
    <div className={cn('block', className)}>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            type="button"
            variant="outline"
            role="combobox"
            aria-expanded={open}
            disabled={disabled}
            className={cn(
              'w-full justify-between text-left font-normal h-9 px-3 py-1 text-smalldoge-3 border-input hover:bg-background',
              mode === 'multiple' &&
                selectedOptions.length > 0 &&
                'h-auto min-h-9 py-2',
            )}
          >
            <div className="flex flex-wrap items-center flex-1 gap-1 mr-2">
              {mode === 'multiple' && selectedOptions.length > 0 ? (
                selectedOptions.map((option) => (
                  <Badge
                    key={option.value}
                    variant="secondary"
                    className="h-6 px-2 text-xs text-blue-800 bg-blue-100 hover:bg-blue-200"
                  >
                    {option.label}
                    <button
                      type="button"
                      className="ml-1 hover:bg-blue-300 rounded-sm p-0.5"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleRemove(option.value);
                      }}
                    >
                      <X className="w-3 h-3" />
                    </button>
                  </Badge>
                ))
              ) : (
                <span
                  className={cn(
                    'truncate max-w-48 sm:max-w-64',
                    selectedOptions.length === 0 && 'text-muted-foreground',
                  )}
                >
                  {displayValue}
                </span>
              )}
            </div>
            <ChevronsUpDown className="w-4 h-4 opacity-50 shrink-0" />
          </Button>
        </PopoverTrigger>
        <PopoverContent
          className="w-[--radix-popover-trigger-width] p-0 prevent-drawer-outside-click"
          align="start"
        >
          <Command
            filter={(value, search) => {
              if (value.toLowerCase().includes(search.toLowerCase())) return 1;
              return 0;
            }}
          >
            <CommandInput
              placeholder={searchPlaceholder}
              value={query}
              onValueChange={setQuery}
              onMouseDown={(e) => e.stopPropagation()}
              className="text-smalldoge-3"
            />
            <CommandEmpty
              onClick={handleCreate}
              onMouseDown={(e) => e.stopPropagation()}
              className={cn(
                'flex cursor-pointer items-center justify-center gap-1 py-6 text-center text-sm text-smalldoge-3',
                onCreate && query.trim() && 'hover:bg-accent',
              )}
            >
              {onCreate && query.trim() ? (
                <>
                  <span>Create: </span>
                  <span className="font-semibold truncate text-primary max-w-48">
                    {query}
                  </span>
                </>
              ) : (
                emptyMessage
              )}
            </CommandEmpty>
            <ScrollArea>
              <div className="max-h-80">
                <CommandGroup>
                  <CommandList>
                    {options.map((option) => (
                      <CommandItem
                        key={option.value}
                        value={option.label}
                        onSelect={() => handleSelect(option.value)}
                        onMouseDown={(e) => e.stopPropagation()}
                        className="cursor-pointer text-smalldoge-3"
                      >
                        <Check
                          className={cn(
                            'mr-2 h-4 w-4',
                            selectedArray.includes(option.value)
                              ? 'opacity-100'
                              : 'opacity-0',
                          )}
                        />
                        {option.label}
                      </CommandItem>
                    ))}
                  </CommandList>
                </CommandGroup>
              </div>
            </ScrollArea>
          </Command>
        </PopoverContent>
      </Popover>
    </div>
  );
}
