import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import mongoose, { Document, Types } from 'mongoose';

import { BaseModel } from '../../db';

export interface ICustomer extends BaseModel {
  name: string;
  organization: Types.ObjectId;
  _id: Types.ObjectId;
}

export type CustomerDocument = Customer & Document;

@Schema({ timestamps: true })
export class Customer {
  _id: Types.ObjectId;

  @Prop({
    type: String,
    required: true,
    minlength: [3, 'Customer name must be at least 3 characters'],
    validate: {
      validator: function (v: string) {
        return /^[a-zA-Z0-9\s]+$/.test(v);
      },
      message: 'Customer name can only contain letters, numbers, and spaces',
    },
  })
  name: string;

  @Prop({
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Organization',
    required: true,
  })
  organization: Types.ObjectId;
}

export const CustomerSchema = SchemaFactory.createForClass(Customer);

CustomerSchema.index(
  { name: 1, organization: 1 },
  {
    unique: true,
    collation: { locale: 'en', strength: 2 },
  },
);
