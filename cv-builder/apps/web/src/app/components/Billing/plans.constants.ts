import { Plan, StripePlanMapping } from 'shared/types';

export const PLANS: Record<StripePlanMapping, Plan> = {
  [StripePlanMapping.FREE]: {
    name: 'Free Plan',
    price: 'Free',
    description: 'Ideal for tryouts, small agencies',
    features: [
      { name: 'Profiles', value: 'Up to 20' },
      { name: 'Max CVs (Watermarked)', value: '20 CVs' },
      { name: 'Users (Only admin roles)', value: '5' },
      { name: 'AI generated CVs', value: 'Yes (limited)' },
      { name: 'Basic CV database', value: 'Yes' },
      { name: 'Skills integration to', value: 'MuchSkills' },
      {
        name: 'Skills based search & profile matching',
        value: 'Yes (limited)',
      },
    ],
    tier: StripePlanMapping.FREE,
  },
  [StripePlanMapping.PRO]: {
    name: 'Pro Plan',
    price: 'Not available',
    description: 'Ideal for small recruiting teams or boutiques',
    features: [
      { name: 'Profiles', value: 'Up to 100' },
      { name: 'Max CVs', value: '100 CVs' },
      { name: 'Users (Full access control)', value: '5' },
      { name: 'AI generated CVs', value: 'Yes' },
      { name: 'Core CV database', value: 'Yes' },
      { name: 'Skills integration to', value: 'MuchSkills' },
      { name: 'Custom integrations', value: 'None' },
      { name: 'Skills based search & profile matching', value: 'Yes(limited)' },
    ],
    tier: StripePlanMapping.PRO,
  },
  [StripePlanMapping.BUSINESS]: {
    name: 'Business Plan',
    price: 'Not available',
    description: 'Medium agencies, internal recruitment teams',
    features: [
      { name: 'Everything in Pro +', value: '' },
      { name: 'Profiles', value: '500' },
      { name: 'CVs', value: '100 CVs/month' },
      { name: 'Users (Full access control)', value: '20' },
      { name: 'AI generated CVs', value: 'Yes' },
      { name: 'Custom integrations', value: 'Possible' },
      {
        name: 'Skills based search & profile matching',
        value: 'Yes',
      },
    ],
    tier: StripePlanMapping.BUSINESS,
  },
  [StripePlanMapping.ENTERPRISE]: {
    name: 'Enterprise',
    price: 'Custom pricing',
    description: 'Global complex orgs, needing full control',
    features: [
      { name: 'Everything in Business +', value: '' },
      { name: 'Profiles', value: 'Unlimited' },
      { name: 'CVs', value: 'Unlimited' },
      { name: 'Users', value: 'Unlimited' },
      {
        name: 'Skills based search & profile matching',
        value: 'Yes',
      },
      { name: 'Dedicated support and onboarding', value: 'Yes' },
      { name: 'API + Integrations', value: 'Yes' },
      { name: 'SSO, SAML and SCIM', value: 'Yes' },
      { name: 'SLAs', value: 'Yes' },
    ],
    tier: StripePlanMapping.ENTERPRISE,
  },
};
