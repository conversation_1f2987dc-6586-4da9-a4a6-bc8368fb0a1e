import { zodResolver } from '@hookform/resolvers/zod';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { AxiosError } from 'axios';
import { isNaN } from 'lodash';
import { useCallback, useEffect } from 'react';
import { DateRange } from 'react-day-picker';
import { useForm, Controller } from 'react-hook-form';
import ReactQuill from 'react-quill';
import {
  CostRate,
  Currency,
  currencyData,
  Cv,
  CvStatus,
  TimeRange,
  timeRangeData,
  OrganizationFullInfo,
} from 'shared/types';
import { toast } from 'sonner';
import { z } from 'zod';

import {
  createCvRequest,
  updateCvRequest,
  getCustomersRequest,
  createCustomerRequest,
} from '../../helpers/requests';
import {
  ButtonPrimary,
  Checkbox,
  CustomerCombobox,
  DateRangePicker,
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuTrigger,
  Input,
  InputAutoWidth,
} from '../common';
import { RowWrapper } from '../MemberProfileForm/components';

const defaultCostRateData: CostRate = {
  currency: Currency.eur,
  amount: 0,
  timeRange: TimeRange.month,
};

const costRateSchema = z.object({
  currency: z.nativeEnum(Currency),
  amount: z.number(),
  timeRange: z.nativeEnum(TimeRange),
});

const cvSettingsSchema = z
  .object({
    title: z.string().min(1, 'Title is required'),
    maxPages: z.number(),
    costRate: costRateSchema,
    customer: z.string().optional(),
    link: z.string().url('Must be a valid URL').optional().or(z.literal('')),
    contractRange: z
      .object({ from: z.date().optional(), to: z.date().optional() })
      .optional(),
    autoRenewal: z.boolean().optional(),
    leastExperience: z.number(),
    maxExperience: z.number(),
    description: z.string().optional(),
  })
  .refine(
    (data) => {
      if (
        data.leastExperience !== undefined &&
        data.maxExperience !== undefined
      ) {
        return data.leastExperience <= data.maxExperience;
      }
      return true;
    },
    {
      message: 'Minimum experience cannot be greater than maximum experience',
      path: ['leastExperience'],
    },
  );

type CvSettingsFormValues = z.infer<typeof cvSettingsSchema>;

const defaultValues: CvSettingsFormValues = {
  title: '',
  maxPages: 2,
  costRate: defaultCostRateData,
  customer: undefined,
  link: '',
  contractRange: undefined,
  autoRenewal: false,
  leastExperience: 0,
  maxExperience: 0,
  description: '',
};

interface CvSettingsFormProps {
  memberId: string;
  cv?: Cv;
  onCanceled?: () => void;
  onSubmitted?: (cvId?: string) => void;
}

export function CvSettingsForm({
  memberId,
  cv,
  onCanceled,
  onSubmitted,
}: CvSettingsFormProps) {
  const queryClient = useQueryClient();

  const {
    register,
    handleSubmit,
    control,
    formState: { errors, isSubmitting },
    reset,
    watch,
    setValue,
  } = useForm<CvSettingsFormValues>({
    resolver: zodResolver(cvSettingsSchema),
    defaultValues,
  });
  const costRate = watch('costRate');

  const { data: customers } = useQuery({
    queryKey: ['customers'],
    queryFn: getCustomersRequest,
  });

  const handleResetForm = useCallback(() => {
    if (cv) {
      reset({
        title: cv.preferences.title || '',
        maxPages: cv.preferences.maxPages || 0,
        costRate: cv.preferences.costRate || defaultCostRateData,
        customer: cv.preferences.customer?._id,
        link: cv.preferences.link || '',
        contractRange: {
          from: cv.preferences.contractStart
            ? new Date(cv.preferences.contractStart)
            : undefined,
          to: cv.preferences.contractEnd
            ? new Date(cv.preferences.contractEnd)
            : undefined,
        },
        autoRenewal: cv.preferences.autoRenewal || false,
        leastExperience: cv.preferences.leastExperience || 0,
        maxExperience: cv.preferences.maxExperience || 0,
        description: cv.preferences.description || '',
      });
    } else {
      reset(defaultValues);
    }
  }, [cv, reset]);

  useEffect(() => handleResetForm(), [handleResetForm]);

  const { mutate: createCv } = useMutation({
    mutationFn: (data: CvSettingsFormValues) => {
      const dto = {
        title: data.title,
        maxPages: data.maxPages,
        costRate: data.costRate,
        customer: data.customer,
        link: data.link,
        contractStart: data.contractRange?.from,
        contractEnd: data.contractRange?.to,
        autoRenewal: data.autoRenewal,
        leastExperience: data.leastExperience,
        maxExperience: data.maxExperience,
        description: data.description,
      };
      return createCvRequest(memberId, dto);
    },
    onSuccess: (newCv) => {
      if (onSubmitted) {
        onSubmitted(newCv._id);
      }

      handleResetForm();
      queryClient.invalidateQueries({
        queryKey: ['memberCvs', { memberId }],
      });
      queryClient.invalidateQueries({
        queryKey: ['draftCvs'],
      });

      // Update organization full info to increment totalCvs count
      queryClient.setQueryData(
        ['organizationFullInfo'],
        (oldData: OrganizationFullInfo | undefined) => {
          if (!oldData) return oldData;

          return {
            ...oldData,
            totalCvs: oldData.totalCvs + 1,
          };
        },
      );

      // Invalidate paginated members data to refetch updated CV data
      queryClient.invalidateQueries({
        queryKey: ['paginatedMembers'],
      });

      toast.message('CV is created!');
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });

  const { mutate: updateCv } = useMutation({
    mutationFn: ({
      cvId,
      data,
    }: {
      cvId: string;
      data: CvSettingsFormValues;
    }) => {
      const dto = {
        preferences: {
          title: data.title,
          maxPages: data.maxPages,
          costRate: data.costRate,
          customer: data.customer,
          link: data.link,
          contractStart: data.contractRange?.from,
          contractEnd: data.contractRange?.to,
          autoRenewal: data.autoRenewal,
          leastExperience: data.leastExperience,
          maxExperience: data.maxExperience,
          description: data.description,
        },
      };
      return updateCvRequest(cvId, dto);
    },
    onSuccess: (_, variables) => {
      if (onSubmitted) {
        onSubmitted();
      }

      queryClient.invalidateQueries({ queryKey: ['memberCvs', { memberId }] });
      queryClient.invalidateQueries({
        queryKey: ['paginatedMembers'],
      });
      queryClient.invalidateQueries({
        queryKey: ['cv', { cvId: variables.cvId }],
      });
      // If updating a draft CV, also invalidate draftCvs query
      if (cv?.status === CvStatus.draft) {
        queryClient.invalidateQueries({
          queryKey: ['draftCvs'],
        });
      }

      toast.message('CV is updated!');
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });

  const { mutate: createCustomer, isPending: isCreatingCustomer } = useMutation(
    {
      mutationFn: createCustomerRequest,
      onSuccess: (newCustomer) => {
        queryClient.invalidateQueries({ queryKey: ['customers'] });
        setValue('customer', newCustomer._id);
        toast.message('Customer created successfully!');
      },
      onError: (error: AxiosError) => {
        const errorData = error as {
          message: string;
          errors?: string[];
        };

        if (errorData?.errors && errorData.errors.length > 0) {
          toast.error(errorData.message || 'Validation failed', {
            description: errorData.errors.join('\n'),
          });
        } else {
          toast.error('Failed to create customer');
        }
      },
    },
  );

  const handleCreateCustomer = async (name: string) => {
    createCustomer({ name: name.trim() });
  };

  return (
    <form
      onSubmit={handleSubmit(
        (data) => {
          if (cv) {
            updateCv({ cvId: cv._id, data });
          } else {
            createCv(data);
          }
        },
        () => toast.error('Please fix invalid data before submitting.'),
      )}
      className="flex flex-col space-y-5"
    >
      <span className="font-bold text-smalldoge-1">
        Let's set some basic requirements for this CV
      </span>
      <div className="flex flex-col p-4 space-y-2">
        <RowWrapper label="CV Alias" required={true}>
          <Input
            {...register('title')}
            placeholder="Ex: Sr. Product Manager"
            className="text-smalldoge-3"
            wrapperClassName="flex-grow w-full"
            error={errors.title?.message}
          />
        </RowWrapper>
        <RowWrapper label="Max Pages">
          <Controller
            name="maxPages"
            control={control}
            render={({ field }) => (
              <InputAutoWidth
                inputProps={{
                  maxLength: 2,
                  value: field.value || '',
                  placeholder: '0',
                  onChange: (e) => {
                    if (e.target.value === '') {
                      field.onChange(0);
                      return;
                    }
                    if (isNaN(+e.target.value)) return;
                    if (+e.target.value < 0) return;

                    field.onChange(+e.target.value);
                  },
                  onFocus: (e) => {
                    e.target.select();
                  },
                }}
              />
            )}
          />
        </RowWrapper>
        <RowWrapper label="Contract Rate">
          <div className="flex items-center flex-grow w-full h-9">
            <div className="flex items-center space-x-2">
              <Controller
                name="costRate.amount"
                control={control}
                render={({ field }) => (
                  <InputAutoWidth
                    classNames="pl-4 pr-2"
                    inputProps={{
                      maxLength: 6,
                      value: field.value || '',
                      placeholder: '0',
                      prefixElement: (
                        <span className="text-smalldoge-3 text-msGray-2 font-bold absolute transform leading-5 -translate-y-2.5 translate-x-1.5 top-[50%]">
                          {currencyData[costRate.currency].sign}
                        </span>
                      ),
                      onChange: (e) => {
                        if (e.target.value === '') {
                          field.onChange(0);
                          return;
                        }
                        if (isNaN(+e.target.value)) return;
                        if (+e.target.value < 0) return;

                        field.onChange(+e.target.value);
                      },
                      onFocus: (e) => {
                        e.target.select();
                      },
                    }}
                  />
                )}
              />
              <span>/</span>
              <Controller
                name="costRate.timeRange"
                control={control}
                render={({ field }) => (
                  <DropdownMenu modal={false}>
                    <DropdownMenuTrigger asChild>
                      <div className="flex items-center h-6 rounded-sm cursor-pointer bg-msGray-6">
                        <span className="px-2 font-bold text-smalldoge-3 text-msGray-2">
                          {timeRangeData[field.value].name.toUpperCase()}
                        </span>
                      </div>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent
                      collisionPadding={10}
                      align="start"
                      className="overflow-auto max-h-48 prevent-drawer-outside-click"
                    >
                      <DropdownMenuGroup>
                        {Object.values(TimeRange).map((range) => (
                          <DropdownMenuItem
                            key={range}
                            onClick={() => field.onChange(range)}
                          >
                            <span className="font-bold truncate text-smalldoge-3">
                              {timeRangeData[range].name.toUpperCase()}
                            </span>
                          </DropdownMenuItem>
                        ))}
                      </DropdownMenuGroup>
                    </DropdownMenuContent>
                  </DropdownMenu>
                )}
              />
            </div>
            <Controller
              name="costRate.currency"
              control={control}
              render={({ field }) => (
                <DropdownMenu modal={false}>
                  <DropdownMenuTrigger asChild>
                    <div className="flex items-center h-6 ml-auto rounded-sm cursor-pointer bg-msGray-6">
                      <span className="px-2 font-bold text-smalldoge-3 text-msGray-2">
                        {currencyData[field.value].name}
                      </span>
                    </div>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent
                    collisionPadding={10}
                    align="start"
                    className="overflow-auto max-h-48 prevent-drawer-outside-click"
                  >
                    <DropdownMenuGroup>
                      {Object.values(Currency).map((curr) => (
                        <DropdownMenuItem
                          key={curr}
                          onClick={() => field.onChange(curr)}
                        >
                          <span className="font-bold truncate text-smalldoge-3">
                            {currencyData[curr].name}
                          </span>
                        </DropdownMenuItem>
                      ))}
                    </DropdownMenuGroup>
                  </DropdownMenuContent>
                </DropdownMenu>
              )}
            />
          </div>
        </RowWrapper>

        <RowWrapper label="Client">
          <Controller
            name="customer"
            control={control}
            render={({ field }) => (
              <CustomerCombobox
                customers={customers || []}
                selectedCustomer={field.value}
                onCustomerChange={field.onChange}
                onCreateCustomer={handleCreateCustomer}
                isCreatingCustomer={isCreatingCustomer}
                className="w-full"
              />
            )}
          />
        </RowWrapper>
        <RowWrapper label="RFQ or Job Ad">
          <Input
            {...register('link')}
            placeholder="URL goes here"
            className="text-smalldoge-3"
            wrapperClassName="flex-grow w-full"
            error={errors.link?.message}
          />
        </RowWrapper>
        <RowWrapper label="Contract Timeline">
          <div className="flex flex-col w-full space-y-2">
            <Controller
              name="contractRange"
              control={control}
              render={({ field }) => (
                <DateRangePicker
                  date={field.value as DateRange | undefined}
                  onDateChange={field.onChange}
                />
              )}
            />
            <div className="flex items-center space-x-2">
              <Controller
                name="autoRenewal"
                control={control}
                render={({ field }) => (
                  <Checkbox
                    id="autoRenew"
                    checked={field.value}
                    onCheckedChange={field.onChange}
                  />
                )}
              />
              <label
                htmlFor="autoRenew"
                className="text-smalldoge-5 text-msGray-2"
              >
                Contract auto-renews
              </label>
            </div>
          </div>
        </RowWrapper>
        <RowWrapper label="Experience Required">
          <div className="flex flex-col space-y-1">
            <div className="flex items-center space-x-2 h-9">
              <Controller
                name="leastExperience"
                control={control}
                render={({ field }) => (
                  <InputAutoWidth
                    inputProps={{
                      maxLength: 2,
                      value: field.value || '',
                      placeholder: '0',
                      onChange: (e) => {
                        if (e.target.value === '') {
                          field.onChange(0);
                          return;
                        }
                        if (isNaN(+e.target.value)) return;
                        if (+e.target.value < 0) return;

                        field.onChange(+e.target.value);
                      },
                    }}
                  />
                )}
              />
              <span>-</span>
              <Controller
                name="maxExperience"
                control={control}
                render={({ field }) => (
                  <InputAutoWidth
                    inputProps={{
                      maxLength: 2,
                      value: field.value || '',
                      placeholder: '0',
                      onChange: (e) => {
                        if (e.target.value === '') {
                          field.onChange(0);
                          return;
                        }
                        if (isNaN(+e.target.value)) return;
                        if (+e.target.value < 0) return;

                        field.onChange(+e.target.value);
                      },
                    }}
                  />
                )}
              />
              <span className="font-bold text-smalldoge-3 text-msGray-2">
                years
              </span>
            </div>
            {errors.leastExperience && (
              <span className="text-smalldoge-4 text-msRed-1">
                {errors.leastExperience.message}
              </span>
            )}
          </div>
        </RowWrapper>
        <RowWrapper label="Description">
          <div className="w-full min-h">
            <Controller
              name="description"
              control={control}
              render={({ field }) => (
                <ReactQuill
                  modules={{
                    toolbar: [
                      ['bold', 'italic', 'underline'],
                      [{ list: 'ordered' }, { list: 'bullet' }],
                    ],
                  }}
                  theme="snow"
                  value={field.value || ''}
                  onChange={(value, delta, source) => {
                    if (source === 'user') {
                      field.onChange(value);
                    }
                  }}
                />
              )}
            />
          </div>
        </RowWrapper>
        <div className="flex justify-end space-x-2 pt-7">
          <ButtonPrimary
            variant="white"
            type="button"
            onClick={() => {
              handleResetForm();

              if (onCanceled) {
                onCanceled();
              }
            }}
          >
            Cancel
          </ButtonPrimary>
          <ButtonPrimary type="submit" disabled={isSubmitting}>
            {cv ? 'Save Changes' : 'Create'}
          </ButtonPrimary>
        </div>
      </div>
    </form>
  );
}
