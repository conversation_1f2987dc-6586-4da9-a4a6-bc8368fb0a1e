{"updatedSections": {"sections": {"workHistory": {"id": "689f2013d50e9ed4127d5ed3", "title": "Work Experience", "active": true, "order": 3, "data": [{"id": "689f2013d50e9ed4127d5ecf", "title": "Middle Software Engineer", "companyName": "Archive.com", "description": "<p><strong>Customer:</strong> US-based product company | <strong>Project:</strong> AI influencer marketing platform | <strong>Team:</strong> 7 FS Devs, 3 Designers, 1 TL | <strong>Role:</strong> Full Stack Ruby on Rails & React</p><ul><li>Delivered React UI components aligned with Figma, reducing UI defects by 35% and accelerating feature release cycle by 20%.</li><li>Designed and shipped GraphQL endpoints in Rails (graphql-ruby), cutting over-fetching and improving client response times by ~25%.</li><li>Built end-to-end tests in Cypress and integrated into CI, increasing regression coverage to 85% and preventing prod rollbacks.</li><li>Partnered with designers and PM in async reviews, driving fast feedback via MR discussions and small, atomic commits.</li><li>Led story estimation and defect triage, helping the team meet sprint goals 95%+ on-time.</li></ul><p><em>Stack:</em> Ruby on Rails, GraphQL, React, Cypress, PostgreSQL, Redis, Sidekiq, Docker, Git</p>", "startDate": "2025-05-17T11:54:59.943Z", "endDate": "2025-07-16T11:54:59.943Z", "location": "", "isCurrentJob": false}, {"id": "689f2013d50e9ed4127d5ed0", "title": "Junior Strong Software Engineer", "companyName": "Coax Software", "description": "<p><strong>Customer:</strong> UK logistics | <strong>Projects:</strong> Job Management App for Drivers; Bus/Carlift apps | <strong>Role:</strong> Ruby on Rails API Developer</p><ul><li>Implemented MVP Rails APIs (JWT auth, pagination, rate limiting), enabling first driver onboarding within 6 weeks.</li><li>Integrated third‑party KYC/ID verification, reducing manual checks by 60% and improving sign-up completion by 30%.</li><li>Collaborated with mobile teams to debug device-specific issues, lowering crash rate by 40% across target devices.</li><li>Owned API observability (logging, error handling), cutting average incident resolution time from days to hours.</li><li>Drove planning/estimation with concise user stories and acceptance criteria, improving delivery predictability.</li></ul><p><em>Stack:</em> Ruby on Rails, PostgreSQL, Redis, Sidekiq, REST, JWT, Docker, Git, Postman</p>", "startDate": "2024-11-18T12:54:59.943Z", "endDate": "2025-05-17T11:54:59.943Z", "location": "", "isCurrentJob": false}, {"id": "689f2013d50e9ed4127d5ed1", "title": "Junior Software Engineer", "companyName": "SoftServe", "description": "<p><strong>Customer:</strong> US healthcare | <strong>Project:</strong> Canadian user registration | <strong>Role:</strong> Developer</p><ul><li>Shipped a new GraphQL-based registration flow, decreasing time-to-signup by ~20% and improving conversion.</li><li>Contributed React/UI fixes and accessibility improvements, reducing support tickets related to onboarding by 25%.</li><li>Drove cross-team collaboration via async docs and MR reviews; authored clear Confluence runbooks and ADRs.</li><li>Performed code reviews and knowledge-sharing sessions, accelerating onboarding for 3 newcomers.</li><li>Supported demos and release readiness with test data, feature flags, and rollback plans.</li></ul><p><em>Stack:</em> GraphQL, Ruby on Rails, React, PostgreSQL, GraphiQL, Git</p>", "startDate": "2024-03-18T12:54:59.943Z", "endDate": "2024-08-15T11:54:59.943Z", "location": "", "isCurrentJob": false}, {"id": "689f2013d50e9ed4127d5ed2", "title": "Trainee / Junior Software Engineer", "companyName": "SoftServe", "description": "<p><strong>Customer:</strong> US healthcare | <strong>Project:</strong> Messaging system | <strong>Role:</strong> Developer</p><ul><li>Delivered backend and UI features with small MRs, increasing review throughput and reducing merge conflicts.</li><li>Wrote RSpec/Capybara unit and integration tests, raising coverage to 80%+ and enabling safer refactors.</li><li>Improved service integrations and background jobs (Sidekiq/Redis), cutting message latency by ~30%.</li><li>Participated in sprint planning/grooming; provided accurate estimates and contributed to team velocity growth.</li><li>Supported QA and release processes via Jenkins pipelines and RuboCop/linters, ensuring consistent CI quality.</li></ul><p><em>Stack:</em> Ruby on Rails, MySQL, REST, React, Sidekiq, Redis, Docker, Jenkins, RuboCop, Git, Jira, Confluence</p>", "startDate": "2023-03-19T12:54:59.943Z", "endDate": "2023-12-19T12:54:59.943Z", "location": "", "isCurrentJob": false}]}}, "customSections": []}, "changesSummary": "I tailored your Work Experience for GitLab by emphasizing measurable impact, async collaboration, small MRs/code review culture, CI/testing rigor, and DevOps-friendly metrics. Content now highlights GraphQL/Rails/React, Cypress/RSepc coverage, and reliability improvements relevant to GitLab’s remote-first and MR-driven workflows. Would you like to keep these edits?"}