import {
  Body,
  Controller,
  Post,
  Get,
  Query,
  UseGuards,
  Param,
  UploadedFile,
  UseInterceptors,
  ParseFilePipe,
  MaxFileSizeValidator,
  FileTypeValidator,
  Delete,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { Express } from 'express';
import { MAX_AVATAR_SIZE } from 'shared/constants';
import { AuthUser } from 'src/modules/global/decorators/user.decorator';
import { AuthUserDto } from 'src/modules/global/dto/auth-user.dto';

import { CreateMemberDto, GetMembersDto } from './dto';
import { UpdateMemberDto } from './dto/update-member.dto';
import { Member } from './member.schema';
import { MembersService } from './members.service';
import { AuthGuard } from '../auth/guards/auth.guard';
import { Action } from '../casl/casl.types';
import { CheckAbilities } from '../casl/decorators/check-policies.decorator';
import { PoliciesGuard } from '../casl/guards/policies.guard';
import { CheckPlanLimit } from '../stripe/decorators/check-plan-limit.decorator';
import { LimitType } from '../stripe/guards/plan-limit.guard';
import { PlanLimitGuard } from '../stripe/guards/plan-limit.guard';

@UseGuards(AuthGuard, PoliciesGuard)
@Controller('members')
export class MembersController {
  constructor(private membersService: MembersService) {}

  @UseGuards(PlanLimitGuard)
  @CheckPlanLimit(LimitType.PROFILES)
  @Post('create')
  @CheckAbilities({ action: Action.Create, subject: Member })
  async createMember(
    @AuthUser() user: AuthUserDto,
    @Body() dto: CreateMemberDto,
  ) {
    return await this.membersService.createMember(dto, user.organization._id);
  }

  @Post('update/:memberId')
  @CheckAbilities({ action: Action.Update, subject: Member })
  async updateMember(
    @AuthUser() user: AuthUserDto,
    @Param('memberId') memberId: string,
    @Body() dto: UpdateMemberDto,
  ) {
    return await this.membersService.updateMember(memberId, dto, user);
  }

  @Get('/:memberId')
  @CheckAbilities({ action: Action.Read, subject: Member })
  async getMemberById(
    @AuthUser() user: AuthUserDto,
    @Param('memberId') memberId: string,
  ) {
    return await this.membersService.getMemberById(memberId, user);
  }

  @Get()
  @CheckAbilities({ action: Action.Read, subject: Member })
  async getOrgMembers(
    @AuthUser() user: AuthUserDto,
    @Query()
    { source, search, page, itemsPerPage, sortBy, sortOrder }: GetMembersDto,
  ) {
    const { members, totalMembers } = await this.membersService.getOrgMembers(
      user.organization._id,
      {
        source,
        search,
        page,
        itemsPerPage,
        sortBy,
        sortOrder,
      },
    );

    return { members, totalMembers };
  }

  @Post('avatar-update/:memberId')
  @UseInterceptors(FileInterceptor('avatar'))
  @CheckAbilities({ action: Action.Update, subject: Member })
  async updateMemberAvatar(
    @AuthUser() user: AuthUserDto,
    @Param('memberId') memberId: string,
    @Body() { removeAvatar }: { removeAvatar: string },
    @UploadedFile(
      new ParseFilePipe({
        validators: [
          new MaxFileSizeValidator({ maxSize: MAX_AVATAR_SIZE }),
          new FileTypeValidator({ fileType: 'image' }),
        ],
        fileIsRequired: false,
      }),
    )
    avatar?: Express.Multer.File,
  ) {
    if (removeAvatar) {
      return await this.membersService.removeMemberAvatar(memberId, user);
    }

    return await this.membersService.uploadMemberAvatar(memberId, user, avatar);
  }

  @Delete('/:memberId')
  @CheckAbilities({ action: Action.Delete, subject: Member })
  async deleteMember(
    @AuthUser() user: AuthUserDto,
    @Param('memberId') memberId: string,
  ) {
    return await this.membersService.deleteMember(memberId, user);
  }
}
