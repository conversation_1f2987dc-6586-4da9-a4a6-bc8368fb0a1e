import {
  Is<PERSON>ptional,
  IsString,
  IsArray,
  <PERSON>Length,
  ArrayMaxSize,
} from 'class-validator';

export class UpdateOrganizationDto {
  @IsOptional()
  @IsString()
  @MaxLength(100)
  name?: string;

  @IsOptional()
  @IsString()
  @MaxLength(500)
  description?: string;

  @IsOptional()
  @IsString()
  @MaxLength(100)
  website?: string;

  @IsOptional()
  @IsString()
  timezone?: string;

  @IsOptional()
  @IsArray()
  @ArrayMaxSize(5)
  @IsString({ each: true })
  locations?: string[];

  @IsOptional()
  @IsString()
  photo?: string;
}
