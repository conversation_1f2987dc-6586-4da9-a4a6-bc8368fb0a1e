import { clsx, type ClassValue } from 'clsx';
import { extendTailwindMerge } from 'tailwind-merge';

export function cn(...inputs: ClassValue[]) {
  const twMerge = extendTailwindMerge({
    extend: {
      classGroups: {
        'font-size': [
          {
            text: [
              'bigdoge-1',
              'bigdoge-2',
              'bigdoge-3',
              'bigdoge-4',
              'bigdoge-5',
              'bigdoge-6',
              'bigdoge-7',
              'smalldoge-1',
              'smalldoge-2',
              'smalldoge-3',
              'smalldoge-4',
              'smalldoge-5',
            ],
          },
        ],
      },
    },
  });

  return twMerge(clsx(inputs));
}
