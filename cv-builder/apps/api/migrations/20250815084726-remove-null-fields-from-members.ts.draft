import { Db } from 'mongodb';

export async function up(db: Db) {
  console.log('Starting migration: removing null fields from members...');

  // Find all members that have null fields
  const membersWithNulls = await db
    .collection('members')
    .find({
      $or: [
        { avatar: null },
        { lastName: null },
        { email: null },
        { location: null },
        { telephone: null },
        { currentPosition: null },
        { currentLevel: null },
        { yearsOfExperience: null },
        { type: null },
        { costRate: null },
        { costToCompany: null },
        { sourceId: null },
        { competencesAmount: null },
        // Check for null fields in nested arrays
        { 'workExperience.roleTitle': null },
        { 'workExperience.description': null },
        { 'workExperience.endDate': null },
        { 'workExperience.isCurrent': null },
        { 'education.degree': null },
        { 'education.description': null },
        { 'education.startDate': null },
        { 'education.endDate': null },
      ],
    })
    .toArray();

  console.log(`Found ${membersWithNulls.length} members with null fields`);

  if (membersWithNulls.length > 0) {
    // Remove null fields from top-level member fields
    const topLevelResult = await db
      .collection('members')
      .updateMany(
        {},
        {
          $unset: {
            avatar: '',
            lastName: '',
            email: '',
            location: '',
            telephone: '',
            currentPosition: '',
            currentLevel: '',
            yearsOfExperience: '',
            type: '',
            costRate: '',
            costToCompany: '',
            sourceId: '',
            competencesAmount: '',
          },
        },
        {
          // Only unset fields that are actually null
          arrayFilters: [],
        }
      );

    // Process each member individually to clean nested arrays
    let processedMembers = 0;
    for (const member of membersWithNulls) {
      const updates: any = {};

      // Clean workExperience array
      if (member.workExperience && Array.isArray(member.workExperience)) {
        const cleanedWorkExperience = member.workExperience.map((work: any) => {
          const cleanedWork: any = {};
          Object.keys(work).forEach(key => {
            if (work[key] !== null) {
              cleanedWork[key] = work[key];
            }
          });
          return cleanedWork;
        });
        updates.workExperience = cleanedWorkExperience;
      }

      // Clean education array
      if (member.education && Array.isArray(member.education)) {
        const cleanedEducation = member.education.map((edu: any) => {
          const cleanedEdu: any = {};
          Object.keys(edu).forEach(key => {
            if (edu[key] !== null) {
              cleanedEdu[key] = edu[key];
            }
          });
          return cleanedEdu;
        });
        updates.education = cleanedEducation;
      }

      // Clean certifications array
      if (member.certifications && Array.isArray(member.certifications)) {
        const cleanedCertifications = member.certifications.map((cert: any) => {
          const cleanedCert: any = {};
          Object.keys(cert).forEach(key => {
            if (cert[key] !== null) {
              cleanedCert[key] = cert[key];
            }
          });
          return cleanedCert;
        });
        updates.certifications = cleanedCertifications;
      }

      // Update member if there are changes
      if (Object.keys(updates).length > 0) {
        await db
          .collection('members')
          .updateOne({ _id: member._id }, { $set: updates });
        processedMembers++;
      }
    }

    console.log(`Updated ${processedMembers} members - removed null fields from nested arrays`);
    
    // Now remove top-level null fields using $unset with conditions
    const fieldsToCheck = [
      'avatar', 'lastName', 'email', 'location', 'telephone', 
      'currentPosition', 'currentLevel', 'yearsOfExperience', 
      'type', 'costRate', 'costToCompany', 'sourceId', 'competencesAmount'
    ];

    for (const field of fieldsToCheck) {
      const result = await db
        .collection('members')
        .updateMany(
          { [field]: null },
          { $unset: { [field]: '' } }
        );
      
      if (result.modifiedCount > 0) {
        console.log(`Removed null ${field} from ${result.modifiedCount} members`);
      }
    }
  }

  console.log('Migration completed successfully');
}

export async function down(db: Db) {
  console.log('Rolling back migration: this migration cannot be safely rolled back');
  console.log('Null fields were removed and cannot be restored without data loss');
  console.log('If rollback is needed, restore from database backup');
}
