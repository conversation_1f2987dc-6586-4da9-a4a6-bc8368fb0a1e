import { useState, useEffect } from 'react';
import { ApiStripeProduct, Plan, StripePlanMapping } from 'shared/types';

import { PLANS } from '@/components/Billing/plans.constants';
import { formatPrice } from '@/helpers/billing.utils';

interface UseProcessedPlansProps {
  stripeProducts: ApiStripeProduct[] | undefined;
  billingInterval: 'month' | 'year';
  selectedCurrency: 'usd' | 'eur';
}

export function useProcessedPlans({
  stripeProducts,
  billingInterval,
  selectedCurrency,
}: UseProcessedPlansProps): { processedPlans: Plan[] } {
  const [processedPlans, setProcessedPlans] = useState<Plan[]>([]);

  useEffect(() => {
    if (stripeProducts) {
      const transformedPlans: Plan[] = stripeProducts
        .filter((product) => product.active)
        .map((product) =>
          transformStripeProductToPlan(
            product,
            billingInterval,
            selectedCurrency,
          ),
        )
        .filter((plan): plan is Plan => plan !== null);

      const finalPlans = combineAndSortPlans(transformedPlans);
      setProcessedPlans(finalPlans);
    } else {
      setProcessedPlans([]);
    }
  }, [stripeProducts, billingInterval, selectedCurrency]);

  return { processedPlans };
}

function transformStripeProductToPlan(
  product: ApiStripeProduct,
  billingInterval: 'month' | 'year',
  selectedCurrency: 'usd' | 'eur',
): Plan | null {
  const productMetadataId = product.metadata.id as StripePlanMapping;
  // const planDescription = product.description || 'No description available.';
  let planPriceId: string | undefined = undefined;

  const planDefaults = PLANS[productMetadataId];

  if (!planDefaults) {
    console.warn(
      `No plan defaults found in PLANS constant for metadata ID: ${productMetadataId}. Product: ${product.name}`,
    );
    return null;
  }

  const derivedTier = planDefaults.tier;
  let planPrice = planDefaults.price;

  if (
    derivedTier !== StripePlanMapping.FREE &&
    derivedTier !== StripePlanMapping.ENTERPRISE
  ) {
    const targetPrice = product.prices?.find(
      (p) =>
        p.recurring?.interval === billingInterval &&
        p.currency === selectedCurrency &&
        p.active,
    );

    if (targetPrice && targetPrice.unit_amount !== null) {
      planPrice = formatPrice(
        targetPrice.unit_amount,
        targetPrice.currency,
        billingInterval,
      );
      planPriceId = targetPrice.id;
    } else {
      console.warn(
        `Product ${product.id} (${product.name}) is 'paid' but has no active ${billingInterval} price in ${selectedCurrency.toUpperCase()}.`,
      );
      return null;
    }
  }

  return {
    name: product.name || 'Unnamed Plan',
    tier: derivedTier,
    price: planPrice,
    description: planDefaults.description,
    features: planDefaults.features,
    priceId: planPriceId,
  };
}

function combineAndSortPlans(transformedPlans: Plan[]): Plan[] {
  const stripeEnterprisePlan = transformedPlans.find(
    (p) =>
      p.tier === StripePlanMapping.ENTERPRISE ||
      p.name.toLowerCase() === 'enterprise',
  );

  const combinedPlans = [
    PLANS[StripePlanMapping.FREE],
    ...transformedPlans.filter(
      (p) =>
        !(
          p.tier === StripePlanMapping.ENTERPRISE ||
          p.name.toLowerCase() === 'enterprise'
        ),
    ),
    stripeEnterprisePlan
      ? stripeEnterprisePlan
      : PLANS[StripePlanMapping.ENTERPRISE],
  ];

  const tierOrder: Record<string, number> = {
    [StripePlanMapping.FREE]: 1,
    [StripePlanMapping.PRO]: 2,
    [StripePlanMapping.BUSINESS]: 3,
    [StripePlanMapping.ENTERPRISE]: 4,
  };

  combinedPlans.sort((a, b) => {
    const orderA = tierOrder[a.tier as StripePlanMapping] || Infinity;
    const orderB = tierOrder[b.tier as StripePlanMapping] || Infinity;
    return orderA - orderB;
  });

  return combinedPlans;
}
