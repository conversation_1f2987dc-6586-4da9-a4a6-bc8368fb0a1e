import { memo } from 'react';
import { PersonalInfoData, SectionData } from 'shared/types';

import { Input, MultiInput, Label } from '../../../../components';

import { FormReducerAction, ReducerActionType } from '@/pages/CVEditPage';

interface PersonalInfoFormProps {
  sectionId: string;
  data: PersonalInfoData;
  onDataChange: React.Dispatch<FormReducerAction>;
}

export const PersonalInfoForm = memo(
  ({ sectionId, data, onDataChange }: PersonalInfoFormProps) => {
    const {
      firstName,
      lastName,
      jobTitle,
      location,
      nationality,
      email,
      telephone,
      socials,
    } = data;

    function handleChange(data: SectionData) {
      onDataChange({
        type: ReducerActionType.updateField,
        sectionId: sectionId,
        data,
      });
    }

    return (
      <div className="flex flex-col space-y-5 pt-4 p-px">
        <div className="flex items-center space-x-3">
          <div className="flex flex-col space-y-2 sm:flex-row sm:space-x-2 sm:space-y-0 flex-grow">
            <div className="flex flex-grow flex-col space-y-2">
              <Label
                label="First Name"
                labelProps={{ htmlFor: 'firstName' }}
                checkboxProps={{
                  checked: firstName.active,
                  onCheckedChange: (val) => {
                    handleChange({
                      ...data,
                      firstName: { ...firstName, active: !!val },
                    });
                  },
                }}
              />
              <Input
                id="firstName"
                type="text"
                value={firstName.value}
                onChange={(e) =>
                  handleChange({
                    ...data,
                    firstName: { ...firstName, value: e.target.value },
                  })
                }
              />
            </div>
            <div className="flex flex-grow flex-col space-y-2">
              <Label
                label="Last Name"
                labelProps={{ htmlFor: 'lastName' }}
                checkboxProps={{
                  checked: lastName.active,
                  onCheckedChange: (val) => {
                    handleChange({
                      ...data,
                      lastName: { ...lastName, active: !!val },
                    });
                  },
                }}
              />
              <Input
                id="lastName"
                type="text"
                value={lastName.value}
                onChange={(e) =>
                  handleChange({
                    ...data,
                    lastName: { ...lastName, value: e.target.value },
                  })
                }
              />
            </div>
          </div>
        </div>
        <div className="flex flex-col space-y-2">
          <Label
            label="Job Title"
            labelProps={{ htmlFor: 'jobTitle' }}
            checkboxProps={{
              checked: jobTitle.active,
              onCheckedChange: (val) => {
                handleChange({
                  ...data,
                  jobTitle: { ...jobTitle, active: !!val },
                });
              },
            }}
          />
          <Input
            id="jobTitle"
            type="text"
            value={jobTitle.value}
            onChange={(e) =>
              handleChange({
                ...data,
                jobTitle: { ...jobTitle, value: e.target.value },
              })
            }
          />
        </div>
        <div className="flex flex-col space-y-2">
          <Label
            label="Location"
            labelProps={{ htmlFor: 'location' }}
            checkboxProps={{
              checked: location.active,
              onCheckedChange: (val) =>
                handleChange({
                  ...data,
                  location: { ...location, active: !!val },
                }),
            }}
          />
          <Input
            id="location"
            type="text"
            value={location.value}
            onChange={(e) =>
              handleChange({
                ...data,
                location: { ...location, value: e.target.value },
              })
            }
          />
        </div>
        <div className="flex flex-col space-y-2">
          <Label
            label="Nationality"
            labelProps={{ htmlFor: 'nationality' }}
            checkboxProps={{
              checked: nationality.active,
              onCheckedChange: (val) =>
                handleChange({
                  ...data,
                  nationality: { ...nationality, active: !!val },
                }),
            }}
          />
          <Input
            id="nationality"
            type="text"
            value={nationality.value}
            onChange={(e) =>
              handleChange({
                ...data,
                nationality: { ...nationality, value: e.target.value },
              })
            }
          />
        </div>
        <div className="flex flex-col space-y-2">
          <Label
            label="Email"
            labelProps={{ htmlFor: 'email' }}
            checkboxProps={{
              checked: email.active,
              onCheckedChange: (val) =>
                handleChange({
                  ...data,
                  email: { ...email, active: !!val },
                }),
            }}
          />
          <Input
            id="email"
            type="email"
            value={email.value}
            onChange={(e) =>
              handleChange({
                ...data,
                email: { ...email, value: e.target.value },
              })
            }
          />
        </div>
        <div className="flex flex-col space-y-2">
          <Label
            label="Telephone"
            labelProps={{ htmlFor: 'telephone' }}
            checkboxProps={{
              checked: telephone.active,
              onCheckedChange: (val) =>
                handleChange({
                  ...data,
                  telephone: { ...telephone, active: !!val },
                }),
            }}
          />
          <Input
            id="telephone"
            type="text"
            value={telephone.value}
            onChange={(e) =>
              handleChange({
                ...data,
                telephone: { ...telephone, value: e.target.value },
              })
            }
          />
        </div>
        <div className="flex flex-col space-y-2">
          <Label
            label="Socials"
            checkboxProps={{
              checked: socials.active,
              onCheckedChange: (val) =>
                handleChange({
                  ...data,
                  socials: { ...socials, active: !!val },
                }),
            }}
          />
          <MultiInput
            links={socials.inputs}
            onChange={(links) => {
              handleChange({
                ...data,
                socials: { ...socials, inputs: links },
              });
            }}
          />
        </div>
      </div>
    );
  },
);
