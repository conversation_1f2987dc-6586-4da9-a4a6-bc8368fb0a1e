import { Link } from 'react-router-dom';

import {
  B<PERSON><PERSON><PERSON><PERSON>,
  BreadcrumbList,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbPage,
  BreadcrumbSeparator,
  BreadcrumbEllipsis,
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components';
import { NAVIGATE_PATH } from '@/helpers/constants';
import { cn } from '@/lib/utils';

interface MemberBreadcrumbProps {
  memberName: string;
  isSmallScreen: boolean;
  mode?: 'list' | 'edit';
  cvName?: string;
  onMemberClick?: () => void;
}

export function MemberBreadcrumb({
  memberName,
  isSmallScreen,
  mode = 'list',
  cvName,
  onMemberClick,
}: MemberBreadcrumbProps) {
  if (mode === 'list') {
    return (
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink asChild>
              <Link to={NAVIGATE_PATH.people}>Profiles</Link>
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage
              className={cn(
                'max-w-32 truncate',
                !isSmallScreen && 'md:max-w-none',
              )}
            >
              {memberName}
            </BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>
    );
  }

  // Edit mode
  return (
    <Breadcrumb>
      <BreadcrumbList>
        <BreadcrumbItem>
          <BreadcrumbLink asChild>
            <Link to={NAVIGATE_PATH.people}>Members</Link>
          </BreadcrumbLink>
        </BreadcrumbItem>
        <BreadcrumbSeparator />
        {isSmallScreen ? (
          <>
            <BreadcrumbItem>
              <DropdownMenu>
                <DropdownMenuTrigger
                  className="flex items-center gap-1"
                  aria-label="Toggle menu"
                >
                  <BreadcrumbEllipsis className="size-4" />
                </DropdownMenuTrigger>
                <DropdownMenuContent align="start">
                  <DropdownMenuItem>
                    <button
                      onClick={onMemberClick}
                      className="max-w-32 truncate text-left"
                    >
                      {memberName}
                    </button>
                  </DropdownMenuItem>
                  <DropdownMenuItem>
                    <span className="max-w-32 truncate">{cvName || 'CV'}</span>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage className="truncate max-w-20">
                Edit
              </BreadcrumbPage>
            </BreadcrumbItem>
          </>
        ) : (
          <>
            <BreadcrumbItem>
              <BreadcrumbLink asChild>
                <button
                  onClick={onMemberClick}
                  className="px-2 py-2 transition-colors rounded text-msGray-4 hover:text-msBlack"
                >
                  {memberName}
                </button>
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage className="max-w-32 truncate">
                {cvName || 'CV'}
              </BreadcrumbPage>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage>Edit</BreadcrumbPage>
            </BreadcrumbItem>
          </>
        )}
      </BreadcrumbList>
    </Breadcrumb>
  );
}
