import { cva, type VariantProps } from 'class-variance-authority';
import { ButtonHTMLAttributes, forwardRef } from 'react';

import { cn } from '@/lib/utils';

const buttonVariants = cva(
  'disabled:opacity-50 disabled:cursor-not-allowed transition-colors',
  {
    variants: {
      variant: {
        default: 'px-4 py-2 bg-msRed-1 text-white rounded-md hover:bg-msRed-2',
        pink: 'h-6 px-2 bg-msPink-1 text-msWhite rounded-[2px] hover:bg-msPink-2',
        link: 'h-fit p-0 rounded-[2px] text-smalldoge-3 font-bold text-msPink-1 hover:text-msRed-1 active:text-msRed-2',
      },
    },
    defaultVariants: {
      variant: 'link',
    },
  },
);

export interface ButtonDangerProps
  extends ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {}

export const ButtonDanger = forwardRef<HTMLButtonElement, ButtonDangerProps>(
  ({ className, children, variant, ...props }, ref) => {
    return (
      <button
        ref={ref}
        className={cn(buttonVariants({ variant }), className)}
        {...props}
      >
        {children}
      </button>
    );
  },
);
