import {
  AbilityBuilder,
  createMongoAbility,
  ExtractSubjectType,
} from '@casl/ability';
import { Injectable } from '@nestjs/common';
import { Types } from 'mongoose';
import { UserRole } from 'shared/types';

import { Action, AppAbility, Subjects } from './casl.types';
import { Customer } from '../customers/customer.schema';
import { Cv } from '../cvs/cv.schema';
import { AuthUserDto } from '../global/dto/auth-user.dto';
import { Member } from '../members/member.schema';
import { Organization } from '../organization/organization.schema';
import { User } from '../users/user.schema';

@Injectable()
export class CaslAbilityFactory {
  createForUser(user: AuthUserDto | User) {
    const { can, cannot, build } = new AbilityBuilder<AppAbility>(
      createMongoAbility,
    );

    // Extract role and organization ID based on user type
    let userRole: UserRole;
    let organizationId: Types.ObjectId;
    let userId: Types.ObjectId;

    if ('role' in user && user.role) {
      // AuthUserDto from request (has role and organization directly)
      userRole = user.role;
      organizationId = user.organization._id;
      userId = user._id;
    } else {
      // User from database (need to extract role from availableOrganizations)
      const userDoc = user as User;
      userId = userDoc._id;
      organizationId = userDoc.organization;

      // Find the role for the current organization
      const orgMembership = userDoc.availableOrganizations?.find(
        (org) => org.orgId.toString() === organizationId.toString(),
      );

      if (!orgMembership) {
        throw new Error('User is not a member of the specified organization');
      }

      userRole = orgMembership.role;
    }

    // General Permissios for all user roles
    can(Action.Manage, Cv, { organization: organizationId });
    can(Action.Manage, Member, { organization: organizationId });
    can(Action.Manage, Customer, { organization: organizationId });

    // Permissions for administrative roles (Admin, Owner)
    if (userRole === UserRole.ADMIN || userRole === UserRole.OWNER) {
      can(Action.Manage, User, {
        availableOrganizations: {
          $elemMatch: { orgId: organizationId },
        },
      });
    }

    // Role-specific permissions
    switch (userRole) {
      case UserRole.OWNER:
        can(Action.Manage, Organization, { _id: organizationId });
        break;

      case UserRole.ADMIN:
        cannot(Action.Delete, Organization).because(
          'Only owners can delete the organization.',
        );
        can(Action.Update, Organization, { _id: organizationId });
        break;
      case UserRole.MEMBER:
        // Members can only update their own profile (excluding role field)
        can(Action.Update, User, { _id: userId });
        cannot(Action.Update, User, 'role').because(
          'Members cannot change roles',
        );
    }

    return build({
      detectSubjectType: (item) =>
        item.constructor as ExtractSubjectType<Subjects>,
    });
  }
}
