import { keepPreviousData, useQuery } from '@tanstack/react-query';
import { Plus } from 'lucide-react';
import { ExpertiseLevelEnum, Skill } from 'shared/types';

import { ExpertisePicker } from '../components';

import { Loader } from '@/components/common';
import { searchSkillsRequest } from '@/helpers/requests';

interface SkillsListProps {
  searchValue: string;
  skillsToExclude: string[];
  orgId?: string;
  onAdd: (skill: Skill, level: ExpertiseLevelEnum) => void;
}

export function SkillsList({
  searchValue,
  skillsToExclude,
  orgId,
  onAdd,
}: SkillsListProps) {
  const {
    data: foundSkills,
    isLoading: foundSkillsLoading,
    isFetching: foundSkillsFetching,
  } = useQuery({
    queryKey: ['skills', { searchValue, orgId, skillsToExclude }],
    queryFn: () => searchSkillsRequest(searchValue, orgId, skillsToExclude),
    placeholderData: keepPreviousData,
  });

  if (foundSkillsLoading) {
    return (
      <div className="flex justify-center h-28 items-center">
        <Loader />
      </div>
    );
  }

  return (
    <div className="relative p-4">
      {!foundSkills?.length ? (
        <div className="flex flex-col space-y-2 items-center justify-center border border-dashed rounded-[8px] px-2 py-6">
          <img src="/images/dog-with-binocular.svg" alt="noMatches" />
          <span className="text-smalldoge-3">
            No such skill exists in our database. Try using alternate keywords.
          </span>
        </div>
      ) : (
        <div className="flex flex-col space-y-3 max-h-96 overflow-auto">
          {foundSkills.map((skill) => (
            <div
              key={skill.msId}
              className="flex flex-col bg-msGray-6 rounded-sm p-2 transition-all hover:bg-msGray-5 group"
            >
              <div className="relative flex items-center h-10">
                {skill.image && (
                  <img
                    className="size-10 shrink-0 rounded-full mr-2"
                    src={skill.image}
                    alt="skillIcon"
                  />
                )}
                <b className="text-smalldoge-3 flex-grow min-w-0 truncate">
                  {skill.name}
                </b>
                <Plus className="shrink-0" size={24} />
                <div className="absolute top-auto bottom-auto right-0 transition-all opacity-0 group-hover:opacity-100">
                  <ExpertisePicker
                    onLevelClick={(level) => onAdd(skill, level)}
                  />
                </div>
              </div>
              {skill.description && (
                <span className="text-smalldoge-4 text-msGray-2 line-clamp-2 mt-1">
                  {skill.description}
                </span>
              )}
            </div>
          ))}
        </div>
      )}
      {foundSkillsFetching && (
        <div className="absolute top-0 left-0 bottom-0 right-0 flex justify-center items-center bg-msGray-6 bg-opacity-40">
          <Loader />
        </div>
      )}
    </div>
  );
}
