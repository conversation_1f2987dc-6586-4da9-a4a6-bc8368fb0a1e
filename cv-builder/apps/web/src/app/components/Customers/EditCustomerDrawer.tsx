import { zod<PERSON>esolver } from '@hookform/resolvers/zod';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { AxiosError } from 'axios';
import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { Customer } from 'shared/types';
import { z } from 'zod';

import { ButtonDanger, ButtonPrimary, ButtonSecondary } from '@/components';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/common/Dialog';
import { Drawer } from '@/components/common/Drawer';
import { Input } from '@/components/common/Input';
import {
  updateCustomerRequest,
  deleteCustomerRequest,
} from '@/helpers/requests';

const editCustomerSchema = z.object({
  name: z
    .string()
    .min(3, 'Client name must be at least 3 characters')
    .regex(
      /^[a-zA-Z0-9\s]+$/,
      'Client name can only contain letters, numbers, and spaces',
    )
    .trim(),
});

type EditCustomerFormData = z.infer<typeof editCustomerSchema>;

interface EditCustomerDrawerProps {
  isOpen: boolean;
  customer?: Customer;
  onClose: () => void;
}

export function EditCustomerDrawer({
  isOpen,
  customer,
  onClose,
}: EditCustomerDrawerProps) {
  const queryClient = useQueryClient();
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  const {
    register,
    handleSubmit,
    reset,
    setError,
    formState: { errors, isSubmitted },
  } = useForm<EditCustomerFormData>({
    resolver: zodResolver(editCustomerSchema),
    mode: 'onSubmit',
    reValidateMode: 'onChange',
  });

  useEffect(() => {
    if (customer) {
      reset({ name: customer.name });
    }
  }, [customer, reset]);

  const renderField = (label: string, children: React.ReactNode) => (
    <div className="grid grid-cols-[150px_1fr] md:grid-cols-[200px_1fr] items-center gap-x-4 py-2">
      <div className="font-bold text-smalldoge-3 text-msGray-3">{label}</div>
      <div className="px-1">{children}</div>
    </div>
  );

  const { mutate: updateCustomer, isPending: isUpdatingCustomer } = useMutation(
    {
      mutationFn: ({
        customerId,
        dto,
      }: {
        customerId: string;
        dto: { name: string };
      }) => updateCustomerRequest(customerId, dto),
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: ['customers'] });
        queryClient.invalidateQueries({ queryKey: ['memberCvs'] });
        onClose();
      },
      onError: (error: AxiosError) => {
        const message = (error?.response?.data as { message: string })?.message;
        setError('root', {
          type: 'server',
          message: message || error.message || 'Failed to update customer',
        });
      },
    },
  );

  const { mutate: deleteCustomer, isPending: isDeletingCustomer } = useMutation(
    {
      mutationFn: deleteCustomerRequest,
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: ['customers'] });
        queryClient.invalidateQueries({ queryKey: ['memberCvs'] });
        onClose();
      },
      onError: (error: AxiosError) => {
        const message = (error?.response?.data as { message: string })?.message;
        setError('root', {
          type: 'server',
          message: message || 'Failed to delete client',
        });
      },
    },
  );

  const onSubmit = (data: EditCustomerFormData) => {
    if (!customer) return;
    updateCustomer({
      customerId: customer._id,
      dto: { name: data.name },
    });
  };

  const handleDeleteCustomer = (e: React.MouseEvent) => {
    e.preventDefault();

    if (!customer) return;

    setIsDeleteDialogOpen(false);

    deleteCustomer(customer._id);
  };

  const handleClose = () => {
    if (!isDeleteDialogOpen) {
      reset();
      onClose();
    }
  };

  return (
    <>
      <Drawer
        active={isOpen}
        onClose={handleClose}
        title="Edit client"
        disabledOutsideClick={isDeleteDialogOpen}
      >
        {customer && (
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
            {renderField(
              'Client Name',
              <Input
                id="customerName"
                type="text"
                {...register('name')}
                placeholder="Enter client name"
                className="w-full font-bold text-smalldoge-3 text-msBlack"
                error={isSubmitted ? errors.name?.message : undefined}
              />,
            )}
            {errors.root && (
              <p className="text-smalldoge-2 text-msRed-1">
                {errors.root.message}
              </p>
            )}
            <div className="flex justify-between pt-4">
              <ButtonDanger
                type="button"
                disabled={isDeletingCustomer || isUpdatingCustomer}
                className="px-2 py-1 ml-auto text-smalldoge-4"
                onClick={() => setIsDeleteDialogOpen(true)}
              >
                Delete Client
              </ButtonDanger>
              <ButtonPrimary
                type="submit"
                disabled={isUpdatingCustomer || isDeletingCustomer}
                className="px-2 py-1"
                variant="blackCompact"
              >
                {isUpdatingCustomer ? 'Updating...' : 'Update Client'}
              </ButtonPrimary>
            </div>
          </form>
        )}
      </Drawer>

      <Dialog
        open={isDeleteDialogOpen}
        onOpenChange={setIsDeleteDialogOpen}
        modal={true}
      >
        <DialogContent
          className="prevent-drawer-outside-click"
          onClick={(e) => e.stopPropagation()}
          onInteractOutside={(e) => {
            e.preventDefault();
          }}
        >
          <DialogHeader>
            <DialogTitle>Delete Client?</DialogTitle>
            <DialogDescription>
              This action is permanent and cannot be undone. This will remove
              the client from all associated CVs.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <ButtonSecondary
              variant="ghost"
              onClick={(e) => {
                e.stopPropagation();
                setIsDeleteDialogOpen(false);
              }}
            >
              Cancel
            </ButtonSecondary>
            <ButtonSecondary
              type="button"
              disabled={isDeletingCustomer}
              onClick={handleDeleteCustomer}
            >
              {isDeletingCustomer ? 'Deleting...' : 'Delete'}
            </ButtonSecondary>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
