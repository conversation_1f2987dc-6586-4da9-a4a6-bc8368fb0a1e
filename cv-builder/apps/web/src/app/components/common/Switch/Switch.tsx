import * as SwitchPrimitive from '@radix-ui/react-switch';
import { cva, type VariantProps } from 'class-variance-authority';
import * as React from 'react';

import { cn } from '@/lib/utils';

const switchRootVariants = cva(
  // Minimal base: core interaction and layout
  'peer inline-flex shrink-0 items-center rounded-full border transition-all outline-none disabled:cursor-not-allowed disabled:opacity-50 focus-visible:ring-[3px] focus-visible:border-ring focus-visible:ring-ring/50',
  {
    variants: {
      variant: {
        // Figma-based style
        default:
          'w-10 h-6 border-msGray-4 bg-msGray-6 data-[state=checked]:bg-msGray-6 data-[state=unchecked]:bg-msGray-6 shadow-xs',
        // Original shadcn style
        classic:
          'h-[1.15rem] w-8 border-transparent data-[state=checked]:bg-primary data-[state=unchecked]:bg-input dark:data-[state=unchecked]:bg-input/80 shadow-xs',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);

const switchThumbVariants = cva(
  // Minimal base for thumb
  'pointer-events-none block size-4 rounded-full ring-0 transition-transform',
  {
    variants: {
      variant: {
        // Figma-based thumb style
        default:
          'data-[state=unchecked]:bg-msGray-4 data-[state=unchecked]:border data-[state=unchecked]:border-msGray-3 data-[state=unchecked]:translate-x-1 ' +
          'data-[state=checked]:bg-msGreen-3 data-[state=checked]:border data-[state=checked]:border-msGreen-2 data-[state=checked]:translate-x-5',
        // Original shadcn thumb style
        classic:
          'bg-background dark:data-[state=unchecked]:bg-foreground dark:data-[state=checked]:bg-primary-foreground ' +
          'data-[state=checked]:translate-x-[calc(100%-2px)] data-[state=unchecked]:translate-x-0',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);

export interface SwitchProps
  extends React.ComponentPropsWithoutRef<typeof SwitchPrimitive.Root>,
    VariantProps<typeof switchRootVariants> {}

const Switch = React.forwardRef<
  React.ElementRef<typeof SwitchPrimitive.Root>,
  SwitchProps
>(({ className, variant, ...props }, ref) => {
  return (
    <SwitchPrimitive.Root
      className={cn(switchRootVariants({ variant, className }))}
      {...props}
      ref={ref}
    >
      <SwitchPrimitive.Thumb className={cn(switchThumbVariants({ variant }))} />
    </SwitchPrimitive.Root>
  );
});
Switch.displayName = 'Switch';

export { Switch };
