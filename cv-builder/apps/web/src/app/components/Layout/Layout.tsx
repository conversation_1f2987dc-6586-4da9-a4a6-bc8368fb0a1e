import { createContext, useState, useEffect } from 'react';
import { Outlet, useNavigate } from 'react-router-dom';

import { Header, Navigation } from '@/components';
import { useAuth } from '@/contexts/AuthContext';
import { NAVIGATE_PATH } from '@/helpers/constants';

export const LayoutContext = createContext<{
  setHeaderCallback: (val: string) => void;
}>({
  setHeaderCallback: () => {
    //
  },
});

const Layout = () => {
  const [menuActive, setMenuActive] = useState<boolean>(false);
  const [headerText, setHeaderText] = useState('');
  const navigate = useNavigate();
  const { user, loading } = useAuth();

  useEffect(() => {
    // If not authenticated, redirect to login
    if (!loading && !user) {
      navigate(NAVIGATE_PATH.login, { replace: true });
    }
  }, [navigate, user, loading]);

  // Show loading while checking authentication
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-lg">Loading...</div>
      </div>
    );
  }

  // Don't render layout if user is not authenticated
  if (!user) {
    return null;
  }

  return (
    <div className="w-screen h-screen bg-msGray-6">
      <Header
        menuActive={menuActive}
        headerText={headerText}
        onMenuClick={() => setMenuActive((val) => !val)}
      />
      <div className="flex">
        <Navigation
          mobileMenuActive={menuActive}
          onOutsideClick={() => setMenuActive(false)}
        />
        <main className="flex-grow h-screen min-w-0 p-3 pt-17 md:pt-3">
          {/* overflow-hidden used to make content has rounded corners same as the main block */}
          <div className="bg-msWhite rounded-[4px] h-full overflow-auto">
            <LayoutContext.Provider
              value={{ setHeaderCallback: setHeaderText }}
            >
              <Outlet />
            </LayoutContext.Provider>
          </div>
        </main>
      </div>
    </div>
  );
};

export default Layout;
