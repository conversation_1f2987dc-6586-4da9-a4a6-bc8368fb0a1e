import { keepPreviousData, useQuery } from '@tanstack/react-query';
import { format } from 'date-fns';
import { MoreHorizontal } from 'lucide-react';
import { useContext, useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { SortOrder } from 'shared/dto/common.dto';
import { GetCvsSortBy } from 'shared/dto/cv/get-cvs.dto';
import { Cv, Paging, CvStatus } from 'shared/types';

import { createFullName } from '../helpers/nameUtils';
import { getCvsRequest } from '../helpers/requests';
import { useDuplicateCv, useDeleteCv } from '../hooks/useCvMutations';

import type { SortOption } from '@/components';

import {
  CustomerBadge,
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuTrigger,
  Loader,
  Avatar,
  Pagination,
  SortDropdown,
} from '@/components';
import { Search } from '@/components/common';
import { LayoutContext } from '@/components/Layout/Layout';
import { NAVIGATE_PATH } from '@/helpers/constants';
import { cn } from '@/lib/utils';

const initialPaging = { page: 1, itemsPerPage: 12, total: 0 };

type DraftsSortOption = SortOption & {
  sortBy: GetCvsSortBy;
  sortOrder: SortOrder;
};

const sortOptions: DraftsSortOption[] = [
  {
    label: 'Name (A-Z)',
    value: 'title_asc',
    sortBy: GetCvsSortBy.TITLE,
    sortOrder: SortOrder.ASC,
  },
  {
    label: 'Name (Z-A)',
    value: 'title_desc',
    sortBy: GetCvsSortBy.TITLE,
    sortOrder: SortOrder.DESC,
  },
  {
    label: 'Updated (newest)',
    value: 'updated_desc',
    sortBy: GetCvsSortBy.UPDATED_AT,
    sortOrder: SortOrder.DESC,
  },
  {
    label: 'Updated (oldest)',
    value: 'updated_asc',
    sortBy: GetCvsSortBy.UPDATED_AT,
    sortOrder: SortOrder.ASC,
  },
];

export function DraftsPage() {
  const navigate = useNavigate();
  const { setHeaderCallback } = useContext(LayoutContext);

  const [paging, setPaging] = useState<Paging>(initialPaging);
  const [searchValue, setSearchValue] = useState('');
  const [selectedSort, setSelectedSort] = useState<DraftsSortOption>(
    sortOptions[0],
  );

  const { data: draftCvsData, isLoading } = useQuery({
    queryKey: [
      'draftCvs',
      {
        page: paging.page,
        itemsPerPage: paging.itemsPerPage,
        search: searchValue,
        sortBy: selectedSort.sortBy,
        sortOrder: selectedSort.sortOrder,
      },
    ],
    queryFn: async () =>
      getCvsRequest(
        paging,
        searchValue,
        selectedSort.sortBy,
        selectedSort.sortOrder,
        CvStatus.draft,
      ),
    placeholderData: keepPreviousData,
  });

  useEffect(() => {
    setHeaderCallback(`Drafts ${draftCvsData?.total || 0}`);
  }, [draftCvsData?.total, setHeaderCallback]);

  const handlePageChange = (page: number) => {
    setPaging((prev) => ({ ...prev, page }));
  };

  const handleItemsPerPageChange = (itemsPerPage: number) => {
    setPaging((prev) => ({ ...prev, itemsPerPage, page: 1 }));
  };

  const handleSortChange = (option: SortOption) => {
    const selected = sortOptions.find((opt) => opt.value === option.value);
    if (selected) {
      setSelectedSort(selected);
      setPaging((prev) => ({ ...prev, page: 1 }));
    }
  };

  const handleEditClick = (cvId: string) => {
    const cv = draftCvsData?.cvs.find((cv) => cv._id === cvId);
    if (cv?.member?._id) {
      navigate(
        `${NAVIGATE_PATH.cvList}/${cv.member._id}/${NAVIGATE_PATH.cvEdit}/${cvId}`,
      );
    }
  };

  const handlePreviewClick = (cvId: string) => {
    const cv = draftCvsData?.cvs.find((cv) => cv._id === cvId);
    if (cv?.member?._id) {
      navigate(`${NAVIGATE_PATH.cvList}/${cv.member._id}?previewCv=${cvId}`);
    }
  };

  const { mutate: duplicateCv } = useDuplicateCv();
  const { mutate: deleteCv } = useDeleteCv();

  const handleDuplicate = (cvId: string) => {
    const cv = draftCvsData?.cvs.find((cv) => cv._id === cvId);
    const memberId = cv?.member?._id;

    if (!memberId) {
      console.error('Member ID not found for CV:', cvId);
      return;
    }

    duplicateCv({ cvId, memberId });
  };

  const handleDelete = (cvId: string) => {
    const cv = draftCvsData?.cvs.find((cv) => cv._id === cvId);
    const memberId = cv?.member?._id;

    if (!memberId) {
      console.error('Member ID not found for CV:', cvId);
      return;
    }

    deleteCv({ cvId, memberId });
  };

  if (isLoading) {
    return (
      <div className="flex justify-center w-full h-full py-20 overflow-auto">
        <Loader size={60} />
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full px-4 py-2">
      {/* Toolbar */}
      <div className="flex items-center justify-end gap-6 mb-4">
        <h1 className="hidden mr-auto font-black text-bigdoge-6 md:inline-block">
          Drafts {draftCvsData?.total || 0}
        </h1>
        <SortDropdown
          sortOptions={sortOptions}
          selectedSort={selectedSort}
          onSortChange={handleSortChange}
        />

        {/* Search */}
        <Search
          value={searchValue}
          onChange={setSearchValue}
          placeholder="Search"
        />
      </div>

      {draftCvsData && (
        <div className="flex justify-end mb-3">
          <Pagination
            page={paging.page}
            total={draftCvsData.total}
            itemsPerPage={paging.itemsPerPage}
            onPageChange={handlePageChange}
            onItemsPerPageChange={handleItemsPerPageChange}
          />
        </div>
      )}

      {/* CV List */}
      <div className="w-full overflow-auto">
        <table className="table-fixed w-full min-w-[800px] lg:min-w-full divide-y divide-msGray-5">
          <thead className="pb-1">
            <tr>
              <th
                scope="col"
                className="text-smalldoge-5 text-msGray-3 text-left w-[75%]"
              >
                Draft CV
              </th>
              <th
                scope="col"
                className="text-smalldoge-5 text-msGray-3 text-left w-[10%]"
              >
                Last updated
              </th>
              <th
                scope="col"
                className="text-smalldoge-5 text-msGray-3 text-right w-[15%]"
              >
                Assignment to
              </th>
              <th
                scope="col"
                className="text-smalldoge-5 text-msGray-3 text-center w-[5%]"
              >
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-msGray-5">
            {draftCvsData?.cvs && draftCvsData.cvs.length > 0 ? (
              draftCvsData.cvs.map((cv: Cv) => (
                <DraftCvCard
                  key={cv._id}
                  cv={cv}
                  onEditClick={() => handleEditClick(cv._id)}
                  onPreviewClick={() => handlePreviewClick(cv._id)}
                  onDuplicate={() => handleDuplicate(cv._id)}
                  onDelete={() => handleDelete(cv._id)}
                />
              ))
            ) : (
              <tr>
                <td
                  colSpan={4}
                  className="p-6 text-center text-msGray-3 text-smalldoge-3"
                >
                  No drafts found
                  {searchValue && ' matching your search'}.
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
}

interface DraftCvCardProps {
  cv: Cv;
  onEditClick: () => void;
  onPreviewClick: () => void;
  onDuplicate: () => void;
  onDelete: () => void;
}

function DraftCvCard({
  cv,
  onEditClick,
  onPreviewClick,
  onDuplicate,
  onDelete,
}: DraftCvCardProps) {
  const tabOptions = [
    {
      name: 'Manage CV requirements',
      onClick: onEditClick,
    },
    {
      name: 'Edit CV',
      onClick: onPreviewClick,
    },
    {
      name: 'Duplicate',
      onClick: onDuplicate,
    },
    {
      name: 'Remove',
      classNames: 'text-msRed-1',
      onClick: onDelete,
    },
  ];

  return (
    <tr
      className="transition-colors duration-100 cursor-pointer hover:bg-msGray-6"
      onClick={() => onPreviewClick()}
    >
      {/* CV Info with Icon and Avatar */}
      <td className="py-2 pr-2">
        <div className="flex items-center">
          <img
            src="/icons/cv-preview.svg"
            alt="CV Preview"
            className="flex-shrink-0 mr-2 h-14"
          />
          <div className="mr-2">
            <Avatar
              size={40}
              url={cv.member?.avatar}
              name={createFullName(cv.member?.firstName, cv.member?.lastName)}
            />
          </div>

          <div className="flex flex-col min-w-0">
            <div className="font-bold truncate text-smalldoge-3">
              {cv.preferences.title}
            </div>
            <div className="truncate text-smalldoge-4 text-msGray-3">
              {createFullName(cv.member?.firstName, cv.member?.lastName)}
            </div>
          </div>
        </div>
      </td>

      {/* Last Updated */}
      <td className="py-2 pr-2 text-left">
        <span className="text-smalldoge-4 text-msGray-3">
          {format(cv.updatedAt, 'MMM dd, yyyy')}
        </span>
      </td>

      {/* Customer Assignment */}
      <td className="py-2 text-right">
        <CustomerBadge
          name={cv.preferences.customer?.name}
          className="ml-auto max-w-full"
        />
      </td>

      {/* Actions */}
      <td className="py-2 pr-2">
        <div className="flex justify-center">
          <DropdownMenu modal={false}>
            <DropdownMenuTrigger asChild>
              <button
                className="p-1 transition-colors rounded hover:bg-msGray-6"
                onClick={(e) => e.stopPropagation()}
              >
                <MoreHorizontal
                  size={16}
                  className="transition-colors duration-200 text-msGray-3 hover:text-msGray-1"
                />
              </button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-48">
              <DropdownMenuGroup>
                {tabOptions.map((option) => (
                  <DropdownMenuItem
                    key={option.name}
                    onClick={(e) => {
                      e.stopPropagation();
                      option.onClick();
                    }}
                  >
                    <span className={cn('text-smalldoge-3', option.classNames)}>
                      {option.name}
                    </span>
                  </DropdownMenuItem>
                ))}
              </DropdownMenuGroup>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </td>
    </tr>
  );
}
