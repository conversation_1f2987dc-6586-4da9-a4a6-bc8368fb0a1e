import { useMutation, useQueryClient } from '@tanstack/react-query';
import { AxiosError } from 'axios';
import { isEmpty } from 'lodash';
import { ThumbsUp, Sparkles, Sparkle } from 'lucide-react';
import { useEffect, useRef, useState } from 'react';
import {
  Message,
  CvSections,
  CustomSection,
  MessageRole,
  Cv,
} from 'shared/types';
import { toast } from 'sonner';

import {
  ButtonMagic,
  AutosizeTextarea,
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/components';
import {
  regenerateCvRequest,
  updateCvSectionsRequest,
} from '@/helpers/requests';
import { updateDraftCvTimestamp } from '@/hooks/useCvMutations';
import { cn } from '@/lib/utils';

interface AiChatProps {
  cvId: string;
  cv: Cv;
  onSectionsUpdate: (response: {
    updatedSections: {
      sections: CvSections;
      customSections: CustomSection[];
    };
    changesSummary: string;
  }) => void;
  onPendingChange?: (isPending: boolean) => void;
  onCreateSnapshot?: () => void;
  onRevertToSnapshot?: () => void;
  hasAiChanges?: boolean;
}

export function AiChat({
  cvId,
  cv,
  onSectionsUpdate,
  onPendingChange,
  onCreateSnapshot,
  onRevertToSnapshot,
  hasAiChanges,
}: AiChatProps) {
  const queryClient = useQueryClient();
  const [aiQuery, setAiQuery] = useState<string>('');
  const [messages, setMessages] = useState<Message[]>([]);
  const [isChatOpen, setIsChatOpen] = useState(true);
  const [latestAiResponse, setLatestAiResponse] = useState<{
    updatedSections: {
      sections: CvSections;
      customSections: CustomSection[];
    };
    changesSummary: string;
  } | null>(null);
  const chatContainerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (chatContainerRef.current) {
      chatContainerRef.current.scrollTop =
        chatContainerRef.current.scrollHeight;
    }
  }, [messages]);

  const { mutate, reset, isPending, isSuccess } = useMutation({
    mutationFn: ({
      id,
      query,
      messages,
    }: {
      id: string;
      query: string;
      messages: Message[];
    }) => regenerateCvRequest(id, query, messages),
    onSuccess: (data) => {
      // Store the latest AI response for saving later
      setLatestAiResponse(data);

      // Add changesSummary as system message only if it exists and is not empty
      if (data.changesSummary && data.changesSummary.trim()) {
        const systemMessage: Message = {
          role: MessageRole.ASSISTANT,
          content: data.changesSummary,
        };
        setMessages((prev) => [...prev, systemMessage]);
      }

      // Pass the full response to parent
      onSectionsUpdate(data);
    },
    onError: (error: AxiosError) => {
      const systemMessage: Message = {
        role: MessageRole.ASSISTANT,
        content: 'Something went wrong, try again',
      };
      setMessages((prev) => [...prev, systemMessage]);

      if (['local', 'test'].includes(import.meta.env.VITE_DEPLOYMENT_TYPE)) {
        const message =
          (error?.response?.data as { message: string })?.message ||
          'Something went wrong, try again';
        toast.error(message);
      }
    },
    onSettled: () => {
      setAiQuery('');
    },
  });

  // Notify parent component about pending state changes
  useEffect(() => {
    onPendingChange?.(isPending);
  }, [isPending, onPendingChange]);

  const { mutate: saveCvSections, isPending: isSaving } = useMutation({
    mutationFn: () => {
      if (!latestAiResponse?.updatedSections) {
        throw new Error('No AI changes to save');
      }

      // Transform AI response to match the expected format for updateCvSectionsRequest
      const sections = latestAiResponse.updatedSections.sections || {};
      const customSections =
        latestAiResponse.updatedSections.customSections || [];

      return updateCvSectionsRequest(cvId, {
        personalInfo: {
          data: {
            firstName: sections.personalInfo?.data?.firstName || {
              value: '',
              active: true,
            },
            lastName: sections.personalInfo?.data?.lastName || {
              value: '',
              active: true,
            },
            jobTitle: sections.personalInfo?.data?.jobTitle || {
              value: '',
              active: true,
            },
            location: sections.personalInfo?.data?.location || {
              value: '',
              active: true,
            },
            nationality: sections.personalInfo?.data?.nationality || {
              value: '',
              active: true,
            },
            email: sections.personalInfo?.data?.email || {
              value: '',
              active: true,
            },
            telephone: sections.personalInfo?.data?.telephone || {
              value: '',
              active: true,
            },
            socials: sections.personalInfo?.data?.socials || {
              inputs: [],
              active: true,
            },
          },
          active: sections.personalInfo?.active ?? true,
          order: sections.personalInfo?.order ?? 1,
          title: sections.personalInfo?.title || 'Personal Info',
        },
        aboutMe: {
          data: { description: sections.aboutMe?.data?.description || '' },
          active: sections.aboutMe?.active ?? true,
          order: sections.aboutMe?.order ?? 2,
          title: sections.aboutMe?.title || 'About Me',
        },
        workHistory: {
          data: sections.workHistory?.data || [],
          active: sections.workHistory?.active ?? true,
          order: sections.workHistory?.order ?? 3,
          title: sections.workHistory?.title || 'Work History',
        },
        education: {
          data: sections.education?.data || [],
          active: sections.education?.active ?? true,
          order: sections.education?.order ?? 4,
          title: sections.education?.title || 'Education',
        },
        certifications: {
          data: {
            description: sections.certifications?.data?.description || '',
          },
          active: sections.certifications?.active ?? true,
          order: sections.certifications?.order ?? 5,
          title: sections.certifications?.title || 'Certifications',
        },
        skills: {
          data: { description: sections.skills?.data?.description || '' },
          active: sections.skills?.active ?? true,
          order: sections.skills?.order ?? 6,
          title: sections.skills?.title || 'Skills',
        },
        languages: {
          data: { languages: sections.languages?.data?.languages || [] },
          active: sections.languages?.active ?? true,
          order: sections.languages?.order ?? 7,
          title: sections.languages?.title || 'Languages',
        },
        customSections: customSections,
      });
    },
    onSuccess: () => {
      toast.success('AI changes saved successfully');
      setLatestAiResponse(null);
      updateDraftCvTimestamp(queryClient, cvId, cv.status);
    },
    onError: () => {
      toast.error('Failed to save AI changes');
    },
  });

  function submitMessage() {
    if (!aiQuery.trim()) return;

    // Create snapshot before first AI interaction
    if (messages.length === 0 && onCreateSnapshot) {
      onCreateSnapshot();
    }

    const newUserMessage: Message = {
      role: MessageRole.USER,
      content: aiQuery.trim(),
    };

    const updatedMessages = [...messages, newUserMessage];
    setMessages(updatedMessages);

    mutate({
      id: cvId,
      query: aiQuery.trim(),
      messages: updatedMessages,
    });
  }

  return (
    <div className="relative my-4">
      <div className="relative">
        <div
          className="flex items-center border border-msMagicAi-1 bg-msWhite rounded-3xl h-6 px-1.5 z-10 cursor-pointer w-fit transition-all duration-300 ease-in-out absolute top-0 left-0 transform -translate-x-[10%] -translate-y-[50%]"
          onClick={() => setIsChatOpen(!isChatOpen)}
        >
          <Sparkle className="text-msMagicAi-1" size={16} />
          <span className="ml-1 text-smalldoge-3 text-msGray-2 hover:text-msMagicAi-1">
            {isChatOpen ? 'Close' : 'Open'}
          </span>
        </div>
      </div>
      <div
        ref={chatContainerRef}
        className={cn(
          'relative flex flex-col rounded-[8px] border border-msMagicAi-1 transition-all duration-300 ease-in-out',
          isChatOpen
            ? 'max-h-[395px] opacity-100 mb-4 p-4 overflow-y-auto'
            : 'max-h-0 opacity-0 mb-0 p-0 overflow-hidden',
        )}
      >
        <div className="flex flex-col mb-6 space-y-2">
          <div className="w-fit bg-msMagicAi-3 rounded-[6px] px-2">
            <div className="text-smalldoge-3 whitespace-pre-wrap">
              What sort of a project/RFQ should this be customized to?
            </div>
          </div>

          {/* Display conversation messages */}
          {messages.map((message, index) => (
            <div
              key={index}
              className={cn(
                'w-fit rounded-[6px] px-2',
                message.role === MessageRole.USER
                  ? 'ml-auto bg-msGray-6'
                  : 'bg-msMagicAi-3',
              )}
            >
              <div className="text-smalldoge-3 whitespace-pre-wrap">
                {message.content}
              </div>
            </div>
          ))}

          {!isPending && (
            <div className="relative ml-auto rounded-sm w-fit min-w-28 bg-msGray-6">
              <span
                style={{ wordSpacing: '1px', letterSpacing: 0 }}
                className="block px-2 py-1 break-words whitespace-pre-wrap opacity-0 text-smalldoge-3"
              >
                {aiQuery || 'Type something'}
              </span>
              <div className="absolute top-0 left-0 w-full h-full">
                <AutosizeTextarea
                  style={{ wordSpacing: '1px', letterSpacing: 0 }}
                  placeholder="Type something"
                  className="w-full h-full min-h-full px-2 py-1 border-0 shadow-none outline-none resize-none text-smalldoge-3 focus-visible:ring-0"
                  value={aiQuery}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      e.preventDefault();
                      submitMessage();
                    }
                  }}
                  onChange={(e) => setAiQuery(e.target.value)}
                />
              </div>
            </div>
          )}

          {isPending && (
            <div className="w-fit bg-msMagicAi-3 rounded-[6px] px-2">
              <span className="text-smalldoge-3">Generating...</span>
            </div>
          )}
        </div>

        <div className="flex">
          {hasAiChanges && (
            <div className="flex space-x-2">
              <Tooltip>
                <TooltipTrigger>
                  <ButtonMagic
                    onClick={() => {
                      if (onRevertToSnapshot) {
                        onRevertToSnapshot();
                      }
                      reset();
                      setAiQuery('');
                      setMessages([]);
                      setLatestAiResponse(null);
                    }}
                  >
                    <span className="text-smalldoge-4 text-msGray-2">
                      <span className="md:hidden">Discard</span>
                      <span className="hidden md:inline">
                        Discard all AI suggestions
                      </span>
                    </span>
                  </ButtonMagic>
                </TooltipTrigger>
                <TooltipContent>
                  This will remove all AI-generated edits and restore your last
                  version before using AI
                </TooltipContent>
              </Tooltip>
              {isSuccess && !isEmpty(latestAiResponse?.updatedSections) && (
                <ButtonMagic
                  variant={'green'}
                  shadow={'green'}
                  disabled={isSaving}
                  onClick={() => {
                    // Save AI suggestion to database
                    saveCvSections();
                    reset();
                    setAiQuery('');
                    // Keep messages for context, don't clear them
                  }}
                >
                  <>
                    <ThumbsUp className="text-msGreen-1" size={16} />
                    <span className="text-smalldoge-4 text-msGreen-1">
                      {isSaving ? 'Saving...' : 'Keep'}
                    </span>
                  </>
                </ButtonMagic>
              )}
            </div>
          )}

          <ButtonMagic
            variant={'rainbow'}
            shadow={'yellow'}
            className="ml-auto"
            onClick={submitMessage}
            disabled={!aiQuery}
          >
            <>
              <Sparkles className="text-msMagicAi-1" size={16} />
              <span className="text-smalldoge-4 text-msMagicAi-1">
                Customize
              </span>
            </>
          </ButtonMagic>
        </div>
      </div>
    </div>
  );
}
