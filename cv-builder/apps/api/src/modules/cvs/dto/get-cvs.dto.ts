import { Type } from 'class-transformer';
import { IsEnum, IsN<PERSON>ber, IsOptional, IsString, Min } from 'class-validator';
import { SortOrder } from 'shared/dto/common.dto';
import { GetCvsSortBy } from 'shared/dto/cv/get-cvs.dto';
import { CvStatus } from 'shared/types';
import { Trim } from 'shared/utils/decorators';

export class GetCvsDto {
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  page?: number = 1;

  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  itemsPerPage?: number = 12;

  @IsOptional()
  @IsString()
  @Trim()
  search?: string;

  @IsOptional()
  @IsEnum(GetCvsSortBy)
  sortBy?: GetCvsSortBy = GetCvsSortBy.TITLE;

  @IsOptional()
  @IsEnum(SortOrder)
  sortOrder?: SortOrder = SortOrder.ASC;

  @IsOptional()
  @IsEnum(CvStatus)
  status?: CvStatus = CvStatus.draft;
}
