import { Injectable, Logger } from '@nestjs/common';
import {
  MailDataRequired,
  MailService as SendGridMailService,
} from '@sendgrid/mail';
import { DEFAULT_USER_NAME } from 'shared/constants';
import { SignUpDto } from 'shared/dto/auth/sign-up.dto';
import { EmailTemplate } from 'shared/types';

const APP_NAME = 'CV Inventory';

const sgMail = new SendGridMailService();
sgMail.setApiKey(process.env.SENDGRID_API_KEY as string);

@Injectable()
export class MailService {
  private readonly logger = new Logger(MailService.name);

  private async sendEmail(msg: MailDataRequired) {
    // Safe by default: only send real emails when explicitly enabled
    if (process.env.SEND_REAL_EMAILS !== 'true') {
      this.logger.debug(
        'Mock mode - Email details (set SEND_REAL_EMAILS=true to send real emails):',
        {
          to: msg.to,
          from: msg.from,
          templateId: msg.templateId,
          dynamicTemplateData: msg.dynamicTemplateData,
        },
      );
      return;
    }

    return sgMail.send(msg);
  }

  async sendInviteEmail(
    email: string,
    name: string | undefined,
    token: string,
    organizationName: string,
  ) {
    try {
      const msg = {
        to: email,
        from: process.env.SENDGRID_FROM_EMAIL as string,
        templateId: EmailTemplate.INVITATION_SENT,
        dynamicTemplateData: {
          organizationName,
          appName: APP_NAME,
          salutation: name || 'there',
          inviteLink: `${process.env.VITE_WEB_BASE_URL}/accept-invite?token=${token}`,
        },
      };

      const info = await this.sendEmail(msg);
      this.logger.log(
        `Invite email sent successfully to ${email} for organization ${organizationName}`,
      );
      return info;
    } catch (error: unknown) {
      this.logger.error(
        `Failed to send invite email to ${email}: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
      throw error;
    }
  }

  async sendSignUpConfirmation(dto: SignUpDto, verificationToken: string) {
    try {
      const { email } = dto;

      const msg = {
        to: email,
        from: process.env.SENDGRID_FROM_EMAIL as string,
        templateId: EmailTemplate.SIGNUP_CONFIRMATION,
        dynamicTemplateData: {
          verificationLink: `${process.env.VITE_API_BASE_URL}/activate/${verificationToken}`,
          appName: APP_NAME,
        },
      };

      const info = await this.sendEmail(msg);
      this.logger.log(
        `Signup confirmation email sent successfully to ${email}`,
      );
      return info;
    } catch (error: unknown) {
      this.logger.error(
        `Failed to send signup confirmation email to ${dto.email}: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
      throw error;
    }
  }

  async sendPasswordResetEmail(
    email: string,
    firstName: string,
    resetToken: string,
  ) {
    try {
      const msg = {
        to: email,
        from: process.env.SENDGRID_FROM_EMAIL as string,
        templateId: EmailTemplate.RESET_PASSWORD,
        dynamicTemplateData: {
          salutation: firstName != DEFAULT_USER_NAME ? firstName : 'there',
          resetLink: `${
            process.env.VITE_WEB_BASE_URL
          }${'/set-new-password'}?token=${resetToken}`,
          appName: APP_NAME,
        },
      };

      const info = await this.sendEmail(msg);
      this.logger.log(`Password reset email sent successfully to ${email}`);
      return info;
    } catch (error: unknown) {
      this.logger.error(
        `Failed to send password reset email to ${email}: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
      throw error;
    }
  }

  async sendOrganizationDeletionNotification(
    email: string,
    firstName: string,
    lastName: string,
    organizationName: string,
    deletionDate: Date,
  ) {
    try {
      const msg = {
        to: email,
        from: process.env.SENDGRID_FROM_EMAIL as string,
        templateId: EmailTemplate.CV_TEAM_DEL_SCHEDULED,
        dynamicTemplateData: {
          first_name: firstName,
          last_name: lastName,
          appName: APP_NAME,
          organizationName,
          deletionDate: deletionDate.toISOString(),
        },
      };

      const info = await this.sendEmail(msg);
      this.logger.log(
        `Organization deletion notification email sent successfully to ${email} for organization ${organizationName}`,
      );
      return info;
    } catch (error: unknown) {
      this.logger.error(
        `Failed to send organization deletion notification email to ${email}: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
      throw error;
    }
  }

  async sendUserAccountDeletionNotification(
    email: string,
    firstName: string,
    lastName: string,
    deletionDate: Date,
  ) {
    try {
      const msg = {
        to: email,
        from: process.env.SENDGRID_FROM_EMAIL as string,
        templateId: EmailTemplate.CV_ACCOUNT_DEL_SCHEDULED,
        dynamicTemplateData: {
          first_name: firstName,
          last_name: lastName,
          appName: APP_NAME,
          deletionDate: deletionDate.toISOString(),
        },
      };

      const info = await this.sendEmail(msg);
      this.logger.log(
        `User account deletion notification email sent successfully to ${email}`,
      );
      return info;
    } catch (error: unknown) {
      this.logger.error(
        `Failed to send user account deletion notification email to ${email}: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
      throw error;
    }
  }

  async sendUserAccountDeletedConfirmation(
    email: string,
    firstName: string,
    lastName: string,
  ) {
    try {
      const msg = {
        to: email,
        from: process.env.SENDGRID_FROM_EMAIL as string,
        templateId: EmailTemplate.CV_ACCOUNT_DEL,
        dynamicTemplateData: {
          first_name: firstName,
          last_name: lastName,
          appName: APP_NAME,
        },
      };

      const info = await this.sendEmail(msg);
      this.logger.log(
        `User account deleted confirmation email sent successfully to ${email}`,
      );
      return info;
    } catch (error: unknown) {
      this.logger.error(
        `Failed to send user account deleted confirmation email to ${email}: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
      throw error;
    }
  }

  async sendSoleOrganizationDeletedConfirmation(
    email: string,
    firstName: string,
    lastName: string,
    organizationName: string,
  ) {
    try {
      const msg = {
        to: email,
        from: process.env.SENDGRID_FROM_EMAIL as string,
        templateId: EmailTemplate.CV_SOLE_TEAM_DEL,
        dynamicTemplateData: {
          first_name: firstName,
          last_name: lastName,
          appName: APP_NAME,
          organizationName,
        },
      };

      const info = await this.sendEmail(msg);
      this.logger.log(
        `Sole organization deleted confirmation email sent successfully to ${email} for organization ${organizationName}`,
      );
      return info;
    } catch (error: unknown) {
      this.logger.error(
        `Failed to send sole organization deleted confirmation email to ${email}: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
      throw error;
    }
  }

  async sendOrganizationDeletedConfirmation(
    email: string,
    firstName: string,
    lastName: string,
    organizationName: string,
  ) {
    try {
      const msg = {
        to: email,
        from: process.env.SENDGRID_FROM_EMAIL as string,
        templateId: EmailTemplate.CV_TEAM_DEL,
        dynamicTemplateData: {
          first_name: firstName,
          last_name: lastName,
          appName: APP_NAME,
          organizationName,
        },
      };

      const info = await this.sendEmail(msg);
      this.logger.log(
        `Organization deleted confirmation email sent successfully to ${email} for organization ${organizationName}`,
      );
      return info;
    } catch (error: unknown) {
      this.logger.error(
        `Failed to send organization deleted confirmation email to ${email}: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
      throw error;
    }
  }
}
