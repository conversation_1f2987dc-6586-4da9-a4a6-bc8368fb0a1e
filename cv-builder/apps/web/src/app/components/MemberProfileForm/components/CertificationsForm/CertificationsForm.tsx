import { Search, X } from 'lucide-react';
import { useState } from 'react';
import { useFieldArray, useFormContext } from 'react-hook-form';
import { useDebounce } from 'use-debounce';

import { CertificationsList } from './components';
import { MemberProfileFormValues } from '../../types';

import {
  Input,
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/common';
import useOutsideClick from '@/hooks/useOutsideClick';

interface CertificationsFormProps {
  readonly?: boolean;
  externalMember?: boolean;
}

export function CertificationsForm({
  readonly,
  externalMember,
}: CertificationsFormProps) {
  const [popoverOpen, setPopoverOpen] = useState<boolean>(false);
  const [searchValue, setSearchValue] = useState<string>('');
  const [debounceSetSearchValue] = useDebounce(searchValue, 500);

  const popoverRef = useOutsideClick<HTMLDivElement>(handleOutsideClick);

  function handleOutsideClick(event: MouseEvent | TouchEvent) {
    const targetElement = event.target as HTMLElement;

    if (
      popoverOpen &&
      !targetElement.closest('.popover-content') &&
      !targetElement.classList.contains('popover-content')
    ) {
      setPopoverOpen(false);
    }
  }

  const { control } = useFormContext<MemberProfileFormValues>();
  const {
    fields: memberCertifications,
    append,
    remove,
  } = useFieldArray<MemberProfileFormValues, 'certifications'>({
    control,
    name: 'certifications',
  });

  return (
    <div className="flex flex-col min-h-0">
      {!readonly && (
        <div ref={popoverRef}>
          <Popover
            open={popoverOpen}
            onOpenChange={(val) => {
              if (popoverOpen) return;

              setPopoverOpen(val);
            }}
          >
            <PopoverTrigger asChild>
              <div className="mb-3">
                <Input
                  prefixElement={
                    <Search
                      className="absolute transform -translate-y-2.5 translate-x-2 top-[50%]"
                      size={20}
                    />
                  }
                  value={searchValue}
                  placeholder="Search a certificate"
                  className="h-12 pl-8"
                  onChange={(e) => setSearchValue(e.target.value)}
                />
              </div>
            </PopoverTrigger>
            <PopoverContent
              className="p-0 prevent-drawer-outside-click popover-content"
              isFullWidth={true}
              align="start"
              sideOffset={10}
              collisionPadding={10}
              onOpenAutoFocus={(e) => e.preventDefault()}
            >
              <CertificationsList
                searchValue={debounceSetSearchValue}
                certificatesToExclude={memberCertifications.map(
                  (mc) => mc.msId,
                )}
                onAdd={(cert) => {
                  append({
                    msId: cert.msId,
                    name: cert.name,
                    organization: cert.organization,
                  });

                  setPopoverOpen(false);
                  setSearchValue('');
                }}
              />
            </PopoverContent>
          </Popover>
        </div>
      )}
      {memberCertifications?.length ? (
        <div className="flex flex-wrap overflow-auto">
          {memberCertifications
            .sort((a, b) => a.name.localeCompare(b.name))
            .map((mc, i) => (
              <div
                key={mc.msId}
                className="h-6 w-fit flex items-center space-x-1 bg-msBlue-3 rounded-[100px] px-2 min-w-0 mr-2 mb-2 cursor-default"
              >
                <span className="truncate text-smalldoge-3 text-msBlue-1">
                  {mc.name}
                </span>
                {!readonly && (
                  <button
                    type="button"
                    className="shrink-0"
                    onClick={(e) => {
                      e.stopPropagation();
                      remove(i);
                    }}
                  >
                    <X className="text-msBlue-1" size={12} strokeWidth={3} />
                  </button>
                )}
              </div>
            ))}
        </div>
      ) : (
        <div className="flex flex-col space-y-2 items-center justify-center border border-dashed rounded-[8px] px-2 py-6">
          <img src="/images/notes.svg" alt="notes" />
          <span className="text-smalldoge-3 text-center">
            {externalMember ? (
              <>
                No certificates found from your HR system.
                <br />
                You can make changes there and they will be reflected here after
                sync.
              </>
            ) : (
              <>No certificates added yet.</>
            )}
          </span>
        </div>
      )}
    </div>
  );
}
