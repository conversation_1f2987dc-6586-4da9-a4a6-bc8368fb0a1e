/**
 * Parses URL query parameters and returns them as an object
 * @returns Object with query parameters
 */
export function getQueryParams(): Record<string, string> {
  const params = new URLSearchParams(window.location.search);
  const result: Record<string, string> = {};

  params.forEach((value, key) => {
    result[key] = value;
  });

  return result;
}

/**
 * Removes query parameters from the URL without reloading the page
 */
export function clearQueryParams(): void {
  const url = new URL(window.location.href);
  window.history.replaceState({}, document.title, url.pathname);
}

export function parseHostName(url: string) {
  try {
    return new URL(url, 'https://base').hostname.toLowerCase();
  } catch {
    return null;
  }
}
