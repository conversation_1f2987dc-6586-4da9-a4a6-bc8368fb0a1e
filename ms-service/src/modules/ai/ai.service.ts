import {
  BadGatewayException,
  BadRequestException,
  Injectable,
} from '@nestjs/common';
import { OpenAI } from 'openai';
import { ChatCompletionCreateParamsNonStreaming } from 'openai/resources/chat/completions';

import * as countries from '../../data/countries.json';
import { Country } from '../../types';
import { delay } from '../../helpers';

export interface Skill {
  name: string;
  type: 'soft' | 'software' | 'job' | 'custom';
  language: string;
  categoryName?: string;
}

const maxAttempts = 10;

@Injectable()
export class AiService {
  // new
  async getAnalysisData({ query }: { query: string }): Promise<{
    error: boolean;
    errorMessage: string;
    title: string;
    description: string;
  }> {
    if (!query || query.trim().length < 3) {
      return {
        error: true,
        errorMessage: 'Your query is too short. Please provide more details.',
        title: '',
        description: '',
      };
    }

    const apiKey = process.env.OPENAI_API_KEY;
    const openai = new OpenAI({ apiKey });
    const prompt = `
    You are a professional content generator. Based on the user's query provided below, generate a JSON object with the following keys:
    - "error": a boolean that is false if the query is valid, or true if the query is completely inappropriate or nonsensical.
    - "errorMessage": a string containing an error message if the query is inappropriate; otherwise, an empty string.
    - "title": a concise, keyword-focused title that starts with the most important keywords. The title should be short (max 50 characters) and immediately identify the purpose of the skills list.
    - "description": a description that introduces a list of skills, explaining that these skills are critical for the specified purpose.

    IMPORTANT GUIDELINES:
    1. The title MUST start with the most important keywords (e.g., "Manager Skills", "Innovation Competencies", "Data Science Expertise").
    2. Do NOT start titles with generic phrases like "Essential skills for" or "Important competencies for".
    3. The title should be short and immediately identifiable in a sidebar.
    4. Do NOT list or mention any specific skills in the output.
    5. The description should only introduce the concept of a skills list and state that the skills to follow are essential for the specified purpose.
    6. Be very flexible with queries - almost any query related to skills, competencies, roles, abilities, technologies, or trends should be considered valid.
    7. For queries about trending or current technologies (e.g., "trendy AI techs", "most popular programming languages"), create appropriate skills lists for professionals in those domains.
    8. Convert direct requests for technologies or tools into skills needed to work with those technologies.
    9. Only mark as error if the query is completely inappropriate, nonsensical, or unrelated to professional competencies.
    10. Always output valid JSON matching exactly the following format:
    {
      "error": boolean,
      "errorMessage": string,
      "title": string,
      "description": string
    }
    User query: "${query}"
      `;
    try {
      const completionParams: ChatCompletionCreateParamsNonStreaming = {
        model: 'gpt-4o',
        messages: [{ role: 'user', content: prompt }],
        temperature: 0.8,
        response_format: {
          type: 'json_schema',
          json_schema: {
            name: 'RoleIntroduction',
            schema: {
              type: 'object',
              properties: {
                error: {
                  type: 'boolean',
                  description: 'Indicates whether there is an error',
                },
                errorMessage: {
                  type: 'string',
                  description:
                    'The error message if error is true; otherwise, an empty string',
                },
                title: {
                  type: 'string',
                  description:
                    'The generated title when no error, or empty string if error is true',
                },
                description: {
                  type: 'string',
                  description:
                    'The generated description when no error, or empty string if error is true',
                },
              },
              required: ['error', 'errorMessage', 'title', 'description'],
              additionalProperties: false,
            },
            strict: true,
          },
        },
      };
      const response = await openai.chat.completions.create(completionParams);
      const content = response.choices?.[0]?.message?.content;
      if (!content)
        return {
          error: true,
          errorMessage:
            "We couldn't generate a response. Please try a different query.",
          title: '',
          description: '',
        };
      const parsedResponse = JSON.parse(content);
      if (
        typeof parsedResponse.error !== 'boolean' ||
        typeof parsedResponse.errorMessage !== 'string' ||
        typeof parsedResponse.title !== 'string' ||
        typeof parsedResponse.description !== 'string'
      ) {
        return {
          error: true,
          errorMessage: 'Something went wrong. Please try a different query.',
          title: '',
          description: '',
        };
      }
      return parsedResponse;
    } catch (error) {
      console.error(`Failed to getAnalysisData for query "${query}":`, error);
      return {
        error: true,
        errorMessage: 'Something went wrong. Please try a different query.',
        title: '',
        description: '',
      };
    }
  }

  async generateEmbeddings(
    text: string,
    retries = 3,
    delayMs = 500,
    dimensions = 1536,
  ): Promise<number[]> {
    const apiKey = process.env.OPENAI_API_KEY;
    const openai = new OpenAI({
      apiKey,
    });

    try {
      const response = await openai.embeddings.create({
        model: 'text-embedding-3-large',
        input: text,
        dimensions,
      });

      return response.data[0].embedding;
    } catch (error) {
      if (retries > 0 && error.response?.status === 429) {
        console.warn(
          `Rate limit hit, retrying in ${
            delayMs / 1000
          } seconds... (${retries} attempts left)`,
        );
        await delay(delayMs);
        return this.generateEmbeddings(
          text,
          retries - 1,
          delayMs * 2,
          dimensions,
        );
      }

      console.error('Failed to generate embedding:', {
        error: error.message,
        status: error.response?.status,
        text: text.substring(0, 100) + '...',
      });

      throw error;
    }
  }

  async getSkillsForAnalysis({
    analysis,
    skillsToSkip = [],
    customSkills = [],
    query,
    companyDescription,
  }: {
    analysis: { title: string; description: string };
    skillsToSkip?: string[];
    customSkills?: string[];
    query: string;
    companyDescription?: string;
  }): Promise<{
    error: boolean;
    errorMessage: string;
    skills: {
      name: string;
      isImportant: boolean;
      level: 'Beginner' | 'Intermediate' | 'Expert';
    }[];
  }> {
    if (!analysis || !analysis.title || !query || query.trim().length === 0) {
      return {
        error: true,
        errorMessage:
          'Please provide a valid skill list title and a clear query.',
        skills: [],
      };
    }

    const apiKey = process.env.OPENAI_API_KEY;
    const openai = new OpenAI({ apiKey });

    // Format the skills to skip and custom skills for prompts
    const skillsToSkipText =
      skillsToSkip.length > 0
        ? `Skills to skip: ${skillsToSkip.join(', ')}.`
        : '';
    const customSkillsText =
      customSkills.length > 0
        ? `Custom skills available: ${JSON.stringify(customSkills)}.`
        : '';

    try {
      // Step 1: Determine appropriate skill count based on skillsToSkip size
      const baseSkillCount = 40;
      const minSkillCount = 5;
      // Adjust target count to account for skills that might be skipped
      const targetSkillCount = Math.max(
        minSkillCount,
        baseSkillCount - Math.floor(skillsToSkip.length / 2),
      );

      // Step 2: Analyze the query to determine the type of request and if web search is needed
      const companyContext = companyDescription
        ? `Company context: "${companyDescription}"`
        : '';

      const analyzePrompt = `Analyze this user query and determine:
1. Whether the query is appropriate and valid
2. Which category the query falls into (if valid)
3. Whether web search would be needed for current/trending information (if valid)

Query: "${query}"
List title: "${analysis.title}"
List description: "${analysis.description || ''}"
${companyContext}

VALIDATION RULES:
- Reject queries containing profanity, inappropriate language, or offensive content
- Reject queries that are mostly random characters or nonsensical text (e.g., "asfkdjhgksjdhfgkjs")
- Reject queries that are completely unrelated to professional skills, jobs, or work
- Accept queries about skills, job titles, roles, teams, professional development, etc.

Categories (for valid queries):
1. SPECIFIC_SKILLS_REQUEST: User explicitly lists skills they want to include, possibly with priority indications
2. JOB_TITLE_SIMPLE: User provides a basic job title without details
3. DETAILED_ROLE: User provides an in-depth role description
4. ABSTRACT_CONCEPT: User asks for theoretical skills (innovation, learning, etc.)
5. TEAM_ANALYSIS: User wants to analyze existing team skills for reporting
6. TRENDING_SKILLS: User wants current, trending, or latest skills in a field

Return your analysis as a valid JSON with exactly these fields:
{
  "isValid": boolean,
  "errorMessage": string (empty if valid, descriptive error if invalid),
  "queryType": "CATEGORY_NAME" (only if valid),
  "needsWebSearch": boolean (only if valid)
}

For needsWebSearch, return true if:
- The query mentions trending, current, latest, new, recent skills
- The information is likely to be updated frequently
- Up-to-date information would significantly improve the response quality`;

      const analysisResponse = await openai.chat.completions.create({
        model: 'gpt-4o',
        messages: [{ role: 'user', content: analyzePrompt }],
        temperature: 0.3,
        response_format: { type: 'json_object' } as const,
      });

      const analysisContent = analysisResponse.choices?.[0]?.message?.content;

      let queryType = 'DETAILED_ROLE';
      let needsWebSearch = false;

      if (analysisContent) {
        try {
          const analysis = JSON.parse(analysisContent);

          // Check if the query is valid
          if (analysis.isValid === false) {
            return {
              error: true,
              errorMessage:
                analysis.errorMessage ||
                'Invalid or inappropriate query. Please provide a professional, work-related query about skills, job roles, or professional development.',
              skills: [],
            };
          }

          if (analysis.queryType) {
            queryType = analysis.queryType;
          }
          if (typeof analysis.needsWebSearch === 'boolean') {
            needsWebSearch = analysis.needsWebSearch;
          }
        } catch (parseError) {
          console.error('Failed to parse analysis response:', parseError);
        }
      }

      console.log(
        'queryType:',
        queryType,
        'needsWebSearch:',
        needsWebSearch,
        'targetSkillCount:',
        targetSkillCount,
      );

      // Step 3: Generate the appropriate prompt based on the query type
      let skillsPrompt = '';

      // Common instructions to add to all prompts
      const commonInstructions = `
IMPORTANT GUIDELINES FOR SKILLS:
- Each skill must be a SINGLE specific skill, tool, or technology
- DO NOT group multiple skills together (e.g., use "Django" not "Web Frameworks (Django, Flask, FastAPI)")
- DO NOT use general categories as skills (e.g., use "Python" not "Advanced Python Programming")
- For technical skills, use the official name (e.g., "PostgreSQL" not "Database Management")
- For software/frameworks, list each one separately (e.g., "Django", "Flask", "FastAPI" as separate skills)
- For soft skills, use single specific terms (e.g., "Empathy", "Leadership") 
- AVOID parentheses in skill names
- ALWAYS include skills explicitly requested in the user query - these are highest priority
- If the user mentions specific skills like "Emotional Intelligence" or "Cultivating Inclusivity", ALWAYS include them
- Technical skills MUST include relevant services, tools, platforms, and applications - not just programming languages
- When user asks for services/tools/platforms (e.g., "AI services for app building"), prioritize returning actual service names (e.g., "OpenAI API", "Hugging Face", "Anthropic Claude API") over programming languages
- CRITICAL: If company context is provided, you MUST prioritize technologies, tools, and skills that align with the company's tech stack, industry, and specific requirements mentioned
- When company context specifies technologies (e.g., "we use Go exclusively"), those technologies should be prioritized over alternatives (e.g., prioritize Go over Java/Python for backend development)

CRITICAL LEVEL ASSIGNMENT GUIDELINES:
- BE VERY CONSERVATIVE with "Expert" level - use it ONLY for life-critical scenarios (medical expertise, safety-critical systems) or when user explicitly requests expert level
- For isImportant=true skills that are crucial for the role: assign "Intermediate" level (NOT Expert)
- For isImportant=true skills that are recommended but not critical: assign "Beginner" level
- For isImportant=false skills: generally avoid assigning levels, or use "Beginner" sparingly and NEVER "Expert"
- Expert level should be reserved for scenarios where lack of expertise could cause severe consequences (patient safety, system security, etc.)
- Default assumption: most professional skills can be performed effectively at "Intermediate" level`;

      if (queryType === 'SPECIFIC_SKILLS_REQUEST') {
        skillsPrompt = `
You are a professional skills analyst. Based on the user query, generate a JSON object with skills that match the following criteria:

User query: "${query}"
List title: "${analysis.title}"
List description: "${analysis.description || ''}"
${companyContext}
${skillsToSkipText}
${customSkillsText}

INSTRUCTIONS:
1. FIRST, extract any specific skills the user explicitly requests to include - THESE MUST BE INCLUDED in your response
2. If company context is provided, prioritize skills that match the company's tech stack, industry, and requirements over generic alternatives
3. Prioritize using skills from the custom skills list provided
4. For any technical skills not in the custom list that are essential, add them
5. If the user specifies priorities, mark those skills as "isImportant": true
6. Assign levels conservatively: "Intermediate" for crucial skills, "Beginner" for recommended skills, "Expert" only for life-critical expertise or when explicitly requested
7. Exclude any skills in the "Skills to skip" list
8. The total number of skills should be between ${Math.round(
          targetSkillCount * 0.7,
        )}-${targetSkillCount}, with a preference for using custom skills
${commonInstructions}`;
      } else if (queryType === 'JOB_TITLE_SIMPLE') {
        skillsPrompt = `
You are a professional skills analyst. Generate a comprehensive skill list for this job title:

Job title: "${query}"
List title: "${analysis.title}"
List description: "${analysis.description || ''}"
${companyContext}
${skillsToSkipText}
${customSkillsText}

INSTRUCTIONS:
1. If company context is provided, prioritize skills that align with the company's technology stack and industry requirements
2. Prioritize using skills from the custom skills list provided
3. Add essential technical skills not in the custom list that are required for this job
4. Include approximately ${targetSkillCount} skills total
5. Mark the most essential skills as "isImportant": true (around 30% of total)
6. Assign levels conservatively: "Intermediate" for essential job skills, "Beginner" for supportive skills, "Expert" only for safety-critical roles or specialized expertise
7. Exclude any skills in the "Skills to skip" list
8. If the job involves using services, tools, or platforms, include SPECIFIC SERVICE/TOOL NAMES in the skills list
${commonInstructions}`;
      } else if (queryType === 'DETAILED_ROLE') {
        skillsPrompt = `
You are a professional skills analyst. Generate a skill list for this detailed role description:

Role description: "${query}"
List title: "${analysis.title}"
List description: "${analysis.description || ''}"
${companyContext}
${skillsToSkipText}
${customSkillsText}

INSTRUCTIONS:
1. Analyze the detailed role description carefully
2. If company context is provided, ensure skills align with the company's tech stack, industry, and specific requirements
3. Prioritize using skills from the custom skills list that match the description
4. Add essential technical skills not in the custom list that are mentioned or implied
5. Include approximately ${targetSkillCount} skills total, focusing on the most relevant
6. Mark the most critical skills as "isImportant": true (around 30% of total)
7. Assign levels conservatively: "Intermediate" for role-critical skills, "Beginner" for supporting skills, "Expert" only for specialized/critical domains
8. Exclude any skills in the "Skills to skip" list
9. If the role involves using services, tools, or platforms, include SPECIFIC SERVICE/TOOL NAMES in the skills list
${commonInstructions}`;
      } else if (
        queryType === 'ABSTRACT_CONCEPT' ||
        queryType === 'TRENDING_SKILLS'
      ) {
        skillsPrompt = `
You are a professional skills analyst. Generate a skill list for this ${
          needsWebSearch ? 'current and trending' : ''
        } topic or concept:

Topic/Concept: "${query}"
List title: "${analysis.title}"
List description: "${analysis.description || ''}"
${companyContext}
${skillsToSkipText}
${customSkillsText}

INSTRUCTIONS:
1. Identify the key ${
          needsWebSearch ? 'current and trending aspects of the' : ''
        } concept (innovation, learning agility, teamwork, etc.)
2. If company context is provided, focus on skills relevant to the company's technology stack and industry when applying the concept
3. Select skills from the custom skills list that support this concept
4. Add technical skills not in the custom list that are relevant to the concept${
          needsWebSearch ? ', including trending and current skills' : ''
        }
5. Include approximately ${Math.round(targetSkillCount * 0.85)} skills total
6. Mark the most directly relevant skills as "isImportant": true (around 25% of total)
7. Assign levels conservatively: "Intermediate" for core concept skills, "Beginner" for supporting skills, "Expert" rarely and only for complex specializations
8. Exclude any skills in the "Skills to skip" list
9. If the concept involves services, tools, or platforms, include SPECIFIC SERVICE/TOOL NAMES in the skills list
${commonInstructions}`;
      } else if (queryType === 'TEAM_ANALYSIS') {
        skillsPrompt = `
You are a professional skills analyst. Help analyze existing team skills for reporting purposes:

Team context: "${query}"
List title: "${analysis.title}"
List description: "${analysis.description || ''}"
${companyContext}
${skillsToSkipText}
${customSkillsText}

INSTRUCTIONS:
1. Focus primarily on skills from the custom skills list as these represent the team's actual skills
2. If company context is provided, ensure selected skills align with the company's technology stack and operational requirements
3. Select the skills most relevant to the team's function described in the context
4. Include approximately ${Math.round(targetSkillCount * 0.75)} skills total
5. Mark the most critical skills for the team's function as "isImportant": true
6. Assign levels conservatively: "Intermediate" for team-critical skills, "Beginner" for secondary skills, "Expert" only for specialized team functions
7. Exclude any skills in the "Skills to skip" list
8. If the team uses specific services, tools, or platforms, include those SERVICE/TOOL NAMES in the skills list
${commonInstructions}`;
      } else {
        // Default case - use a general prompt
        skillsPrompt = `
You are a professional skills analyst. Generate a skill list based on this query:

Query: "${query}"
List title: "${analysis.title}"
List description: "${analysis.description || ''}"
${companyContext}
${skillsToSkipText}
${customSkillsText}

INSTRUCTIONS:
1. If company context is provided, prioritize skills that match the company's technology stack and industry requirements
2. Prioritize using skills from the custom skills list provided
3. Add essential technical skills not in the custom list that are required
4. Include approximately ${targetSkillCount} skills total
5. Mark the most essential skills as "isImportant": true (around 30% of total)
6. Assign levels conservatively: "Intermediate" for essential skills, "Beginner" for recommended skills, "Expert" only for critical specializations
7. Exclude any skills in the "Skills to skip" list
8. If the query mentions services, tools, or platforms, prioritize including SPECIFIC SERVICE/TOOL NAMES rather than just programming languages
${commonInstructions}`;
      }

      // Add the common output format to all prompts
      skillsPrompt += `

Return your response in a valid JSON object with these fields:
{
  "error": boolean,
  "errorMessage": string,
  "skills": [
    { "name": string, "isImportant": boolean, "level": "Beginner" | "Intermediate" | "Expert" }
  ]
}`;

      // Step 4: Get the skills based on the appropriate prompt and model
      const model =
        needsWebSearch || queryType === 'TRENDING_SKILLS'
          ? 'gpt-4o-search-preview'
          : 'gpt-4o';
      console.log('Using model:', model);

      const completionParams: ChatCompletionCreateParamsNonStreaming = {
        model,
        messages: [{ role: 'user', content: skillsPrompt }],
        ...(model === 'gpt-4o' ? { temperature: 0.7 } : {}),
        response_format:
          model === 'gpt-4o'
            ? {
                type: 'json_schema',
                json_schema: {
                  name: 'SkillsResponse',
                  schema: {
                    type: 'object',
                    properties: {
                      error: {
                        type: 'boolean',
                        description: 'Indicates whether there is an error',
                      },
                      errorMessage: {
                        type: 'string',
                        description:
                          'Error message if error is true; otherwise an empty string',
                      },
                      skills: {
                        type: 'array',
                        description:
                          'An array of recommended skills with importance marker and proficiency level',
                        items: {
                          type: 'object',
                          properties: {
                            name: {
                              type: 'string',
                              description:
                                'A concise, standalone term for the skill',
                            },
                            isImportant: {
                              type: 'boolean',
                              description:
                                'Indicates if the skill is critical for success',
                            },
                            level: {
                              type: 'string',
                              enum: ['Beginner', 'Intermediate', 'Expert'],
                              description:
                                'The minimum required proficiency for the skill',
                            },
                          },
                          required: ['name', 'isImportant', 'level'],
                          additionalProperties: false,
                        },
                      },
                    },
                    required: ['error', 'errorMessage', 'skills'],
                    additionalProperties: false,
                  },
                  strict: true,
                },
              }
            : undefined, // Don't use response_format for search-preview model
      };

      const response = await openai.chat.completions.create(completionParams);
      const content = response.choices?.[0]?.message?.content;

      if (!content) {
        return {
          error: true,
          errorMessage:
            "We couldn't generate a response. Please try a different query.",
          skills: [],
        };
      }

      try {
        // Extract skills from the response
        try {
          // First check if content is markdown-formatted JSON
          if (content.startsWith('```json') || content.includes('```json')) {
            console.log(
              'Detected markdown JSON format in initial response - using specialized parser',
            );
            // Let the extraction logic below handle it
            throw new Error('EXPECTED_MARKDOWN_FORMAT');
          }

          // Only try direct parsing if not markdown formatted
          const parsedResponse = JSON.parse(content);
          if (
            typeof parsedResponse.error !== 'boolean' ||
            typeof parsedResponse.errorMessage !== 'string' ||
            !Array.isArray(parsedResponse.skills)
          ) {
            return {
              error: true,
              errorMessage:
                'Something went wrong. Please try a different query.',
              skills: [],
            };
          }

          return parsedResponse;
        } catch (parseError) {
          // Don't log as error if it's our controlled markdown format exception
          if (parseError.message === 'EXPECTED_MARKDOWN_FORMAT') {
            console.log('Switching to markdown parser as expected');
          } else {
            console.error('Failed to parse response:', parseError.message);
          }

          // Try to extract JSON from the content if using web search model
          if (model === 'gpt-4o-search-preview') {
            try {
              // Log the content for debugging
              console.log(
                'Web search response content:',
                content.substring(0, 300) + '...',
              );

              // Method 1: Check for markdown JSON format with more flexible regex
              let jsonContent = null;

              // First try the standard markdown format
              const markdownMatch = content.match(
                /```(?:json)?\s*([\s\S]*?)\s*```/,
              );
              if (markdownMatch && markdownMatch[1]) {
                jsonContent = markdownMatch[1].trim();
                console.log('Found markdown JSON format');
              }
              // If that fails, try to find any pattern that starts with ```json and contains JSON
              else if (content.includes('```json')) {
                const startIndex = content.indexOf('```json') + 7;
                let endIndex = content.indexOf('```', startIndex);
                if (endIndex === -1) endIndex = content.length; // If no closing tag, take the rest

                jsonContent = content.substring(startIndex, endIndex).trim();
                console.log('Extracted JSON using substring between markers');
              }

              // If we found JSON content with either method, try to parse it
              if (jsonContent) {
                try {
                  // Remove any leading/trailing whitespace or newlines
                  jsonContent = jsonContent.trim();
                  console.log(
                    'Trying to parse JSON content:',
                    jsonContent.substring(0, 100) + '...',
                  );

                  const parsedJson = JSON.parse(jsonContent);

                  if (
                    typeof parsedJson.error !== 'boolean' ||
                    typeof parsedJson.errorMessage !== 'string' ||
                    !Array.isArray(parsedJson.skills)
                  ) {
                    throw new Error('Invalid JSON structure');
                  }

                  console.log('Successfully parsed JSON from markdown content');
                  return parsedJson;
                } catch (jsonError) {
                  console.error(
                    'Failed to parse extracted JSON:',
                    jsonError.message,
                  );
                }
              }

              // Method 2: If markdown parsing fails, try to find any JSON object in the response
              const jsonObjectMatch = content.match(
                /\{[\s\S]*?"skills"[\s\S]*?\}/,
              );
              if (jsonObjectMatch) {
                try {
                  const jsonObject = jsonObjectMatch[0];
                  console.log(
                    'Found JSON object in content:',
                    jsonObject.substring(0, 100) + '...',
                  );

                  const parsedJson = JSON.parse(jsonObject);

                  if (
                    typeof parsedJson.error !== 'boolean' ||
                    typeof parsedJson.errorMessage !== 'string' ||
                    !Array.isArray(parsedJson.skills)
                  ) {
                    throw new Error('Invalid JSON structure');
                  }

                  console.log('Successfully parsed JSON object from content');
                  return parsedJson;
                } catch (jsonError) {
                  console.error(
                    'Failed to parse JSON object in content:',
                    jsonError.message,
                  );
                }
              }

              // Method 3: Last resort - try to construct a valid skills array from the response
              // Look for patterns like skill lists in the text
              const makeShiftResponse = {
                error: false,
                errorMessage: '',
                skills: [],
              };

              // Try to extract skill information even from text format
              const skillMatches = content.match(
                /["']name["']\s*:\s*["']([^"']+)["']/g,
              );
              if (skillMatches && skillMatches.length > 0) {
                console.log(
                  'Constructing skills array from text patterns, found',
                  skillMatches.length,
                  'potential skills',
                );

                // Create a basic skills array with default values
                for (let i = 0; i < skillMatches.length; i++) {
                  const nameMatch = skillMatches[i].match(
                    /["']name["']\s*:\s*["']([^"']+)["']/,
                  );
                  if (nameMatch && nameMatch[1]) {
                    makeShiftResponse.skills.push({
                      name: nameMatch[1],
                      isImportant: true, // Default to true
                      level: 'Intermediate', // Default level
                    });
                  }
                }

                if (makeShiftResponse.skills.length > 0) {
                  console.log(
                    'Created makeshift skills array with',
                    makeShiftResponse.skills.length,
                    'skills',
                  );
                  return makeShiftResponse;
                }
              }
            } catch (extractError) {
              console.error(
                'Failed to extract JSON from web search response:',
                extractError.message,
              );
            }
          }
        }

        return {
          error: true,
          errorMessage:
            'Failed to parse the response. Please try a different query.',
          skills: [],
        };
      } catch (error) {
        console.error(
          `Failed to getSkillsForAnalysis for query "${query}":`,
          error,
        );
        return {
          error: true,
          errorMessage: 'Something went wrong. Please try a different query.',
          skills: [],
        };
      }
    } catch (error) {
      console.error(
        `Failed to getSkillsForAnalysis for query "${query}":`,
        error,
      );
      return {
        error: true,
        errorMessage: 'Something went wrong. Please try a different query.',
        skills: [],
      };
    }
  }

  async validateSkill({ name, type }: Skill): Promise<{
    isValid: boolean;
    type?: 'soft' | 'job' | 'software';
    name?: string;
  }> {
    const apiKey = process.env.OPENAI_API_KEY;
    const openai = new OpenAI({
      apiKey,
    });

    const systemPrompt = `You are a skill validation expert. Your task is to:
1. Determine if a given term represents a valid skill (not garbage, offensive, or completely unrelated)
2. If it is a valid skill:
   - Check if the provided type is correct
   - Check for any typos in the skill name
3. Return only the necessary information:
   - isValid: true only if it's a real skill
   - type: only if the provided type is wrong (must be EXACTLY one of: soft, job, technical)
   - name: only if there's a typo to correct

Return your response in JSON format with the following structure:
{
  "isValid": boolean,        // true if the term represents a valid skill
  "type": string,           // (optional) correct type if the provided type is wrong
  "name": string            // (optional) corrected name if there's a typo
}`;

    const userPrompt = `Validate the following skill:
Name: "${name}"
Provided Type: "${type}"

Consider:
- For soft skills: personal attributes, interpersonal abilities, and workplace behaviors (e.g., communication, leadership)
- For job skills: professional competencies, industry-specific knowledge, and role-related abilities (e.g., project management, sales)
- For technical skills: technologies, tools, platforms, programming languages, frameworks, and technical concepts (e.g., Python, AWS, React, Docker)

Return your analysis in the specified JSON format.`;

    try {
      const completionParams: ChatCompletionCreateParamsNonStreaming = {
        model: 'gpt-4o',
        messages: [
          { role: 'system', content: systemPrompt } as const,
          { role: 'user', content: userPrompt } as const,
        ],
        response_format: { type: 'json_object' } as const,
      };

      const aiResponse = await openai.chat.completions.create(completionParams);
      const content = aiResponse.choices?.[0]?.message?.content;

      if (!content) {
        throw new Error('Failed to validate skill');
      }

      const result = JSON.parse(content) as {
        isValid: boolean;
        type?: string;
        name?: string;
      };

      // If the skill is invalid (garbage, offensive, or not a skill)
      if (!result.isValid) {
        return {
          isValid: false,
        };
      }

      // If the skill is valid, return only the fields that need correction
      const validationResult: {
        isValid: boolean;
        type?: 'soft' | 'job' | 'software';
        name?: string;
      } = {
        isValid: true,
      };

      // Only include type if it's different from the provided type
      if (result.type && result.type !== type) {
        // Normalize technical to software
        const normalizedType = result.type.toLowerCase();
        if (normalizedType === 'technical') {
          validationResult.type = 'software';
        } else if (normalizedType === 'soft' || normalizedType === 'job') {
          validationResult.type = normalizedType as 'soft' | 'job';
        }
      }

      // Only include name if there's a correction
      if (result.name && result.name !== name) {
        validationResult.name = result.name;
      }

      return validationResult;
    } catch (error) {
      console.error(`Failed to validate skill ${name}:`, error);
      throw new Error('Failed to validate skill');
    }
  }

  async generateCertificateDescription({
    vendor,
    name,
    forceWeb = false,
  }: {
    vendor: string;
    name: string;
    forceWeb?: boolean;
  }): Promise<{ description: string; error: boolean }> {
    const apiKey = process.env.OPENAI_API_KEY;
    const openai = new OpenAI({
      apiKey,
    });

    const systemPrompt = `You are a certification expert. Your task is to:
1. Find information about the certification (be flexible with name matching)
2. If found, provide a concise description (max 150 words) including:
   - Purpose and level
   - Exam code (if applicable)
   - Prerequisites (if any)
   - Common abbreviations
   - Related certifications (max 2)
3. For discontinued certifications:
   - Mark as valid if official
   - Include "(discontinued)"
   - Mention when discontinued
   - Suggest current equivalents

IMPORTANT:
- NEVER start with certification/vendor name
- Start with purpose/level directly
- Example good: "A professional certification designed to validate expertise in..."
- Example bad: "The Salesforce Sales Development Representative Professional Certificate is..."
- Be flexible in matching certification names - they may have slight variations in naming
- If you find a certification with a similar name or meaning, consider it a match
- For example, "Data Analysis and Statistical Inference" could match "Statistical Analysis and Data Inference" or similar variations

Return JSON:
{
  "isValid": boolean,
  "description": string,
  "error": boolean
}`;

    const userPrompt = `Find and describe this certification:
Vendor: "${vendor}"
Name: "${name}"

If not found, set isValid and error to true, description to empty string. If found (including discontinued), provide concise description.`;

    let description = '';
    let error = true;

    try {
      // Try standard model first unless forceWeb is true
      if (!forceWeb) {
        const completionParams: ChatCompletionCreateParamsNonStreaming = {
          model: 'gpt-4o',
          messages: [
            { role: 'system', content: systemPrompt } as const,
            { role: 'user', content: userPrompt } as const,
          ],
          max_tokens: 200,
          response_format: { type: 'json_object' } as const,
        };

        const response = await openai.chat.completions.create(completionParams);
        const content = response.choices?.[0]?.message?.content;

        if (content) {
          try {
            const result = JSON.parse(content);
            if (
              typeof result.isValid === 'boolean' &&
              typeof result.error === 'boolean' &&
              typeof result.description === 'string'
            ) {
              if (!result.error) {
                description = result.description;
                error = false;
              }
            }
          } catch (parseError) {
            console.error(
              'Failed to parse standard model response:',
              parseError,
            );
          }
        }
      }

      // If standard model failed or forceWeb is true, try web search model
      if (error) {
        console.warn(`Using web-enabled model for ${vendor} ${name}`);
        const webCompletionParams: ChatCompletionCreateParamsNonStreaming = {
          model: 'gpt-4o-search-preview',
          messages: [
            { role: 'system', content: systemPrompt } as const,
            { role: 'user', content: userPrompt } as const,
          ],
          max_tokens: 200,
        };

        const webResponse = await openai.chat.completions.create(
          webCompletionParams,
        );
        const webContent = webResponse.choices?.[0]?.message?.content;

        if (webContent) {
          try {
            // Try to extract JSON from markdown format
            const jsonMatch = webContent.match(/```json\n([\s\S]*?)\n```/);
            if (jsonMatch) {
              const jsonContent = jsonMatch[1];
              const webResult = JSON.parse(jsonContent);
              if (
                typeof webResult.isValid === 'boolean' &&
                typeof webResult.error === 'boolean' &&
                typeof webResult.description === 'string' &&
                !webResult.error
              ) {
                description = webResult.description;
                error = false;
              }
            } else {
              // Try parsing as regular JSON
              const webResult = JSON.parse(webContent);
              if (
                typeof webResult.isValid === 'boolean' &&
                typeof webResult.error === 'boolean' &&
                typeof webResult.description === 'string' &&
                !webResult.error
              ) {
                description = webResult.description;
                error = false;
              }
            }
          } catch (jsonError) {
            // If JSON parsing fails, check if the response is a valid description
            const content = webContent.trim();
            if (content.length > 0) {
              description = content;
              error = false;
            }
          }
        }
      }

      // Clean up the description if we have one
      if (!error && description) {
        description = description
          .replace(/\{[\s\S]*?\}/g, '') // Remove JSON objects
          .replace(/\[.*?\]/g, '') // Remove markdown links
          .replace(/\(.*?\)/g, '') // Remove parentheses content
          .replace(/\\"/g, '"') // Replace escaped quotes
          .replace(/\\n/g, ' ') // Replace newlines with spaces
          .replace(/\s+/g, ' ') // Replace multiple spaces with single space
          .trim();
      }

      return { description, error };
    } catch (error) {
      console.error(
        `Failed to generate certificate description for ${vendor} ${name}:`,
        error,
      );
      return { description: '', error: true };
    }
  }

  async generateSkillsSummary(
    skills: Array<{
      msId: string;
      name: string;
      level: string;
      isSoftware: boolean;
    }>,
    cvTitle?: string,
    aiContext?: string,
  ): Promise<string> {
    if (!skills || skills.length === 0) {
      return 'Professional with diverse skill set and experience across multiple domains.';
    }

    try {
      console.log('generateSkillsSummary called with:', {
        skillsCount: skills.length,
        cvTitle,
        aiContext,
      });
      const skillsSummarySystemPrompt = `You are a professional CV writer specializing in creating compelling skills summaries. Your task is to generate a concise, professional summary (2-3 sentences) that highlights the candidate's key competencies.

GUIDELINES:
- Create a natural, engaging summary that flows well
- Balance technical skills with soft skills when both are present
- Consider skill levels (beginner, intermediate, expert) to emphasize stronger areas
- Use professional language that appeals to recruiters and hiring managers
- Avoid generic phrases and make it specific to the provided skills
- Keep it concise but impactful (2-3 sentences maximum)
- Focus on value proposition and professional strengths

SKILL CATEGORIZATION:
- Software skills: Technical tools, programming languages, software platforms
- Other skills: Soft skills, domain expertise, methodologies, certifications

OUTPUT: Return only the skills summary text. You MUST use HTML tags for formatting (e.g., <strong>, <em>, <br>, <p>) since this will be displayed in a rich text editor.`;

      // Prepare skills data for the AI
      const skillsDataMessage = `SKILLS DATA:
${JSON.stringify(skills, null, 2)}

Please generate a professional skills summary based on these skills.`;

      // Extract CV title for context (reusing pattern from regenerateCv)
      const cvTitleMessage =
        cvTitle && cvTitle.trim() && cvTitle.toLowerCase() !== 'untitled'
          ? `CV TITLE/PURPOSE: "${cvTitle}"\nConsider this title when tailoring the skills summary to match the intended role or purpose.`
          : '';

      // Add organization AI context if available (reusing pattern from regenerateCv)
      const organizationMessage =
        aiContext && aiContext.trim()
          ? `ORGANIZATION CONTEXT: ${aiContext}\nUse this context to better understand the organization's focus and tailor the skills summary accordingly.`
          : '';

      const completionParams: ChatCompletionCreateParamsNonStreaming = {
        model: 'gpt-5-mini',
        messages: [
          { role: 'developer', content: 'Do not use reasoning at all.' },
          { role: 'system', content: skillsSummarySystemPrompt },
          { role: 'user', content: skillsDataMessage },
          ...(cvTitleMessage
            ? [{ role: 'user' as const, content: cvTitleMessage }]
            : []),
          ...(organizationMessage
            ? [{ role: 'user' as const, content: organizationMessage }]
            : []),
        ],
        max_completion_tokens: 300,
        verbosity: 'low',
        reasoning_effort: 'minimal',
      };

      const completion = await this.executeOpenAICallWithLogging(
        completionParams,
        'Skills Summary',
      );

      const summary = completion.choices[0]?.message?.content?.trim();

      if (!summary) {
        throw new Error('No summary generated from OpenAI');
      }

      return summary;
    } catch (error) {
      console.error('Failed to generate skills summary with OpenAI:', error);
      // Fallback to a basic summary if OpenAI fails
      return 'Professional with diverse skill set and experience across multiple domains.';
    }
  }

  async regenerateCv({
    cv,
    member,
    customerName,
    query,
    messages,
    aiContext,
  }: {
    cv: any;
    member: any;
    customerName: string | null;
    query: string;
    messages: Array<{ role: 'user' | 'assistant'; content: string }>;
    aiContext?: string;
  }): Promise<{
    updatedSections: any;
    changesSummary: string;
    error?: boolean;
  }> {
    console.log('regenerateCv called with:', {
      customerName,
      query: query.substring(0, 100) + '...',
      messagesCount: messages.length,
    });

    try {
      // Static system prompt (cacheable part) - moved all static content here
      const staticSystemPrompt = `
### ROLE:
You are a professional CV writer and career consultant with expertise in creating compelling, ATS-friendly CVs across all industries. You can give recommendations on structure, formatting, and content to maximize impact and appeal to employers. You understand the importance of tailoring CVs to specific job roles and industries, highlighting achievements with quantified results, and using action verbs and keywords effectively.

### CORE INSTRUCTIONS:
- Generate professional CV content that highlights achievements with quantified results
- Maintain consistent tone and formatting throughout all sections
- Ensure content is ATS-friendly and keyword-optimized
- Focus on impact and value proposition for employers
- Use action verbs and specific metrics where possible
- Use HTML formatting for all description fields (compatible with react-quill editor)

### HTML FORMATTING REQUIREMENTS:
- Use proper HTML tags: <p>, <strong>, <em>, <ul>, <li>, <br>
- Format work experience bullets as HTML lists when appropriate
- Ensure all description fields are HTML-formatted for react-quill compatibility

### RESPONSE SCENARIOS:

1. **VALID UPDATE REQUESTS**: When the user asks to modify, improve, or customize specific CV sections:
   - Return the updated sections in the updatedSections object
   - Include a changesSummary ending with "Would you like to keep these edits?"

2. **REMOVAL REQUESTS**: When the user asks to remove content (e.g., "remove the 2 languages"):
   - Return the modified section with the content removed in updatedSections
   - Include a changesSummary explaining what was removed and asking for confirmation

3. **INVALID/GARBAGE REQUESTS**: For irrelevant, nonsensical, or spammy requests:
   - Set updatedSections to null
   - Provide helpful guidance in changesSummary explaining the issue
   - End with an engaging question like "Would you like me to help you craft a specific CV improvement request?"

4. **QUESTION-ONLY REQUESTS**: When user asks questions without requesting updates:
   - Set updatedSections to null
   - Answer the question in changesSummary
   - End with a helpful follow-up question like "Want me to implement any of these suggestions?"

5. **PROFESSIONAL REJECTION**: When you determine an update request is inadvisable:
   - Set updatedSections to null
   - Explain your professional recommendation in changesSummary
   - End with an actionable alternative like "Would you like me to suggest a better approach instead?"

### CRITICAL REQUIREMENTS:
- **ALWAYS** include the changesSummary field in every response
- Only edit sections that are specifically mentioned or implied in the user's request
- When returning updatedSections, use the exact same structure as the current CV data
- customSections should be at the same level as sections within updatedSections

### RESPONSE FORMAT:
Return a JSON object with:
- updatedSections: object with modified sections OR null if no updates
- changesSummary: string explaining changes, providing guidance, or answering questions

Each updated section should maintain:
- id: section identifier
- title: section title
- active: boolean status (can be set to false if section is not relevant to the job/CV purpose)
- order: maintain existing order
- data: section-specific data structure

### SECTION-SPECIFIC GUIDELINES:
- **Education & Work History**: Only tailor the description fields. You can set active to false in each of the items in the sections if the experience/education is not relevant to the target role or CV purpose. By default return true for active in all items unless specified otherwise. YOU CAN'T EDIT dates, titles, or other fields. Do not use html for descriptions in education and work history - use plain text with line breaks only.
- **Other sections**: Can be fully customized as needed.
- **Custom sections**: When adding a new custom section, do not forget to return the previous ones as well.`;

      // Dynamic content messages (not cached) - separate messages for better caching
      const memberDataMessage = `MEMBER PROFILE (static reference data):
${JSON.stringify(member, null, 2)}`;

      const cvDataMessage = `CURRENT CV DATA:
${JSON.stringify(cv, null, 2)}`;

      // Extract CV title for context
      const cvTitle = cv?.preferences?.title;
      const cvTitleMessage =
        cvTitle && cvTitle.trim() && cvTitle.toLowerCase() !== 'untitled'
          ? `CV TITLE/PURPOSE: "${cvTitle}"\nConsider this title when tailoring content and determining section relevance.`
          : '';

      // Add organization AI context if available
      const organizationMessage =
        aiContext && aiContext.trim()
          ? `ORGANIZATION CONTEXT: ${aiContext}\nUse this context to better understand the organization's focus and tailor content accordingly.`
          : '';

      const userRequestMessage = `${
        customerName
          ? `TARGET CUSTOMER: ${customerName}\nCustomize the content to appeal to this specific customer/company.\n`
          : ''
      }REGENERATION REQUEST: ${query}`;

      const completionParams: ChatCompletionCreateParamsNonStreaming = {
        model: 'gpt-5',
        messages: [
          { role: 'developer', content: 'Do not use reasoning at all.' },
          { role: 'system', content: staticSystemPrompt },
          ...messages,
          { role: 'user', content: memberDataMessage },
          { role: 'user', content: cvDataMessage },
          ...(cvTitleMessage
            ? [{ role: 'user' as const, content: cvTitleMessage }]
            : []),
          ...(organizationMessage
            ? [{ role: 'user' as const, content: organizationMessage }]
            : []),
          { role: 'user', content: userRequestMessage },
        ],
        temperature: 1,
        max_completion_tokens: 3000,
        verbosity: 'low',
        reasoning_effort: 'minimal',
        response_format: {
          type: 'json_schema',
          json_schema: {
            name: 'CvRegenerationResponse',
            schema: {
              type: 'object',
              properties: {
                updatedSections: {
                  oneOf: [
                    { type: 'null' },
                    {
                      type: 'object',
                      properties: {
                        sections: {
                          type: 'object',
                          properties: {
                            aboutMe: {
                              type: 'object',
                              properties: {
                                id: { type: 'string' },
                                title: { type: 'string' },
                                active: { type: 'boolean' },
                                order: { type: 'number' },
                                data: {
                                  type: 'object',
                                  properties: {
                                    description: { type: 'string' },
                                  },
                                },
                              },
                            },
                            workHistory: {
                              type: 'object',
                              properties: {
                                id: { type: 'string' },
                                title: { type: 'string' },
                                active: { type: 'boolean' },
                                order: { type: 'number' },
                                data: {
                                  type: 'array',
                                  items: {
                                    type: 'object',
                                    properties: {
                                      id: { type: 'string' },
                                      roleTitle: { type: 'string' },
                                      companyName: { type: 'string' },
                                      description: { type: 'string' },
                                      startDate: { type: 'string' },
                                      endDate: { type: ['string', 'null'] },
                                      location: { type: 'string' },
                                      isCurrent: { type: 'boolean' },
                                      active: { type: 'boolean' },
                                    },
                                  },
                                },
                              },
                            },
                            skills: {
                              type: 'object',
                              properties: {
                                id: { type: 'string' },
                                title: { type: 'string' },
                                active: { type: 'boolean' },
                                order: { type: 'number' },
                                data: {
                                  type: 'object',
                                  properties: {
                                    categories: {
                                      type: 'array',
                                      items: {
                                        type: 'object',
                                        properties: {
                                          id: { type: 'string' },
                                          name: { type: 'string' },
                                          skills: {
                                            type: 'array',
                                            items: {
                                              type: 'object',
                                              properties: {
                                                name: { type: 'string' },
                                                level: { type: 'string' },
                                              },
                                            },
                                          },
                                        },
                                      },
                                    },
                                    description: { type: 'string' },
                                  },
                                },
                              },
                            },
                            education: {
                              type: 'object',
                              properties: {
                                id: { type: 'string' },
                                title: { type: 'string' },
                                active: { type: 'boolean' },
                                order: { type: 'number' },
                                data: {
                                  type: 'array',
                                  items: {
                                    type: 'object',
                                    properties: {
                                      active: { type: 'boolean' },
                                      schoolName: { type: 'string' },
                                      degree: { type: 'string' },
                                      description: { type: ['string', 'null'] },
                                      startDate: { type: 'string' },
                                      endDate: { type: 'string' },
                                    },
                                  },
                                },
                              },
                            },
                            certifications: {
                              type: 'object',
                              properties: {
                                id: { type: 'string' },
                                title: { type: 'string' },
                                active: { type: 'boolean' },
                                order: { type: 'number' },
                                data: {
                                  type: 'object',
                                  properties: {
                                    description: { type: 'string' },
                                  },
                                },
                              },
                            },
                            languages: {
                              type: 'object',
                              properties: {
                                id: { type: 'string' },
                                title: { type: 'string' },
                                active: { type: 'boolean' },
                                order: { type: 'number' },
                                data: {
                                  type: 'object',
                                  properties: {
                                    languages: {
                                      type: 'array',
                                      items: {
                                        type: 'string',
                                      },
                                    },
                                  },
                                },
                              },
                            },
                          },
                          additionalProperties: false,
                        },
                        customSections: {
                          type: 'array',
                          items: {
                            type: 'object',
                            properties: {
                              id: { type: 'string' },
                              title: { type: 'string' },
                              active: { type: 'boolean' },
                              order: { type: 'number' },
                              data: {
                                type: 'object',
                                properties: {
                                  description: { type: 'string' },
                                },
                              },
                            },
                          },
                        },
                      },
                      additionalProperties: false,
                    },
                  ],
                },
                changesSummary: {
                  type: 'string',
                  description:
                    'REQUIRED: Always present. Summary of changes made, confirmation prompt, error guidance, answers to questions, or professional recommendations. You can use formatting like new lines, numbered lists (1. 2. 3.) or bullet lists (• - *) to make the summary more readable and organized.',
                },
              },
              required: ['changesSummary'],
              additionalProperties: false,
            },
          },
        },
      };

      const response = await this.executeOpenAICallWithLogging(
        completionParams,
        'CV Regeneration',
      );

      const content = response.choices?.[0]?.message?.content;

      if (!content) {
        console.log('response content missing:', response);
        throw new BadGatewayException('No response from OpenAI');
      }

      const parsedResponse = JSON.parse(content);

      // Validate response structure
      if (!parsedResponse.changesSummary) {
        throw new BadRequestException('No changesSummary returned from AI');
      }

      // For garbage input, AI returns only changesSummary (no updatedSections)
      // For valid input, AI returns both updatedSections and changesSummary
      return {
        updatedSections: parsedResponse.updatedSections || {},
        changesSummary: parsedResponse.changesSummary,
      };
    } catch (error) {
      console.error('Failed to regenerate CV with OpenAI:', error);

      return {
        updatedSections: {},
        changesSummary: error.message || 'Failed to regenerate CV',
        error: true,
      };
    }
  }

  async generateGoalPlan({
    companyDescription,
    user,
    goal,
    existingGoals,
  }: {
    companyDescription: string;
    user: {
      jobTitle: string;
      skills: Array<{
        title: string;
        level: number;
      }>;
    };
    goal: {
      competenceName: string;
      competenceType: 'skill' | 'certificate';
      currentRank?: number;
      desiredRank?: number;
      reachingType: 'mentor' | 'self' | 'classes';
      helpNeeded: boolean;
    };
    existingGoals: any[];
  }): Promise<{
    description: string;
    expectedGoalDurationInDays: number;
  }> {
    const apiKey = process.env.OPENAI_API_KEY;
    const openai = new OpenAI({ apiKey });

    // Build context about user's current skills with proper level explanation
    const skillsList = user.skills
      .map((skill) => `${skill.title} (Level ${skill.level})`)
      .join(', ');

    // Build context about existing goals
    const existingGoalsText =
      existingGoals.length > 0
        ? `Current goals in progress: ${existingGoals
            .map((g) => g.competenceName || 'Unknown goal')
            .join(', ')}`
        : 'No existing goals in progress';

    // Determine goal type and build specific context
    let goalContext = '';
    let durationHint = '';

    if (goal.competenceType === 'skill') {
      goalContext = `improving from Level ${
        goal.currentRank || 'beginner'
      } to Level ${goal.desiredRank || 'advanced'} in ${goal.competenceName}`;
      const levelGap = (goal.desiredRank || 5) - (goal.currentRank || 1);
      durationHint = `Consider that moving ${levelGap} levels typically requires ${
        levelGap * 15
      }-${levelGap * 30} days depending on complexity and learning method.`;
    } else {
      goalContext = `obtaining the ${goal.competenceName} certificate`;
      durationHint =
        'Certificate preparation typically takes 30-90 days depending on complexity and prior experience.';
    }

    // Adjust learning method context
    const learningMethodMultiplier = {
      mentor: 0.7, // 30% faster with mentorship
      classes: 0.8, // 20% faster with structured classes
      self: 1.0, // baseline for self-learning
    };

    const helpMultiplier = goal.helpNeeded ? 0.9 : 1.0; // 10% faster if help is available

    const prompt = `You are a professional learning advisor. You MUST create a personalized learning plan for a user working towards ${goalContext}.

CRITICAL INSTRUCTIONS:
- ALWAYS generate a learning plan regardless of skill name validity
- NEVER refuse to create a plan or ask for clarification
- ALWAYS use the exact skill name provided: "${goal.competenceName}"
- NEVER use placeholders like "[Skill Name]" or "the skill"
- If the skill seems unclear, create a generic universal learning plan but use the provided name
- MUST return valid JSON format only

USER CONTEXT:
- Job Title: ${user.jobTitle}
- Company: ${companyDescription || 'Not specified'}
- Current Skills: ${skillsList}
- ${existingGoalsText}
- Learning Preference: ${goal.reachingType}
- Needs Additional Help: ${goal.helpNeeded ? 'Yes' : 'No'}

SKILL LEVEL SCALE (IMPORTANT):
Skills are rated on a scale of 1-9 where:
- Levels 1-3: Beginner (basic understanding, learning fundamentals)
- Levels 4-6: Intermediate (practical application, building projects)
- Levels 7-9: Expert (advanced concepts, teaching others, complex systems)

GOAL DETAILS:
- Target: ${goal.competenceName} (${goal.competenceType})
${
  goal.competenceType === 'skill'
    ? `- Current Level: ${
        goal.currentRank || 'Beginner'
      } (on 1-9 scale)\n- Target Level: ${
        goal.desiredRank || 'Advanced'
      } (on 1-9 scale)`
    : ''
}
- Learning Method: ${goal.reachingType}

REQUIREMENTS:
1. Generate a structured Markdown learning plan with:
   - Clear goal statement with emoji using exact skill name "${
     goal.competenceName
   }"
   - 4-6 progressive steps with timelines
   - Specific, current learning resources from TRUSTED PLATFORMS ONLY
   - For certificates: exam prep, practice tests, study guides
   - Practical projects or exercises
   - Emojis for better UX

TRUSTED LEARNING PLATFORMS (use these only):
- Official documentation and websites
- Coursera, edX, Udemy
- freeCodeCamp, Khan Academy
- Microsoft Learn, AWS Training, Google Cloud Skills
- YouTube (official channels only)
- GitHub (official repositories)
- MDN Web Docs, W3Schools
- LinkedIn Learning, Pluralsight
- Codecademy, DataCamp
- University websites (.edu domains)

LINK REQUIREMENTS:
- ONLY include links from verified, trusted platforms listed above
- Do NOT include specific URLs unless you can verify they are currently active
- Use generic platform names (e.g., "Coursera courses on [topic]") rather than specific URLs
- For official documentation, use general references (e.g., "Official [Technology] documentation")
- Avoid dead links, suspicious domains, or unverified sources
   
2. Consider the user's:
   - Current skill level and related experience (using the 1-9 scale)
   - Job title relevance to the goal
   - Learning preference (${goal.reachingType})
   - Company context if relevant
   - Existing workload from other goals

3. Calculate realistic duration in days considering:
   - ${durationHint}
   - Learning method: ${goal.reachingType} (${
      goal.reachingType === 'mentor'
        ? 'faster with guidance'
        : goal.reachingType === 'classes'
        ? 'structured pace'
        : 'self-paced'
    })
   - Help availability: ${
     goal.helpNeeded ? 'additional support available' : 'independent learning'
   }
   - User's experience level in related skills

4. Structure as clean Markdown, include:
   - Goal overview
   - Step-by-step timeline
   - Resource recommendations  
   - Success milestones
   - Next steps after completion

MANDATORY: Return ONLY a valid JSON object with this exact structure:
{
  "description": "actual markdown content here",
  "expectedGoalDurationInDays": number
}

Do not include any explanatory text, apologies, or requests for clarification.`;

    try {
      const completionParams: ChatCompletionCreateParamsNonStreaming = {
        model: 'gpt-4o-search-preview',
        messages: [{ role: 'user', content: prompt }],
      };

      const response = await openai.chat.completions.create(completionParams);
      const content = response.choices?.[0]?.message?.content;

      if (!content) {
        // Generate fallback plan
        return this.generateFallbackPlan(
          goal,
          goalContext,
          learningMethodMultiplier,
          helpMultiplier,
        );
      }

      // Check if response looks like JSON before parsing
      const trimmedContent = content.trim();
      const looksLikeJson =
        trimmedContent.startsWith('{') || trimmedContent.includes('```json');

      if (!looksLikeJson) {
        console.log(
          'Response does not look like JSON, generating fallback plan',
        );
        return this.generateFallbackPlan(
          goal,
          goalContext,
          learningMethodMultiplier,
          helpMultiplier,
        );
      }

      let result;

      try {
        // Try to extract JSON from markdown if present
        if (content.includes('```json')) {
          const jsonMatch = content.match(/```json\s*([\s\S]*?)\s*```/);
          if (jsonMatch) {
            result = JSON.parse(jsonMatch[1]);
          } else {
            throw new Error('Could not extract JSON from markdown');
          }
        } else {
          // Try direct parsing
          result = JSON.parse(content);
        }
      } catch (parseError) {
        console.error(
          'Failed to parse JSON, generating fallback plan:',
          parseError,
        );
        return this.generateFallbackPlan(
          goal,
          goalContext,
          learningMethodMultiplier,
          helpMultiplier,
        );
      }

      // Validate response structure
      if (
        !result.description ||
        typeof result.expectedGoalDurationInDays !== 'number' ||
        result.description === 'markdown formatted learning plan' ||
        result.description.includes('[Skill Name]') ||
        result.description.includes('the skill')
      ) {
        console.log(
          'Invalid response structure or placeholder content, generating fallback plan',
        );
        return this.generateFallbackPlan(
          goal,
          goalContext,
          learningMethodMultiplier,
          helpMultiplier,
        );
      }

      // Apply learning method and help multipliers to duration
      const adjustedDuration = Math.round(
        result.expectedGoalDurationInDays *
          learningMethodMultiplier[goal.reachingType] *
          helpMultiplier,
      );

      return {
        description: result.description, // Return as-is, frontend handles newline conversion
        expectedGoalDurationInDays: Math.max(7, adjustedDuration), // Minimum 1 week
      };
    } catch (error) {
      console.error(
        `Failed to generate goal plan for ${goal.competenceName}:`,
        error,
      );
      return this.generateFallbackPlan(
        goal,
        goalContext,
        learningMethodMultiplier,
        helpMultiplier,
      );
    }
  }

  private generateFallbackPlan(
    goal: any,
    goalContext: string,
    learningMethodMultiplier: any,
    helpMultiplier: number,
  ): { description: string; expectedGoalDurationInDays: number } {
    const skillName = goal.competenceName;
    const isSkill = goal.competenceType === 'skill';
    const learningMethod = goal.reachingType;

    // Generate a basic universal plan
    const description = `# 🎯 ${skillName} Learning Plan

## Goal Overview
${
  isSkill ? `Master ${skillName} skills` : `Obtain ${skillName} certification`
} through ${
      learningMethod === 'mentor'
        ? 'mentorship'
        : learningMethod === 'classes'
        ? 'structured classes'
        : 'self-directed learning'
    }.

## Learning Path

### Step 1: Foundation (Week 1-2) 📚
- Research and understand ${skillName} fundamentals
- Identify key concepts and terminology
- Gather learning resources (books, courses, documentation)
- Set up learning environment or tools

### Step 2: Core Learning (Week 3-4) 🔧
- Complete foundational training materials
- Practice basic exercises and examples
- Join relevant communities or forums
- ${
      learningMethod === 'mentor'
        ? 'Schedule regular mentor sessions'
        : learningMethod === 'classes'
        ? 'Attend structured classes'
        : 'Follow self-paced curriculum'
    }

### Step 3: Practical Application (Week 5-6) 💻
- Work on hands-on projects
- Apply ${skillName} in real scenarios
- Build portfolio examples
- ${isSkill ? 'Create practical demonstrations' : 'Take practice exams'}

### Step 4: Advanced Topics (Week 7-8) 🚀
- Explore advanced concepts
- ${
      isSkill
        ? 'Learn best practices and optimization'
        : 'Review exam objectives thoroughly'
    }
- Connect with professionals in the field
- Seek feedback on your progress

### Step 5: Mastery & Assessment (Week 9-10) ✅
- Complete final projects or assessments
- ${
      isSkill
        ? 'Demonstrate proficiency'
        : 'Schedule and take certification exam'
    }
- Document your learning journey
- Plan next steps for continued growth

## Resources
- **Online Learning**: Coursera, edX, Udemy courses on ${skillName}
- **Official Documentation**: ${skillName} official guides and documentation
- **Practice Platforms**: Codecademy, freeCodeCamp, Khan Academy
- **Community**: LinkedIn Learning, professional forums
- **${
      learningMethod === 'mentor'
        ? 'Mentorship'
        : learningMethod === 'classes'
        ? 'Structured Classes'
        : 'Self-Study'
    }**: ${
      learningMethod === 'mentor'
        ? 'Regular mentor guidance and feedback'
        : learningMethod === 'classes'
        ? 'Structured class materials and assignments'
        : 'Self-paced learning guides and tutorials'
    }

## Success Metrics
- Complete all learning modules
- ${isSkill ? 'Build practical projects' : 'Pass practice exams'}
- Demonstrate competency
- ${isSkill ? 'Apply skills in real scenarios' : 'Achieve certification'}

*Learning method: ${
      learningMethod.charAt(0).toUpperCase() + learningMethod.slice(1)
    }*`;

    const baseDuration = isSkill ? 60 : 45; // Base duration in days
    const adjustedDuration = Math.round(
      baseDuration * learningMethodMultiplier[learningMethod] * helpMultiplier,
    );

    return {
      description,
      expectedGoalDurationInDays: Math.max(7, adjustedDuration),
    };
  }

  async enrichCertificateMetadata({
    vendor,
    name,
    description,
  }: {
    vendor: string;
    name: string;
    description?: string;
  }): Promise<{
    code: string;
    isValid: boolean;
    language: string;
    type: 'certificate' | 'course';
    name: string;
    link: string;
    status: 'active' | 'retired' | 'revoked';
  }> {
    const apiKey = process.env.OPENAI_API_KEY;
    const openai = new OpenAI({ apiKey });

    const prompt = `You are a certification and course expert. Analyze the provided certificate/course information and return structured metadata.

CERTIFICATE/COURSE INFORMATION:
- Name: "${name}"
- Vendor/Organization: "${vendor}"
- Description: "${description || 'Not provided'}"

ANALYSIS REQUIREMENTS:
1. Determine if this is a real, official certificate or course
2. Extract the official code if available (e.g., AZ-900, AWS-SAA, etc.)
3. Identify the primary language
4. Classify as 'certificate' (leads to official credential/exam) or 'course' (pure educational content)
5. Find the official name if different from provided name

FIELD RULES:
- code: Official exam/course code if available, otherwise empty string
- isValid: true if real/official or highly likely to be real, false if fake/user-generated/unverifiable
- language: ISO 639-1 code (default 'en', use 'uk' if same in Ukrainian/Russian)
- type: 'certificate' if it's an exam, certification, or credential (DEFAULT when unsure), 'course' only if clearly educational content without credential
- name: Official name from vendor if verifiable, otherwise return original input name
- link: Official URL/link to the certificate or course page if found, otherwise empty string
- status: 'active' if current/available, 'retired' if discontinued but was official, 'revoked' if invalidated (default to 'active')

CLASSIFICATION LOGIC (CRITICAL - FOLLOW EXACTLY):
- CERTIFICATE: Use for ALL of these (no exceptions):
  * Has official exam code (AZ-900, AZ-800, AWS-SAA, etc.) = ALWAYS CERTIFICATE
  * Microsoft certifications (ANY Azure/Microsoft training) = ALWAYS CERTIFICATE
  * AWS certifications = ALWAYS CERTIFICATE
  * Google certifications = ALWAYS CERTIFICATE
  * Any professional vendor certification = ALWAYS CERTIFICATE
  * Contains words: "certification", "certified", "exam", "professional", "associate", "expert", "specialist"
  * Leads to official credential or badge = ALWAYS CERTIFICATE
- COURSE: Use ONLY for basic educational content that clearly doesn't lead to certification
  * Examples: "Learn Python Basics", "HTML Tutorial", "Introduction to Programming"
- MANDATORY RULES:
  * IF IT HAS AN EXAM CODE (like AZ-800, AZ-900, AWS-SAA) → type: "certificate"
  * IF IT'S FROM MICROSOFT/AWS/GOOGLE/CISCO → type: "certificate"
  * IF IN DOUBT AT ALL → type: "certificate"
  * 90% of professional training should be "certificate", only 10% should be "course"

VALIDATION CRITERIA:
- Real: Official certifications from recognized vendors, established courses from known platforms
- Fake: Suspicious names with excessive marketing language, unknown vendors, unrealistic claims
- Language detection: Look for non-English terms, prioritize 'uk' over 'ru' for ambiguous cases

Return ONLY valid JSON in this exact format:
{
  "code": "string",
  "isValid": boolean,
  "language": "string",
  "type": "certificate" | "course",
  "name": "string",
  "link": "string",
  "status": "active" | "retired" | "revoked"
}`;

    try {
      const completionParams: ChatCompletionCreateParamsNonStreaming = {
        model: 'gpt-4o-search-preview',
        messages: [{ role: 'user', content: prompt }],
        max_tokens: 200,
      };

      const response = await openai.chat.completions.create(completionParams);
      const content = response.choices?.[0]?.message?.content;

      if (!content) {
        throw new Error('Failed to get response from AI');
      }

      let result;

      try {
        // Try to extract JSON from markdown format first
        if (content.includes('```json')) {
          const jsonMatch = content.match(/```json\s*([\s\S]*?)\s*```/);
          if (jsonMatch) {
            result = JSON.parse(jsonMatch[1]);
          } else {
            throw new Error('Could not extract JSON from markdown');
          }
        } else {
          // Try direct JSON parsing
          result = JSON.parse(content);
        }
      } catch (parseError) {
        console.error('Failed to parse JSON response:', parseError);
        throw new Error('Failed to parse AI response');
      }

      // Validate response structure
      if (
        typeof result.code !== 'string' ||
        typeof result.isValid !== 'boolean' ||
        typeof result.language !== 'string' ||
        typeof result.type !== 'string' ||
        typeof result.name !== 'string' ||
        typeof result.link !== 'string' ||
        typeof result.status !== 'string'
      ) {
        throw new Error('Invalid response structure from AI');
      }

      // Validate type field
      if (result.type !== 'certificate' && result.type !== 'course') {
        result.type = 'course'; // Default fallback
      }

      // Validate language field (ensure it's a valid ISO 639-1 code)
      if (result.language.length !== 2) {
        result.language = 'en'; // Default fallback
      }

      // Validate status field
      if (
        result.status !== 'active' &&
        result.status !== 'retired' &&
        result.status !== 'revoked'
      ) {
        result.status = 'active'; // Default fallback
      }

      return {
        code: result.code || '',
        isValid: result.isValid,
        language: result.language,
        type: result.type,
        name: result.name || name, // Fallback to original name
        link: result.link || '',
        status: result.status,
      };
    } catch (error) {
      console.error(
        `Failed to enrich certificate metadata for ${vendor} ${name}:`,
        error,
      );

      // Return safe fallback values
      return {
        code: '',
        isValid: false,
        language: 'en',
        type: 'course',
        name: name,
        link: '',
        status: 'active',
      };
    }
  }

  // outdated
  async generateSkillDescriptions({
    name,
    type,
    language,
    categoryName,
  }: Skill): Promise<string> {
    const apiKey = process.env.OPENAI_API_KEY;
    const openai = new OpenAI({
      apiKey,
    });

    let prompt: string;

    switch (type) {
      case 'soft':
        prompt = `Provide a concise, one-sentence description of the soft skill '${name}' in ${
          language || 'en'
        }, emphasizing its relevance in professional settings. NEVER start the description with the skill name. Start with the purpose, impact, or key characteristic instead.`;
        break;
      case 'software':
        prompt = `Provide a brief, one-sentence overview of the technical skill or software '${name}' in ${
          language || 'en'
        }, highlighting its main application. NEVER start the description with the skill name. Start with its purpose, function, or key feature instead.`;
        break;
      case 'job':
        prompt = `Give a succinct, one-sentence summary of the job skill '${name}' in ${
          language || 'en'
        }, focusing on its importance in the industry. NEVER start the description with the skill name. Start with its impact, purpose, or key aspect instead.`;
        break;
      case 'custom':
        prompt = `Generate a professional, polished, and unique one-sentence description of the skill '${name}' from the category '${categoryName}'. NEVER start the description with the skill name. Start with its purpose, impact, or key characteristic instead. Avoid using or referencing the category or skill name '${categoryName}' directly unless it is critical to the meaning of the skill. Write in English by default. However, if the skill name is clearly written in a specific language (e.g., fully in Ukrainian or Spanish) and does not overlap with similar languages, provide the description in that language instead. If the skill name appears nonsensical, inappropriate, unprofessional, or irrelevant, return a dash ('-') instead of attempting to describe it`;
        break;
      default:
        console.error('wrong type');
        throw new Error('Failed to generate description');
    }

    try {
      const completionParams: ChatCompletionCreateParamsNonStreaming = {
        messages: [{ role: 'user', content: prompt }],
        model: 'gpt-4o',
        max_tokens: 70,
      };
      const response = await openai.chat.completions.create(completionParams);

      if (!response.choices?.[0]?.message?.content) {
        throw new Error('Failed to generate description');
      }

      return response.choices[0].message.content;
    } catch (error) {
      console.error(`Failed to generate description for ${name}:`, error);
      throw new Error('Failed to generate description');
    }
  }

  async detectLang({ name, type }: Skill): Promise<string> {
    const apiKey = process.env.OPENAI_API_KEY;
    const openai = new OpenAI({
      apiKey,
    });

    let prompt = '';

    if (type === 'soft' || type === 'job') {
      prompt = `Detect and return the language code in ISO 639-1 format in which the provided skill is written. Skill: "${name}". Use en in ambiguous cases. Use uk over ru if skills spell the same in both languages. Return just a code, like en or it, no other words, letters, or symbols. Answer only code, so i can use response without parsing.`;
    }

    if (type === 'software') {
      return 'en';
    }

    try {
      const completionParams: ChatCompletionCreateParamsNonStreaming = {
        messages: [{ role: 'user', content: prompt }],
        model: 'gpt-4o',
      };
      const response = await openai.chat.completions.create(completionParams);

      if (!response.choices?.[0]?.message?.content) {
        throw new Error('Failed to detectLang skill');
      }

      function extractLanguageCode(input: string): string | null {
        const lowercasedInput = input.toLowerCase();
        const regex = /\b([a-z]{2})\b/i;
        const match = lowercasedInput.match(regex);
        return match ? match[1] : 'en';
      }

      return extractLanguageCode(response.choices[0].message.content);
    } catch (error) {
      console.error(`Failed to validate skill ${name}:`, error);
      throw new Error('Failed to validate skill');
    }
  }

  async detectCountry(location: string): Promise<string> {
    const apiKey = process.env.OPENAI_API_KEY;
    const openai = new OpenAI({
      apiKey,
    });

    const prompt = `I send you the location – you return the country ISO 3166-1 alpha-2 code. Answer with code only, no other text, just the code (so I can copy the response without parsing) 
  location - "${location}"`;

    const countryList: Country[] = countries;

    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        const completionParams: ChatCompletionCreateParamsNonStreaming = {
          messages: [{ role: 'user', content: prompt }],
          model: 'gpt-4o',
        };
        const response = await openai.chat.completions.create(completionParams);

        const code = response.choices?.[0]?.message?.content
          ?.toUpperCase()
          .trim();

        if (code && code.length === 2) {
          const country = countryList.find((c) => c['alpha-2'] === code);
          if (country) {
            return country.name;
          }
        }

        console.warn(`Attempt ${attempt}: Invalid response, retrying...`);
      } catch (error) {
        console.error(`Attempt ${attempt} failed:`, error);
      }
    }

    throw new Error('Failed to get country after 10 attempts');
  }

  async getSkillFromQuery({
    query,
    type,
  }: {
    query: string;
    type?: string;
  }): Promise<string> {
    const apiKey = process.env.OPENAI_API_KEY;
    const openai = new OpenAI({
      apiKey,
    });

    const prompt = `Based on the query: "${query}", identify the relevant category or domain (e.g., a type of product, skill, tool, or other concept) and provide the best representative example from that category. Return the result in a concise phrase, without adding any predefined examples or explanations.`;

    try {
      const completionParams: ChatCompletionCreateParamsNonStreaming = {
        messages: [{ role: 'user', content: prompt }],
        model: 'gpt-4o',
      };
      const response = await openai.chat.completions.create(completionParams);

      if (!response.choices?.[0]?.message?.content) {
        throw new Error('Failed to getSkillFromQuery skill');
      }

      return response.choices[0].message.content;
    } catch (error) {
      console.error(`Failed to getSkillFromQuery ${query}:`, error);
      throw new Error('Failed to getSkillFromQuery');
    }
  }

  async getTeamFromQuery({ query }: { query: string }): Promise<string> {
    const apiKey = process.env.OPENAI_API_KEY;
    const openai = new OpenAI({
      apiKey,
    });

    const prompt = `Based on the user's query asking for top skills needed to build a specific project "${query}", generate a professional title and description to introduce the skills list. The output should not include the skills themselves, any recommendations, planning, or suggestions. The title should be concise and relevant to the project, while the description should explain that the skills listed are essential for success in the specified project. The response must only contain the title and description, with no extra information.`;

    try {
      const completionParams: ChatCompletionCreateParamsNonStreaming = {
        messages: [{ role: 'user', content: prompt }],
        model: 'gpt-4o',
      };
      const response = await openai.chat.completions.create(completionParams);

      if (!response.choices?.[0]?.message?.content) {
        throw new Error('Failed to getTeamFromQuery skill');
      }

      return response.choices[0].message.content;
    } catch (error) {
      console.error(`Failed to getTeamFromQuery ${query}:`, error);
      throw new Error('Failed to getTeamFromQuery');
    }
  }

  async getSkillsForTopic({
    query,
    amount,
    topic,
  }: {
    query: string;
    amount: number;
    topic: string;
  }): Promise<string[]> {
    const apiKey = process.env.OPENAI_API_KEY;
    const openai = new OpenAI({
      apiKey,
    });

    if (topic === 'project') {
      topic = 'successfully build the project';
    }

    const prompt = `Based on the following query from the user: "${query}", provide a list of the top ${amount} soft, job, and technical skills or tools required for the ${topic} that the user is asking about. Return the result as a comma-separated list. Do not repeat the query itself.`;

    try {
      const completionParams: ChatCompletionCreateParamsNonStreaming = {
        messages: [{ role: 'user', content: prompt }],
        model: 'gpt-4o',
      };
      const response = await openai.chat.completions.create(completionParams);

      if (!response.choices?.[0]?.message?.content) {
        throw new Error('Failed to getSkillsForTopic');
      }

      const skills = response.choices[0].message.content
        .split(',')
        .map((skill) => skill.trim())
        .slice(0, amount);

      return skills;
    } catch (error) {
      console.error(`Failed to getSkillsForTopic ${query}:`, error);
      throw new Error('Failed to getSkillsForTopic');
    }
  }

  async getIdeasFromSkills({
    jobTitle,
    skills,
  }: {
    jobTitle: string;
    skills: string[];
  }): Promise<string> {
    const apiKey = process.env.OPENAI_API_KEY;
    const openai = new OpenAI({
      apiKey,
    });

    const prompt = `I have a user with the following job title and skills, each with their respective proficiency levels. I would like to know what the next potential career roles could be for this user, and what steps or improvements in specific skills would best help them advance on their career ladder. Please provide a clear career plan that highlights the next roles, recommended skill improvements, and any additional advice for career growth.

Current Role: ${jobTitle}
Skills: ${skills.join('; ')}

Please structure the response with the following sections:

Summary.
Next Possible Roles: Suggested career roles the user can pursue based on current skills and job title.
Skills to Focus On: Specific skills or new areas the user should develop or improve.
Career Growth Plan: A general guide for how the user can improve their profile to fit the suggested roles.

The response must contain only this—no extra bot-like phrases, so it looks professional. 
`;

    try {
      const completionParams: ChatCompletionCreateParamsNonStreaming = {
        messages: [{ role: 'user', content: prompt }],
        model: 'gpt-4o',
      };
      const response = await openai.chat.completions.create(completionParams);

      if (!response.choices?.[0]?.message?.content) {
        throw new Error('Failed to getIdeasFromSkills');
      }

      return response.choices[0].message.content;
    } catch (error) {
      console.error(`Failed to getIdeasFromSkills:`, error);
      throw new Error('Failed to getIdeasFromSkills');
    }
  }

  private async executeOpenAICallWithLogging(
    completionParams: ChatCompletionCreateParamsNonStreaming,
    operationName: string,
  ) {
    const apiKey = process.env.OPENAI_API_KEY;
    const openai = new OpenAI({ apiKey });

    const startTime = Date.now();
    const completion = await openai.chat.completions.create(completionParams);
    const endTime = Date.now();
    const timeTaken = (endTime - startTime) / 1000;

    const usage = completion.usage;
    const totalPromptTokens = usage?.prompt_tokens || 0;
    const completionTokens = usage?.completion_tokens || 0;
    const cachedTokens = usage?.prompt_tokens_details?.cached_tokens || 0;
    const cacheHitRate =
      totalPromptTokens > 0
        ? ((cachedTokens / totalPromptTokens) * 100).toFixed(1)
        : '0.0';

    // Calculate cost based on model pricing
    const costBreakdown = this.calculateOpenAICost(
      completionParams.model,
      totalPromptTokens,
      completionTokens,
    );

    console.log(`OpenAI Token Usage (${operationName}):`, {
      model: completionParams.model,
      timeTaken: timeTaken.toFixed(2) + 's',
      promptTokens: totalPromptTokens,
      completionTokens: completionTokens,
      totalTokens: usage?.total_tokens || 0,
      cachedTokens: cachedTokens,
      cacheHitRate: `${cacheHitRate}%`,
      inputCost: `$${costBreakdown.inputCost.toFixed(6)}`,
      outputCost: `$${costBreakdown.outputCost.toFixed(6)}`,
      totalCost: `$${costBreakdown.totalCost.toFixed(6)}`,
      promptTokensDetails: usage?.prompt_tokens_details || {},
      completionTokensDetails: usage?.completion_tokens_details || {},
    });

    return completion;
  }

  /**
   * Calculate the cost of an OpenAI API call based on model pricing
   * Pricing as of 2024 (per 1M tokens):
   * - gpt-4o: $2.50 input, $10.00 output
   * - gpt-4o-mini: $0.15 input, $0.60 output
   * - gpt-4o-search-preview: Same as gpt-4o (estimated)
   * - gpt-5: $1.25 input, $10.00 output
   * - gpt-5-mini: $0.25 input, $2.00 output
   * - gpt-5-nano: $0.05 input, $0.40 output
   */
  private calculateOpenAICost(
    model: string,
    promptTokens: number,
    completionTokens: number,
  ): { inputCost: number; outputCost: number; totalCost: number } {
    const pricing = {
      'gpt-4o': { input: 2.5, output: 10.0 },
      'gpt-4o-mini': { input: 0.15, output: 0.6 },
      'gpt-4o-search-preview': { input: 2.5, output: 10.0 }, // Same as gpt-4o
      'gpt-5': { input: 1.25, output: 10.0 },
      'gpt-5-mini': { input: 0.25, output: 2.0 },
      'gpt-5-nano': { input: 0.05, output: 0.4 },
    };

    const modelPricing = pricing[model] || pricing['gpt-4o']; // Default to gpt-4o pricing

    // Convert per 1M tokens to per token, then calculate cost
    const inputCost = (promptTokens / 1_000_000) * modelPricing.input;
    const outputCost = (completionTokens / 1_000_000) * modelPricing.output;

    return {
      inputCost,
      outputCost,
      totalCost: inputCost + outputCost,
    };
  }
}
