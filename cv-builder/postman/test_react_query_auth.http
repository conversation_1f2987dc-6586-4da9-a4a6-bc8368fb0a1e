### Test React Query Auth Implementation

### 1. Login to establish session
POST {{baseUrl}}/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}

### 2. Test that getCurrentUser works (should be handled by React Query now)
GET {{baseUrl}}/me

### 3. Wait 5+ seconds for token to expire, then test other endpoint
### This should trigger automatic redirect via axios interceptor
GET {{baseUrl}}/members?page=1&itemsPerPage=10

### 4. Test another endpoint after expiration
### Should also trigger redirect
GET {{baseUrl}}/organization