# Git
.git
.gitignore
.gitattributes

# CI/CD
.gitlab-ci.yml
.github/
.husky/

# Documentation
README.md
*.md
docs/

# IDE and editor files
.vscode/
.cursor/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Dependency directories
# node_modules/ - We need this for the build, but it will be excluded in multi-stage

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env
.env.test
.env.local
.env.development.local
.env.test.local
.env.production.local

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt
dist-nuxt

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/

# Editor directories and files
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# Local development files
*.local

# Test files
**/*.test.js
**/*.test.ts
**/*.spec.js
**/*.spec.ts
test/
tests/
__tests__/

# Build artifacts that we don't want in the build context
dist/
build/

# Nx cache
.nx/cache

# ESLint report
eslint-report.json

# Deployment scripts (these are handled separately)
setup_env.sh
setup_docker.sh
