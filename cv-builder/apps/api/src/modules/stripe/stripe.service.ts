import {
  Injectable,
  Logger,
  NotFoundException,
  InternalServerErrorException,
  forwardRef,
  Inject,
  BadRequestException,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { ApiStripeProduct } from 'shared/types';
import Stripe from 'stripe';

import { AuthUserDto } from '../global/dto/auth-user.dto';
import { OrganizationService } from '../organization/organization.service';
import { UsersService } from '../users/users.service';

@Injectable()
export class StripeService {
  private readonly stripe: Stripe;
  private readonly logger = new Logger(StripeService.name);

  constructor(
    private readonly configService: ConfigService,
    @Inject(forwardRef(() => OrganizationService)) // Handle circular dependency
    private readonly organizationService: OrganizationService,
    private readonly usersService: UsersService,
  ) {
    const stripeSecretKey = this.configService.get<string>('STRIPE_SECRET_KEY');
    if (!stripeSecretKey) {
      this.logger.error('STRIPE_SECRET_KEY is not configured.');
      throw new Error('Stripe secret key is not configured.');
    }
    this.stripe = new Stripe(stripeSecretKey, {
      apiVersion: '2025-04-30.basil', // Updated to the version expected by your types
      typescript: true,
    });
  }

  getStripeInstance(): Stripe {
    return this.stripe;
  }

  async createCustomer(
    email: string,
    name: string,
    organizationId: string,
  ): Promise<Stripe.Customer> {
    try {
      const customer = await this.stripe.customers.create({
        email,
        name,
        metadata: {
          organizationId,
        },
      });
      this.logger.log(
        `Stripe customer created: ${customer.id} for organization ${organizationId}`,
      );
      return customer;
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error(
          `Error creating Stripe customer for organization ${organizationId}: ${error.message}`,
          error.stack,
        );
      } else {
        this.logger.error(
          `Error creating Stripe customer for organization ${organizationId}: ${String(error)}`,
        );
      }

      throw error;
    }
  }

  async createCheckoutSession(
    customerId: string,
    priceId: string,
    successUrl: string,
    cancelUrl: string,
    organizationId: string,
    currency?: string,
    trialPeriodDays?: number,
  ): Promise<Stripe.Checkout.Session> {
    try {
      const subscriptionData: Stripe.Checkout.SessionCreateParams.SubscriptionData =
        {
          metadata: { organizationId },
        };

      if (trialPeriodDays && trialPeriodDays > 0) {
        subscriptionData.trial_period_days = trialPeriodDays;
      }

      const sessionParams: Stripe.Checkout.SessionCreateParams = {
        customer: customerId,
        payment_method_types: ['card'],
        mode: 'subscription',
        line_items: [
          {
            price: priceId,
            quantity: 1,
          },
        ],
        subscription_data: subscriptionData,
        success_url: successUrl,
        cancel_url: cancelUrl,
      };

      if (currency) {
        sessionParams.currency = currency.toLowerCase();
      }

      // If you want to pre-fill the email on the Checkout page
      // const customer = await this.stripe.customers.retrieve(customerId);
      // if (customer && !customer.deleted && customer.email) {
      //   sessionParams.customer_email = customer.email;
      // }

      const session = await this.stripe.checkout.sessions.create(sessionParams);
      this.logger.log(
        `Stripe checkout session created: ${session.id} for customer ${customerId}`,
      );
      return session;
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error(
          `Error creating Stripe checkout session for customer ${customerId}: ${error.message}`,
          error.stack,
        );
      } else {
        this.logger.error(
          `Error creating Stripe checkout session for customer ${customerId}: ${String(error)}`,
        );
      }

      throw error;
    }
  }

  async initiateCheckoutForOrganization(
    authUser: AuthUserDto,
    priceId: string,
    successUrl: string,
    cancelUrl: string,
    currency?: string,
  ): Promise<Stripe.Checkout.Session> {
    const organization = await this.organizationService.findById(
      authUser.organization._id,
    );
    if (!organization) {
      this.logger.error(
        `Organization ${authUser.organization._id} not found during checkout initiation.`,
      );
      throw new NotFoundException(
        `Organization ${authUser.organization._id} not found.`,
      );
    }

    let stripeCustomerId = organization.stripeCustomerId;

    if (!stripeCustomerId) {
      const user = await this.usersService.findById(authUser._id.toString());
      if (!user || !user.email) {
        this.logger.error(
          `User ${authUser._id} not found or has no email for Stripe customer creation.`,
        );
        throw new InternalServerErrorException(
          'Could not retrieve user details for Stripe customer creation.',
        );
      }

      const customer = await this.createCustomer(
        user.email,
        organization.name,
        organization._id.toString(),
      );
      stripeCustomerId = customer.id;
      const updatedOrg = await this.organizationService.updateOrganization(
        organization._id.toString(),
        {
          stripeCustomerId,
        },
      );
      if (!updatedOrg) {
        this.logger.error(
          `Failed to update organization ${authUser.organization._id} with Stripe customer ID ${stripeCustomerId}.`,
        );
        throw new InternalServerErrorException(
          'Failed to update organization with Stripe customer ID.',
        );
      }
      // organization = updatedOrg; // Not strictly needed to re-assign here as we only need stripeCustomerId
    }

    // The existing createCheckoutSession method can now be a more direct utility
    return this.createCheckoutSession(
      stripeCustomerId,
      priceId,
      successUrl,
      cancelUrl,
      authUser.organization._id.toString(),
      currency,
    );
  }

  async createPortalSession(
    customerId: string,
    returnUrl: string,
  ): Promise<Stripe.BillingPortal.Session> {
    try {
      const portalSession = await this.stripe.billingPortal.sessions.create({
        customer: customerId,
        return_url: returnUrl,
      });
      this.logger.log(
        `Stripe portal session created for customer ${customerId}`,
      );
      return portalSession;
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error(
          `Error creating Stripe portal session for customer ${customerId}: ${error.message}`,
          error.stack,
        );
      } else {
        this.logger.error(
          `Error creating Stripe portal session for customer ${customerId}: ${String(error)}`,
        );
      }

      throw error;
    }
  }

  async initiatePortalSessionForOrganization(
    authUser: AuthUserDto,
  ): Promise<Stripe.BillingPortal.Session> {
    const organization = await this.organizationService.findById(
      authUser.organization._id,
    );

    if (!organization) {
      this.logger.error(
        `Organization ${authUser.organization._id} not found when creating portal session.`,
      );
      throw new NotFoundException(
        `Organization ${authUser.organization._id} not found.`,
      );
    }

    if (!organization.stripeCustomerId) {
      this.logger.error(
        `Organization ${organization._id} has no Stripe customer ID for portal session. User might need to subscribe first.`,
      );
      throw new BadRequestException(
        'Billing portal not available. Organization is not linked to a Stripe customer.',
      );
    }
    const returnUrl = `${process.env.VITE_WEB_BASE_URL}/settings/billing`;
    return this.createPortalSession(organization.stripeCustomerId, returnUrl);
  }

  constructEventFromPayload(signature: string, payload: Buffer): Stripe.Event {
    const webhookSecret = this.configService.get<string>(
      'STRIPE_WEBHOOK_SECRET',
    );
    if (!webhookSecret) {
      this.logger.error('STRIPE_WEBHOOK_SECRET is not configured.');
      throw new BadRequestException('Stripe webhook secret is not configured.');
    }
    try {
      return this.stripe.webhooks.constructEvent(
        payload,
        signature,
        webhookSecret,
      );
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error(
          `Error constructing Stripe event: ${error.message}`,
          error.stack,
        );
        throw new BadRequestException(`Webhook Error: ${error.message}`);
      } else {
        this.logger.error(`Error constructing Stripe event: ${String(error)}`);
        throw new BadRequestException(`Webhook Error: ${String(error)}`);
      }
    }
  }

  async getCvProductsWithPrices(): Promise<ApiStripeProduct[]> {
    try {
      const productsResponse = await this.stripe.products.list({
        active: true,
      });

      const cvProductsRaw = productsResponse.data.filter(
        (product) => product.metadata && product.metadata.app === 'cv',
      );

      if (!cvProductsRaw.length) {
        return [];
      }

      const productsWithPrices: ApiStripeProduct[] = await Promise.all(
        cvProductsRaw.map(async (product) => {
          const mainPricesResponse = await this.stripe.prices.list({
            product: product.id,
            active: true,
            expand: ['data.currency_options'],
          });

          const allPricesForThisProduct: Stripe.Price[] = [];
          for (const basePrice of mainPricesResponse.data) {
            allPricesForThisProduct.push(basePrice);

            if (basePrice.currency_options) {
              for (const currencyCode in basePrice.currency_options) {
                const option = basePrice.currency_options[currencyCode];
                if (option) {
                  const syntheticPrice = this._createSyntheticPriceFromOption(
                    basePrice,
                    currencyCode,
                    option,
                  );
                  if (syntheticPrice) {
                    allPricesForThisProduct.push(syntheticPrice);
                  }
                }
              }
            }
          }

          const customMetadata =
            product.metadata as unknown as ApiStripeProduct['metadata'];

          return {
            ...product,
            default_price: product.default_price as Stripe.Price | null,
            metadata: customMetadata,
            prices: allPricesForThisProduct,
          } as ApiStripeProduct;
        }),
      );

      return productsWithPrices;
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error(
          `Error fetching CV products with prices from Stripe: ${error.message}`,
          error.stack,
        );
      } else {
        this.logger.error(
          `Error fetching CV products with prices from Stripe: ${String(error)}`,
        );
      }

      throw new InternalServerErrorException(
        'Could not fetch products from Stripe.',
      );
    }
  }

  private _createSyntheticPriceFromOption(
    basePrice: Stripe.Price,
    currencyCode: string,
    option: {
      unit_amount: number | null;
      tax_behavior?: Stripe.Price.TaxBehavior | null;
    },
  ): Stripe.Price | null {
    if (typeof option.unit_amount !== 'number') {
      this.logger.warn(
        `Currency option for ${currencyCode} on price ${basePrice.id} has no unit_amount. Skipping.`,
      );
      return null;
    }

    return {
      id: `${basePrice.id}`,
      object: 'price',
      active: basePrice.active,
      billing_scheme: basePrice.billing_scheme,
      created: basePrice.created,
      currency: currencyCode.toLowerCase(),
      custom_unit_amount: null,
      livemode: basePrice.livemode,
      lookup_key: basePrice.lookup_key
        ? `${basePrice.lookup_key}_${currencyCode}`
        : null,
      metadata: {}, // Currency options don't have separate metadata
      nickname: basePrice.nickname
        ? `${basePrice.nickname} (${currencyCode.toUpperCase()})`
        : null,
      product:
        typeof basePrice.product === 'string'
          ? basePrice.product
          : (basePrice.product as Stripe.Product).id,
      recurring: basePrice.recurring ? { ...basePrice.recurring } : null,
      tax_behavior:
        option.tax_behavior || basePrice.tax_behavior || 'unspecified',
      tiers_mode: null,
      transform_quantity: null,
      type: basePrice.type,
      unit_amount: option.unit_amount,
      unit_amount_decimal: String(option.unit_amount),
    };
  }
}
