import { Type } from 'class-transformer';
import {
  IsNumber,
  IsEmail,
  IsNotEmpty,
  IsOptional,
  IsString,
  Length,
  IsArray,
  ValidateNested,
  MaxLength,
  IsBoolean,
  IsDateString,
} from 'class-validator';
import { Trim } from 'shared/utils/decorators';

export class CvSectionBaseDto {
  @IsNumber()
  order!: number;

  @IsString()
  @MaxLength(64, { message: 'Title must be less than 64 characters' })
  title!: string;
}

export class CvSocialDto {
  @Trim()
  @IsString()
  @IsNotEmpty({ message: 'Social name cannot be empty' })
  @Length(1, 64, { message: 'Social name must be between 1 and 64 characters' })
  title!: string;

  @Trim()
  @IsString()
  @IsNotEmpty({ message: 'Social link cannot be empty' })
  @MaxLength(2048, { message: 'Link is too long' })
  value!: string;

  @IsBoolean()
  active!: string;
}

export class JobTitleDto {
  @Trim()
  @IsOptional()
  @IsString()
  value?: string;

  @IsBoolean()
  active!: string;
}

export class LocationDto {
  @Trim()
  @IsOptional()
  @IsString()
  value?: string;

  @IsBoolean()
  active!: string;
}

export class NationalityDto {
  @Trim()
  @IsOptional()
  @IsString()
  value?: string;

  @IsBoolean()
  active!: string;
}

export class EmailDto {
  @Trim()
  @IsOptional()
  @IsString()
  @IsEmail()
  value?: string;

  @IsBoolean()
  active!: string;
}

export class TelephoneDto {
  @Trim()
  @IsOptional()
  @IsString()
  value?: string;

  @IsBoolean()
  active!: string;
}

export class PersonalInfoDataDto {
  @Trim()
  @IsString()
  @IsNotEmpty({ message: 'First name cannot be empty' })
  @Length(1, 64, { message: 'First name must be between 1 and 64 characters' })
  firstName!: string;

  @Trim()
  @IsString()
  @IsNotEmpty({ message: 'First name cannot be empty' })
  @Length(1, 64, { message: 'First name must be between 1 and 64 characters' })
  lastName!: string;

  @ValidateNested()
  @Type(() => JobTitleDto)
  jobTitle!: JobTitleDto;

  @ValidateNested()
  @Type(() => LocationDto)
  location!: LocationDto;

  @ValidateNested()
  @Type(() => NationalityDto)
  nationality!: NationalityDto;

  @ValidateNested()
  @Type(() => EmailDto)
  email!: EmailDto;

  @ValidateNested()
  @Type(() => TelephoneDto)
  telephone!: TelephoneDto;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CvSocialDto)
  socials!: CvSocialDto[];
}

export class PersonalInfoDto extends CvSectionBaseDto {
  @ValidateNested()
  @Type(() => PersonalInfoDataDto)
  data!: PersonalInfoDataDto;
}

export class AboutMeDataDto {
  @Trim()
  @IsString()
  @MaxLength(10000, { message: 'Paragraph is too long' })
  aboutMe!: string;
}

export class AboutMeDto extends CvSectionBaseDto {
  @ValidateNested()
  @Type(() => AboutMeDataDto)
  data!: AboutMeDataDto;
}

export class WorkHistoryRecordDto {
  @IsBoolean()
  active!: string;

  @Trim()
  @IsString()
  @IsNotEmpty({ message: 'Company name cannot be empty' })
  @Length(1, 64, {
    message: 'Company name must be between 1 and 64 characters',
  })
  companyName!: string;

  @Trim()
  @IsString()
  @IsNotEmpty({ message: 'Role title cannot be empty' })
  @Length(1, 64, { message: 'Role title must be between 1 and 64 characters' })
  roleTitle!: string;

  @Trim()
  @IsOptional()
  @IsString()
  @MaxLength(500, {
    message: 'Role description must be less than 500 characters',
  })
  roleDescription?: string;

  @IsDateString()
  startDate!: Date;

  @IsOptional()
  @IsDateString()
  endDate?: Date;

  @IsBoolean()
  isCurrent!: string;
}

export class WorkHistoryDto extends CvSectionBaseDto {
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => WorkHistoryRecordDto)
  data!: WorkHistoryRecordDto[];
}

export class EducationRecordDto {
  @IsBoolean()
  active!: string;

  @Trim()
  @IsString()
  @IsNotEmpty({ message: 'School name cannot be empty' })
  @Length(1, 64, { message: 'School name must be between 1 and 64 characters' })
  schoolName!: string;

  @Trim()
  @IsString()
  @IsNotEmpty({ message: 'Degree cannot be empty' })
  @Length(1, 64, { message: 'Degree must be between 1 and 64 characters' })
  degree!: string;

  @Trim()
  @IsOptional()
  @IsString()
  @MaxLength(500, { message: 'Description must be less than 500 characters' })
  description?: string;

  @IsOptional()
  @IsDateString()
  startDate?: Date;

  @IsOptional()
  @IsDateString()
  endDate?: Date;
}

export class EducationDto extends CvSectionBaseDto {
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => EducationRecordDto)
  data!: EducationRecordDto[];
}

export class CertificationsDataDto {
  @Trim()
  @IsString()
  @MaxLength(10000, { message: 'Certifications paragraph is too long' })
  certifications!: string;
}

export class CertificationsDto extends CvSectionBaseDto {
  @ValidateNested()
  @Type(() => CertificationsDataDto)
  data!: CertificationsDataDto;
}

export class SkillsDataDto {
  @Trim()
  @IsString()
  @MaxLength(10000, { message: 'Skills paragraph is too long' })
  skills!: string;
}

export class SkillsDto extends CvSectionBaseDto {
  @ValidateNested()
  @Type(() => SkillsDataDto)
  data!: SkillsDataDto;
}

export class LanguagesDataDto {
  @Trim({ each: true })
  @IsArray()
  @IsString({ each: true })
  languages!: string[];
}

export class LanguagesDto extends CvSectionBaseDto {
  @ValidateNested()
  @Type(() => LanguagesDataDto)
  data!: LanguagesDataDto;
}

export class CustomDataDto {
  @Trim()
  @IsString()
  @MaxLength(10000, { message: 'Paragraph is too long' })
  richText!: string;
}

export class CustomDto extends CvSectionBaseDto {
  @ValidateNested()
  @Type(() => CustomDataDto)
  data!: CustomDataDto;
}

export class UpdateCvSectionsDto {
  @ValidateNested()
  @Type(() => PersonalInfoDto)
  personalInfo!: PersonalInfoDto;

  @ValidateNested()
  @Type(() => AboutMeDto)
  aboutMe!: AboutMeDto;

  @ValidateNested()
  @Type(() => WorkHistoryDto)
  workHistory!: WorkHistoryDto;

  @ValidateNested()
  @Type(() => EducationDto)
  education!: EducationDto;

  @ValidateNested()
  @Type(() => CertificationsDto)
  certifications!: CertificationsDto;

  @ValidateNested()
  @Type(() => SkillsDto)
  skills!: SkillsDto;

  @ValidateNested()
  @Type(() => LanguagesDto)
  languages!: LanguagesDto;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CustomDto)
  customSections!: CustomDto[];
}
