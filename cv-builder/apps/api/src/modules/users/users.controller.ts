import {
  Controller,
  Param,
  Body,
  UseGuards,
  Patch,
  Post,
  Delete,
  HttpCode,
  HttpStatus,
  UploadedFile,
  UseInterceptors,
  ParseFilePipe,
  MaxFileSizeValidator,
  FileTypeValidator,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import {
  UpdateUserDto,
  DeleteProfileDto,
} from 'shared/dto/user/update-user.dto';

import { User } from './user.schema';
import { UsersService } from './users.service';
import { AuthGuard } from '../auth/guards/auth.guard';
import { Action } from '../casl/casl.types';
import { CheckAbilities } from '../casl/decorators/check-policies.decorator';
import { PoliciesGuard } from '../casl/guards/policies.guard';
import { AuthUser } from '../global/decorators/user.decorator';
import { AuthUserDto } from '../global/dto/auth-user.dto';

@Controller('users')
export class UsersController {
  constructor(private readonly usersService: UsersService) {}

  @UseGuards(AuthGuard, PoliciesGuard)
  @CheckAbilities({ action: Action.Update, subject: User })
  @Patch(':userId')
  @HttpCode(HttpStatus.OK)
  async updateUser(
    @Param('userId') userId: string,
    @Body() updateUserDto: UpdateUserDto,
    @AuthUser() authUser: AuthUserDto,
  ): Promise<User | null> {
    return this.usersService.updateUser(userId, updateUserDto, authUser);
  }

  @UseGuards(AuthGuard)
  @Post(':userId/avatar')
  @UseInterceptors(FileInterceptor('avatar'))
  @HttpCode(HttpStatus.OK)
  async uploadAvatar(
    @Param('userId') userId: string,
    @UploadedFile(
      new ParseFilePipe({
        validators: [
          new MaxFileSizeValidator({ maxSize: 5 * 1024 * 1024 }), // 5MB
          new FileTypeValidator({ fileType: '.(png|jpeg|jpg|gif|webp)' }),
        ],
      }),
    )
    avatarFile: Express.Multer.File,
  ): Promise<User | null> {
    return this.usersService.uploadUserAvatar(userId, avatarFile);
  }

  @UseGuards(AuthGuard)
  @Delete('me')
  @HttpCode(HttpStatus.OK)
  async deleteMe(
    @AuthUser() authUser: AuthUserDto,
    @Body() deleteProfileDto: DeleteProfileDto,
  ) {
    return this.usersService.deleteUser(
      authUser._id.toString(),
      deleteProfileDto.password,
    );
  }

  @UseGuards(AuthGuard)
  @Post('me/cancel-deletion')
  @HttpCode(HttpStatus.OK)
  async cancelMyDeletion(
    @AuthUser() authUser: AuthUserDto,
  ): Promise<User | null> {
    return this.usersService.cancelUserDeletion(authUser._id.toString());
  }
}
