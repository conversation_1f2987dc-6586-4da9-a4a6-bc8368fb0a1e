import { <PERSON><PERSON>, Controller, Get } from '@nestjs/common';

import { GeneratorService, UserProfile } from './generator.service';

@Controller()
export class GeneratorController {
  constructor(private readonly genService: GeneratorService) {}

  @Get('generate-profiles/:count')
  async generateUsers(@Param('count') count: string): Promise<UserProfile[]> {
    const numUsers = parseInt(count, 10);
    return this.genService.generateUserProfiles(numUsers);
  }
}
