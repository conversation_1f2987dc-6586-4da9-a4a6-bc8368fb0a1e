[{"key": "ctrl+shift+f", "command": "workbench.action.quickTextSearch"}, {"key": "shift+alt+-", "command": "workbench.action.terminal.fontZoomOut"}, {"key": "alt+-", "command": "editor.action.fontZoomOut"}, {"key": "alt+=", "command": "editor.action.fontZoomIn"}, {"key": "shift+alt+=", "command": "workbench.action.terminal.fontZoomIn"}, {"key": "shift+alt+/", "command": "workbench.action.splitEditorDown"}, {"key": "ctrl+k ctrl+\\", "command": "-workbench.action.splitEditorDown"}, {"key": "alt+/", "command": "workbench.action.splitEditorRight"}, {"key": "ctrl+k ctrl+\\", "command": "-workbench.action.splitEditorRight"}, {"key": "ctrl+k r", "command": "workbench.files.action.showActiveFileInExplorer"}, {"key": "ctrl+k ctrl+shift+m", "command": "workbench.action.toggleMaximizedPanel", "when": "panelAlignment == 'center' || panelPosition != 'bottom' && panelPosition != 'top'"}, {"key": "ctrl+k j", "command": "workbench.action.switchWindow"}, {"key": "ctrl+o", "command": "workbench.action.quickOpen"}, {"key": "ctrl+p", "command": "-workbench.action.quickOpen"}, {"key": "ctrl+p", "command": "workbench.action.showCommands"}, {"key": "ctrl+shift+p", "command": "-workbench.action.showCommands"}, {"key": "alt+1", "command": "workbench.action.openEditorAtIndex1"}, {"key": "alt+1", "command": "-workbench.action.openEditorAtIndex1"}, {"key": "alt+f", "command": "editor.action.inlineSuggest.triggerInlineEditExplicit"}, {"key": "ctrl+shift+;", "command": "-breadcrumbs.focus", "when": "breadcrumbsPossible && breadcrumbsVisible"}, {"key": "ctrl+shift+; c", "command": "workbench.action.terminal.clear", "when": "terminalHasBeenCreated || terminalProcessSupported"}, {"key": "ctrl+shift+; b", "command": "bookmarks.toggleLabeled"}, {"key": "ctrl+k ctrl+shift+c", "command": "copyRelativeFilePath", "when": "editor<PERSON><PERSON><PERSON>"}, {"key": "ctrl+k ctrl+shift+c", "command": "-copyRelativeFilePath", "when": "editor<PERSON><PERSON><PERSON>"}, {"key": "ctrl+shift+; p", "command": "copyRelativeFilePath", "when": "editor<PERSON><PERSON><PERSON>"}, {"key": "ctrl+shift+; l", "command": "bookmarks.listFromAllFiles"}, {"key": "ctrl+shift+; m", "command": "markdown.showPreview", "when": "!notebookEditorFocused && editorLangId == 'markdown'"}, {"key": "ctrl+shift+; shift+m", "command": "markdown.showPreviewToSide", "when": "!notebookEditorFocused && editorLangId == 'markdown'"}, {"key": "ctrl+shift+; r", "command": "references-view.findReferences", "when": "editorHasReferenceProvider"}, {"key": "ctrl+shift+; n", "command": "editor.gotoNextFold"}, {"key": "ctrl+shift+; shift+n", "command": "editor.gotoPreviousFold"}, {"key": "ctrl+shift+; i", "command": "workbench.action.terminal.fontZoomIn", "when": "terminalFocus"}, {"key": "ctrl+shift+; shift+d", "command": "workbench.action.terminal.fontZoomOut", "when": "terminalProcessSupported"}, {"key": "ctrl+shift+; g", "command": "git.openChange"}, {"key": "ctrl+shift+; shift+h", "command": "gitlens.gitCommands.switch"}, {"key": "ctrl+shift+; h", "command": "references-view.showCallHierarchy", "when": "editorHasCallHierarchyProvider"}, {"key": "ctrl+shift+; t", "command": "editor.action.goToTypeDefinition", "when": "editorHasTypeDefinitionProvider"}, {"key": "ctrl+shift+; shift+t", "command": "workbench.action.terminal.changeColor", "when": "terminalHasBeenCreated || terminalProcessSupported"}, {"key": "ctrl+shift+; f", "command": "workbench.view.search", "when": "workbench.view.search.active"}, {"key": "ctrl+shift+alt+p", "command": "projectManager.listProjectsNewWindow"}, {"key": "ctrl+shift+g l", "command": "gitlens.gitCommands.history"}, {"key": "ctrl+shift+; e", "command": "cline.focusChatInput"}, {"key": "ctrl+shift+; shift+e", "command": "cline.addToChat", "when": "editorHasSelection"}, {"key": "ctrl+'", "command": "-cline.addToChat", "when": "editorHasSelection"}, {"key": "ctrl+shift+; shift+e", "command": "cline.addTerminalOutputToChat", "when": "terminalFocus"}, {"key": "ctrl+shift+; o", "command": "roo-cline.focusInput"}, {"key": "ctrl+shift+; shift+o", "command": "roo-cline.addToContext"}, {"key": "ctrl+shift+; shift+i", "command": "github.copilot.chat.attachSelection"}, {"key": "ctrl+shift+; i", "command": "github.copilot.chat.attachFile", "when": "resourceScheme == 'file' || resourceScheme == 'untitled' || resourceScheme == 'vscode-remote' || resourceScheme == 'vscode-userdata'"}, {"key": "ctrl+shift+; \\", "command": "workbench.action.maximizeEditorHideSidebar", "when": "auxiliaryBarVisible || sideBarVisible || editorPartMultipleEditorGroups && !editorPartMaximizedEditorGroup"}, {"key": "alt+/", "command": "workbench.action.terminal.split", "when": "terminalFocus"}, {"key": "ctrl+w", "command": "workbench.action.terminal.kill", "when": "terminalFocus && (terminalHasBeenCreated || terminalIsOpen || terminalProcessSupported)"}, {"key": "ctrl+w", "command": "workbench.action.closeActiveEditor", "when": "editorTextFocus"}, {"key": "ctrl+w", "command": "-workbench.action.closeActiveEditor"}, {"key": "ctrl+w", "command": "-workbench.action.terminal.killEditor", "when": "terminalEditorFocus && terminalFocus && terminalHasBeenCreated || terminalEditorFocus && terminalFocus && terminalProcessSupported"}, {"key": "ctrl+shift+; f", "command": "workbench.view.search.focus"}, {"key": "ctrl+shift+; s", "command": "editor.action.insertSnippet", "when": "!editor<PERSON><PERSON><PERSON><PERSON>"}, {"key": "ctrl+shift+; d", "command": "extension.js-debug.npmScript"}, {"key": "ctrl+shift+; k", "command": "kilo-code.focusChatInput", "when": "true"}, {"key": "ctrl+shift+a", "command": "-kilo-code.focusChatInput", "when": "true"}, {"key": "ctrl+shift+; shift+k", "command": "kilo-code.addToContext"}]