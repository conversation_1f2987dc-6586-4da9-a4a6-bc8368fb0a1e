import { keepPreviousData, useQuery } from '@tanstack/react-query';
import { Plus } from 'lucide-react';
import { Certification } from 'shared/types';

import { Loader } from '@/components/common';
import { searchCertificatesRequest } from '@/helpers/requests';

interface CertificationsListProps {
  searchValue: string;
  certificatesToExclude: string[];
  onAdd: (certification: Certification) => void;
}

export function CertificationsList({
  searchValue,
  certificatesToExclude,
  onAdd,
}: CertificationsListProps) {
  const {
    data: foundCertificates,
    isLoading: foundCertificatesLoading,
    isFetching: foundCertificatesFetching,
  } = useQuery({
    queryKey: ['certificates', { searchValue, certificatesToExclude }],
    queryFn: () =>
      searchCertificatesRequest(searchValue, certificatesToExclude),
    placeholderData: keepPreviousData,
  });

  if (foundCertificatesLoading) {
    return (
      <div className="flex justify-center h-28 items-center">
        <Loader />
      </div>
    );
  }

  return (
    <div className="relative p-4">
      {!foundCertificates?.length ? (
        <div className="flex flex-col space-y-2 items-center justify-center border border-dashed rounded-[8px] px-2 py-6">
          <img src="/images/dog-with-binocular.svg" alt="noMatches" />
          <span className="text-smalldoge-3">
            No such certification exists in our database. Try using alternate
            keywords.
          </span>
        </div>
      ) : (
        <div className="flex flex-col space-y-3 max-h-96 overflow-auto">
          {foundCertificates.map((cert) => (
            <div
              key={cert.msId}
              className="flex items-center bg-msGray-6 rounded-sm p-2 transition-all hover:bg-msGray-5 group cursor-pointer"
              onClick={() => onAdd(cert)}
            >
              <img
                className="size-8 shrink-0 mr-2"
                src={cert.image}
                alt="organizationIcon"
              />
              <div className="flex flex-col flex-grow min-w-0">
                <div className="flex">
                  <b className="text-smalldoge-3 flex-grow min-w-0 truncate">
                    {cert.name}
                  </b>
                  <Plus className="shrink-0" size={24} />
                </div>
                <span className="text-smalldoge-4 text-msGray-2 line-clamp-2">
                  {cert.code}
                </span>
                <span className="text-smalldoge-4 text-msGray-2 line-clamp-2">
                  {cert.organization}
                </span>
              </div>
            </div>
          ))}
        </div>
      )}
      {foundCertificatesFetching && (
        <div className="absolute top-0 left-0 bottom-0 right-0 flex justify-center items-center bg-msGray-6 bg-opacity-40">
          <Loader />
        </div>
      )}
    </div>
  );
}
