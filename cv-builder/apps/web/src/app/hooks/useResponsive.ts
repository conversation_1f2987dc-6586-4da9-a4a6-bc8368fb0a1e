import { useEffect, useState } from 'react';

interface ResponsiveBreakpoints {
  isMobile: boolean;
  isSmallScreen: boolean;
}

export function useResponsive(): ResponsiveBreakpoints {
  const [isMobile, setIsMobile] = useState<boolean>(false);
  const [isSmallScreen, setIsSmallScreen] = useState<boolean>(false);

  useEffect(() => {
    const checkResponsive = () => {
      const width = window.innerWidth;
      setIsMobile(width < 768); // md breakpoint
      setIsSmallScreen(width < 1366);
    };

    checkResponsive();
    window.addEventListener('resize', checkResponsive);

    return () => window.removeEventListener('resize', checkResponsive);
  }, []);

  return { isMobile, isSmallScreen };
}
