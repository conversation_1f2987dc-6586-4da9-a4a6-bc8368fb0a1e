### Test Session Expiration Toast Implementation

### 1. Login to establish session
POST {{baseUrl}}/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}

### 2. Wait 5+ seconds for token to expire, then make a request to any protected endpoint
### This should trigger the axios interceptor and redirect to login with sessionExpired=true
### The LoginPage should then show the toast: "Your session has expired. Please log in again."
GET {{baseUrl}}/members?page=1&itemsPerPage=10

### 3. Test with another endpoint to confirm consistent behavior
GET {{baseUrl}}/organization

### 4. Test with CVs endpoint
GET {{baseUrl}}/cvs?page=1&itemsPerPage=10&status=draft

### Note: After each request above (when token is expired), 
### the browser should redirect to: /login?sessionExpired=true
### and show an error toast with the message