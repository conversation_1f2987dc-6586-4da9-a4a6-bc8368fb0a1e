import { format } from 'date-fns';
import { MoreHorizontal } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import {
  currencyData,
  Cv,
  CvPreferences,
  cvStatusData,
  templateData,
  timeRangeDisplayMap,
} from 'shared/types';

import {
  CustomerBadge,
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '../common';

import { cn } from '@/lib/utils';

const formatContractEnd = (preferences: CvPreferences) => {
  const { contractEnd, autoRenewal } = preferences;

  if (contractEnd) {
    return `${format(contractEnd, 'PP')}${autoRenewal ? ' (Auto)' : ''}`;
  }

  return autoRenewal ? 'Auto' : 'None';
};

interface CVListTabProps {
  active: boolean;
  cv: Cv;
  onClick: () => void;
  onEdit: () => void;
  onDelete: () => void;
  onDuplicate: () => void;
}

export function CVListTab({
  active,
  cv,
  onClick,
  onEdit,
  onDelete,
  onDuplicate,
}: CVListTabProps) {
  const navigate = useNavigate();

  const tabOptions = [
    {
      name: 'Edit CV',
      onClick: onEdit,
    },
    {
      name: 'Duplicate',
      onClick: onDuplicate,
    },
    {
      name: 'Remove',
      classNames: 'text-msRed-1',
      onClick: onDelete,
    },
  ];

  return (
    <div
      className={cn(
        'flex space-x-2 items-center px-4 py-2 border-b border-msGray-5 transition-colors duration-100 cursor-pointer hover:bg-msGray-6',
        active && 'bg-msGray-5',
      )}
      onClick={onClick}
    >
      <div className="flex flex-col flex-grow min-w-0">
        <div className="flex flex-col mb-2 space-y-2 sm:space-y-0 sm:space-x-2 sm:flex-row sm:justify-between sm:items-center">
          <div className="flex flex-col min-w-0 sm:max-w-[50%]">
            <span className="font-bold text-smalldoge-3 line-clamp-2">
              {cv.preferences.title}
            </span>
            <span className="text-smalldoge-5 text-msGray-2">
              {templateData[cv.template]}
            </span>
          </div>
          <CustomerBadge
            name={cv.preferences.customer?.name}
            className="min-w-0 max-w-full sm:max-w-[50%]"
            onClick={(e) => {
              e.stopPropagation();

              if (cv.preferences.customer) {
                navigate(`/customers/?customer=${cv.preferences.customer._id}`);
              }
            }}
          />
        </div>

        <div className="flex items-center justify-between space-x-2">
          <div
            className={cn(
              'w-fit max-w-full flex rounded-[100px] px-2',
              cvStatusData[cv.status].bgColor,
            )}
          >
            <span
              className={cn(
                'text-smalldoge-3 text-msGray-3 font-bold truncate',
                cvStatusData[cv.status].textColor,
              )}
            >
              {cvStatusData[cv.status].name}
            </span>
          </div>
          <div className="flex items-center justify-end space-x-2 sm:justify-normal">
            <div className="text-smalldoge-5">
              <span className="text-msGray-3">
                Contract renewal <br className="sm:hidden" />
                {
                  <span className="font-bold text-msGray-2">
                    {formatContractEnd(cv.preferences)}
                  </span>
                }
              </span>
            </div>
            <div className="text-smalldoge-5">
              <span className="text-msGray-3">
                Created on <br className="sm:hidden" />
                <span className="font-bold text-msGray-2">
                  {format(cv.createdAt, 'PP')}
                </span>
              </span>
            </div>
            <div className="text-smalldoge-5">
              <span className="text-msGray-3">
                {cv.preferences.costRate?.timeRange ? (
                  <span>
                    {timeRangeDisplayMap[cv.preferences.costRate.timeRange]}
                    {' Rate'}
                  </span>
                ) : (
                  'Rate'
                )}{' '}
                <br className="sm:hidden" />
                <span className="font-bold text-msGray-2">
                  {cv.preferences.costRate?.amount
                    ? `${currencyData[cv.preferences.costRate.currency].sign} ${cv.preferences.costRate.amount}`
                    : 'Not set'}
                </span>
              </span>
            </div>
          </div>
        </div>
      </div>
      <DropdownMenu modal={false}>
        <DropdownMenuTrigger asChild>
          <button
            className="p-1 transition-colors rounded hover:bg-msGray-5"
            onClick={(e) => e.stopPropagation()}
          >
            <MoreHorizontal
              size={16}
              className="transition-colors duration-200 text-msGray-3 hover:text-msGray-1"
            />
          </button>
        </DropdownMenuTrigger>
        <DropdownMenuContent collisionPadding={10} align="end" className="w-48">
          <DropdownMenuGroup>
            {tabOptions.map((option) => (
              <DropdownMenuItem
                key={option.name}
                onClick={(e) => {
                  e.stopPropagation();
                  option.onClick();
                }}
              >
                <span className={cn('text-smalldoge-3', option.classNames)}>
                  {option.name}
                </span>
              </DropdownMenuItem>
            ))}
          </DropdownMenuGroup>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
}
