import { forwardRef, Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

import { OrganizationController } from './organization.controller';
import { Organization, OrganizationSchema } from './organization.schema';
import { OrganizationService } from './organization.service';
import { CaslModule } from '../casl/casl.module';
import { Cv, CvSchema } from '../cvs/cv.schema';
import { CvsModule } from '../cvs/cvs.module';
import { InviteModule } from '../invite/invite.module';
import { MailService } from '../mail/mail.service';
import { Member, MemberSchema } from '../members/member.schema';
import { UsersModule } from '../users/users.module';

@Module({
  imports: [
    forwardRef(() => InviteModule),
    forwardRef(() => UsersModule),
    forwardRef(() => CvsModule),
    CaslModule,
    MongooseModule.forFeature([
      { name: Organization.name, schema: OrganizationSchema },
      { name: Member.name, schema: MemberSchema },
      { name: Cv.name, schema: CvSchema },
    ]),
  ],
  providers: [OrganizationService, MailService],
  controllers: [OrganizationController],
  exports: [OrganizationService],
})
export class OrganizationModule {}
