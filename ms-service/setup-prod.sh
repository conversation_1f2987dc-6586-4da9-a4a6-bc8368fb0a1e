#!/bin/sh

cd /home/<USER>/ms-service

git fetch origin main
git reset --hard origin/main

export $(cat .env | xargs)

docker network create apinetwork 2>/dev/null || true

docker build . --progress=plain --no-cache -t nest

docker rm -f nginx-proxy 2>/dev/null || true
docker run --detach --restart unless-stopped \
    --name nginx-proxy \
    --publish 80:80 \
    --publish 443:443 \
    --net apinetwork \
    --volume certs:/etc/nginx/certs \
    --volume vhost:/etc/nginx/vhost.d \
    --volume html:/usr/share/nginx/html \
    --volume /var/run/docker.sock:/tmp/docker.sock:ro \
    nginxproxy/nginx-proxy

docker rm -f nginx-proxy-acme 2>/dev/null || true
docker run --detach --restart unless-stopped \
    --name nginx-proxy-acme \
    --net apinetwork \
    --volumes-from nginx-proxy \
    --volume /var/run/docker.sock:/var/run/docker.sock:ro \
    --volume acme:/etc/acme.sh \
    --env "DEFAULT_EMAIL=<EMAIL>" \
    nginxproxy/acme-companion

docker rm -f api 2>/dev/null || true
docker run --detach --restart unless-stopped \
    --name api \
    -p $VIRTUAL_PORT:$VIRTUAL_PORT \
    --net apinetwork \
    --env "VIRTUAL_HOST=$VIRTUAL_HOST" \
    --env "VIRTUAL_PORT=$VIRTUAL_PORT" \
    --env "LETSENCRYPT_HOST=$VIRTUAL_HOST" \
    nest

docker system prune -f
