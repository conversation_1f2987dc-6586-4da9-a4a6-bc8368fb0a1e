import { ForbiddenError } from '@casl/ability';
import {
  BadRequestException,
  ConflictException,
  ForbiddenException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import mongoose, { Model, Types } from 'mongoose';
import { isMongoDuplicateKeyError } from 'src/utils';

import { Customer, CustomerDocument } from './customer.schema';
import { CreateCustomerDto, UpdateCustomerDto } from './dto';
import { CaslAbilityFactory } from '../casl/casl-ability.factory';
import { Action } from '../casl/casl.types';
import { Cv, CvDocument } from '../cvs/cv.schema';
import { AuthUserDto } from '../global/dto/auth-user.dto';

@Injectable()
export class CustomersService {
  constructor(
    @InjectModel(Customer.name)
    private customerModel: Model<CustomerDocument>,
    @InjectModel(Cv.name)
    private cvModel: Model<CvDocument>,
    private caslAbilityFactory: CaslAbilityFactory,
  ) {}

  async createCustomer(dto: CreateCustomerDto, orgId: string | Types.ObjectId) {
    try {
      return await this.customerModel.create({
        ...dto,
        organization: orgId,
      });
    } catch (error) {
      if (isMongoDuplicateKeyError(error)) {
        throw new ConflictException(
          `Customer with name "${dto.name}" already exists in this organization.`,
        );
      }

      if (error instanceof mongoose.Error.ValidationError) {
        const validationErrors = Object.values(error.errors).map(
          (err) => err.message,
        );
        throw new BadRequestException({
          message: 'Validation failed',
          errors: validationErrors,
        });
      }

      throw error;
    }
  }

  async updateCustomer(
    customerId: string | Types.ObjectId,
    dto: UpdateCustomerDto,
    authUser: AuthUserDto,
  ) {
    await this.findCustomerAndAuthorize(customerId, authUser, Action.Update);

    try {
      return await this.customerModel.findByIdAndUpdate(
        customerId,
        { $set: dto },
        { new: true, runValidators: true },
      );
    } catch (error) {
      if (isMongoDuplicateKeyError(error)) {
        throw new ConflictException(
          `Customer with name "${dto.name}" already exists in this organization.`,
        );
      }

      if (error instanceof mongoose.Error.ValidationError) {
        const validationErrors = Object.values(error.errors).map(
          (err: { message: string }) => err.message,
        );
        throw new BadRequestException({
          message: 'Validation failed',
          errors: validationErrors,
        });
      }

      throw error;
    }
  }

  async getCustomer(
    customerId: string | Types.ObjectId,
    authUser: AuthUserDto,
  ) {
    return await this.findCustomerAndAuthorize(
      customerId,
      authUser,
      Action.Read,
    );
  }

  async getOrgCustomers(orgId: string | Types.ObjectId) {
    return await this.customerModel
      .find({ organization: orgId })
      .sort({ name: 1, createdAt: 1 });
  }

  async deleteCustomer(
    customerId: string | Types.ObjectId,
    authUser: AuthUserDto,
  ) {
    await this.findCustomerAndAuthorize(customerId, authUser, Action.Delete);

    // Set customer to null in all CVs that reference this customer
    await this.cvModel.updateMany(
      { 'preferences.customer': customerId },
      { $unset: { 'preferences.customer': '' } },
    );

    return await this.customerModel.findByIdAndDelete(customerId);
  }

  private async findCustomerAndAuthorize(
    customerId: string | Types.ObjectId,
    authUser: AuthUserDto,
    action: Action,
  ): Promise<CustomerDocument> {
    const customer = await this.customerModel.findById(customerId);
    if (!customer) {
      throw new NotFoundException(`Customer with ID "${customerId}" not found`);
    }

    const ability = this.caslAbilityFactory.createForUser(authUser);

    const customerForCheck = {
      ...customer.toObject(),
      constructor: Customer,
      organization: customer.organization.toString(),
    } as unknown as Customer & { organization: string };

    try {
      ForbiddenError.from(ability).throwUnlessCan(action, customerForCheck);
    } catch (error) {
      if (error instanceof ForbiddenError) {
        throw new ForbiddenException(error.message);
      }
      throw error;
    }

    return customer;
  }
}
