import { PDFDownloadLink } from '@react-pdf/renderer';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { ChevronDown, FileDown, Pencil, Settings } from 'lucide-react';
import { useMemo, useState, useEffect } from 'react';
import { UpdateCvInput } from 'shared/inputs';
import {
  Cv,
  CvStatus,
  cvStatusData,
  Member,
  Section,
  Template,
  templateData,
} from 'shared/types';

import {
  EuropassTemplate,
  CleanTemplate,
  ProfessionalTemplate,
} from '../../../assets/templates';
import { AiChat } from '../../components';
import { PDFViewer } from '../../components';
import {
  ButtonSecondary,
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '../common';

import { getMemberBase64Avatar, updateCvRequest } from '@/helpers/requests';
import { useResponsive } from '@/hooks/useResponsive';
import { cn } from '@/lib/utils';

interface CvPreviewProps {
  cv: Cv;
  member: Member;
  sections: Section[];
  editPage?: boolean;
  hasAiChanges?: boolean;
  onEdit?: () => void;
  onSettingsEdit?: () => void;
  onAiSectionsUpdate?: (aiSections: any) => void;
  onCreateSnapshot?: () => void;
  onRevertToSnapshot?: () => void;
}

export function CvPreview({
  cv,
  member,
  sections,
  editPage,
  hasAiChanges,
  onEdit,
  onSettingsEdit,
  onAiSectionsUpdate,
  onCreateSnapshot,
  onRevertToSnapshot,
}: CvPreviewProps) {
  const { isMobile } = useResponsive();
  const queryClient = useQueryClient();

  const [selectedTemplateType, setSelectedTemplateType] = useState<Template>(
    cv.template,
  );

  useEffect(() => {
    setSelectedTemplateType(cv.template);
  }, [cv.template]);

  const { data: base64Avatar } = useQuery({
    queryKey: ['base64Avatar', { memberId: member._id }],
    queryFn: () => getMemberBase64Avatar(member._id),
  });

  const { mutate: updateCv } = useMutation({
    mutationFn: (input: UpdateCvInput) => updateCvRequest(cv._id, input),
    onSuccess: (updatedCv: Cv) => {
      queryClient.setQueryData(['cv', { cvId: cv._id }], (oldData: Cv) => {
        oldData.status = updatedCv.status;
        oldData.template = updatedCv.template;

        return oldData;
      });

      queryClient.invalidateQueries({ queryKey: ['paginatedMembers'] });
      queryClient.invalidateQueries({
        queryKey: ['memberCvs', { memberId: member._id }],
      });

      // If CV was originally a draft or status is being changed, invalidate draftCvs query
      if (cv.status === CvStatus.draft || updatedCv.status !== cv.status) {
        queryClient.invalidateQueries({
          queryKey: ['draftCvs'],
        });
      }
    },
  });

  const [isAIGenerationPending, setIsAIGenerationPending] = useState(false);

  const cvStatusDropdown = (
    <div className="flex items-center ml-1 space-x-1">
      <span className="hidden text-smalldoge-4 sm:block">Status:</span>
      <DropdownMenu modal={false}>
        <DropdownMenuTrigger asChild>
          <div
            className={cn(
              'flex items-center cursor-pointer px-2 py-1 rounded-[2px]',
              cvStatusData[cv.status].triggerBgColor,
              cvStatusData[cv.status].triggerTextColor,
            )}
          >
            <span className="text-smalldoge-4">
              {cvStatusData[cv.status].name}
            </span>
            <ChevronDown className="flex-shrink-0" size={16} />
          </div>
        </DropdownMenuTrigger>
        <DropdownMenuContent
          collisionPadding={10}
          align="end"
          className="w-38 prevent-drawer-outside-click"
        >
          <DropdownMenuGroup>
            {Object.values(CvStatus).map((status) => (
              <DropdownMenuItem
                key={status}
                onClick={() => updateCv({ status })}
              >
                <span className="text-smalldoge-3">
                  {cvStatusData[status].name}
                </span>
              </DropdownMenuItem>
            ))}
          </DropdownMenuGroup>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );

  const templateDropdown = (
    <div className="flex items-center space-x-1">
      <span className="hidden text-smalldoge-4 sm:block">Template:</span>
      <DropdownMenu modal={false}>
        <DropdownMenuTrigger asChild>
          <div className="flex items-center cursor-pointer">
            <span className="font-bold text-smalldoge-4">
              {templateData[selectedTemplateType]}
            </span>
            <ChevronDown className="flex-shrink-0" size={16} />
          </div>
        </DropdownMenuTrigger>
        <DropdownMenuContent
          collisionPadding={10}
          align="end"
          className="w-38 prevent-drawer-outside-click"
        >
          <DropdownMenuGroup>
            {Object.values(Template).map((template) => (
              <DropdownMenuItem
                key={template}
                onClick={() => {
                  setSelectedTemplateType(template);
                  updateCv({ template });
                }}
              >
                <span className="text-smalldoge-3">
                  {templateData[template]}
                </span>
              </DropdownMenuItem>
            ))}
          </DropdownMenuGroup>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );

  const selectedTemplate = useMemo(() => {
    const sortedSections = sections.sort((a, b) => a.order - b.order);

    switch (selectedTemplateType) {
      case Template.clean:
        return <CleanTemplate sections={sortedSections} />;
      case Template.professional:
        return <ProfessionalTemplate sections={sortedSections} />;
      case Template.europass:
      default:
        return (
          <EuropassTemplate avatar={base64Avatar} sections={sortedSections} />
        );
    }
  }, [base64Avatar, sections, selectedTemplateType]);

  return (
    <div className="flex flex-col h-full">
      <div className="flex items-center h-10 px-4 py-2 border-b border-msGray-5">
        <div className="mr-auto">
          {editPage ? (
            templateDropdown
          ) : (
            <ButtonSecondary padding="iconLeft" text="normal" onClick={onEdit}>
              <span className="flex items-center space-x-1">
                <Pencil size={16} />
                <span>{isMobile ? 'Edit Sections' : 'Edit CV'}</span>
              </span>
            </ButtonSecondary>
          )}
        </div>

        <PDFDownloadLink
          className="h-6"
          document={selectedTemplate}
          fileName={`${member.firstName}_${member.lastName}_${cv.preferences.title}`}
        >
          <button className="p-1 transition-all duration-300 rounded-sm hover:bg-msGray-6">
            <FileDown size={16} />
          </button>
        </PDFDownloadLink>
        {editPage && (
          <>
            <button
              className="p-1 transition-all duration-300 rounded-sm hover:bg-msGray-6"
              onClick={onSettingsEdit}
            >
              <Settings size={16} />
            </button>
            {/* <button className="p-1 transition-all duration-300 rounded-sm hover:bg-msGray-6">
              <Share2 size={16} />
            </button> */}
            {cvStatusDropdown}
          </>
        )}
      </div>
      <div className="w-full h-full p-5 overflow-auto">
        {editPage && (
          <AiChat
            cvId={cv._id}
            cv={cv}
            onSectionsUpdate={(aiSections) => {
              if (onAiSectionsUpdate) {
                onAiSectionsUpdate(aiSections);
              }
            }}
            onPendingChange={setIsAIGenerationPending}
            onCreateSnapshot={onCreateSnapshot}
            onRevertToSnapshot={onRevertToSnapshot}
            hasAiChanges={hasAiChanges}
          />
        )}
        <PDFViewer
          template={selectedTemplate}
          isAIGenerationPending={isAIGenerationPending}
        />
      </div>
    </div>
  );
}
