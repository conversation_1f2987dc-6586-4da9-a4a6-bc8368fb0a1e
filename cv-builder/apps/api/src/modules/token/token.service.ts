import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { genSalt, hash } from 'bcrypt';
import { Model } from 'mongoose';
import { v4 as uuidv4 } from 'uuid';

import { Token, TokenDocument } from './token.schema';

@Injectable()
export class TokenService {
  constructor(
    @InjectModel(Token.name) private tokenModel: Model<TokenDocument>,
  ) {}

  async generateToken(dto: { email: string; password: string }) {
    let hashedPassword = '';

    if (dto.password) {
      const salt = await genSalt();
      hashedPassword = await hash(dto.password, salt);
    }

    return new this.tokenModel({
      token: uuidv4(),
      email: dto.email,
      password: hashedPassword,
    }).save();
  }

  findToken(token: string) {
    return this.tokenModel.findOne({ token });
  }

  isTokenInvalid(token: Token) {
    const now = new Date().getTime();
    const created = new Date(token.createdAt).getTime();
    return now > created + 30 * 60 * 1000;
  }

  async deleteToken(token: string) {
    const tokenDoc = await this.findToken(token);
    if (!tokenDoc) {
      return;
    }
    return this.tokenModel.deleteOne({ token });
  }
}
