// MongoDB Script for Test Data Generation
// To run this script, paste it into the MongoDB Compass shell or use the mongo shell.

// --- Clean up existing test data (optional, uncomment to use) ---
db.organizations.deleteMany({
  name: { $in: ['Test Org 1', 'Test Org 2', 'Zamahaw Pro Euro'] },
});
db.users.deleteMany({ email: /@test\.com/ });
db.members.deleteMany({});
db.cvs.deleteMany({});
db.customers.deleteMany({});
db.invites.deleteMany({});

// --- CONSTANTS ---
// Dates
const SIX_MONTHS_AGO_IN_DAYS = -180;
const YEAR_AGO_IN_DAYS = -365;
const MONTH_AGO_IN_DAYS = -30;
const MONTH_FROM_NOW_IN_DAYS = 30;
const THREE_DAYS_FROM_NOW_IN_DAYS = 3;
const fromNow = (days) => {
  const date = new Date();
  date.setDate(date.getDate() + days);
  return date;
};

const MY_USER_EMAIL = '<EMAIL>';

// --- Generate ObjectIDs ---
// Users
const org1Id = new ObjectId('a10000000000000000000000');
const org2Id = new ObjectId('a20000000000000000000000');
// org 3 is for shared users
const org4Id = new ObjectId('a40000000000000000000000');
const emptyOrgId = new ObjectId('a00000000000000000000000');
const mainOrgId = new ObjectId('a00000000000000000000001');

const org1UserOwnerId = new ObjectId('a100000000000000000000b1');
const org1UserAdminId = new ObjectId('a100000000000000000000b2');
const org1UserMemberId = new ObjectId('a100000000000000000000b3');

const org2UserOwnerId = new ObjectId('a200000000000000000000b1');
const org2UserAdminId = new ObjectId('a200000000000000000000b2');
const org2UserMemberId = new ObjectId('a200000000000000000000b3');

const sharedOwnerId = new ObjectId('a300000000000000000000b1');
const sharedMemberId = new ObjectId('a300000000000000000000b2');

const org4OwnerId = new ObjectId('a400000000000000000000b1');

const superAdminId = new ObjectId('a000000000000000000000b0');
// Users end

// Members
const org1MemberId1 = new ObjectId('a100000000000000000000c1');
const org1MemberId2 = new ObjectId('a100000000000000000000c2');
const org2MemberId1 = new ObjectId('a200000000000000000000c1');
const org2MemberId2 = new ObjectId('a200000000000000000000c2');
const activeMemberId = new ObjectId('a000000000000000000000c0');
const blankMemberId = new ObjectId('a000000000000000000000c1');

const org1Member1CvId1 = new ObjectId('a100000000000000000000d1');
// const org1Member2CvId = new ObjectId('a100000000000000000000d2');
const org2Member1CvId1 = new ObjectId('a200000000000000000000d1');
// const org2Member2CvId = new ObjectId('a200000000000000000000d2');
const relevantCvId = new ObjectId('a000000000000000000000d0');

// Customers
const org1CustomerId1 = new ObjectId('a100000000000000000000e1');
const org1CustomerId2 = new ObjectId('a100000000000000000000e2');
const org2CustomerId1 = new ObjectId('a200000000000000000000e1');
const org2CustomerId2 = new ObjectId('a200000000000000000000e2');

const org0TeladocCustomerId = new ObjectId('a000000000000000000000e0');
const org0GithubCustomerId = new ObjectId('a000000000000000000000e1');

// Invites
const org1InviteId1 = new ObjectId('a100000000000000000000f1');
const org1InviteId2 = new ObjectId('a100000000000000000000f2');

// --- Create Organizations ---
const organizationsToInsert = [
  // Main Organization
  {
    _id: mainOrgId,
    name: 'Main Organization',
    photo:
      'https://preview.redd.it/the-hulkbuster-should-have-shaken-the-entire-mcu-geopolitics-v0-1m3r3md26rcf1.jpeg?width=640&crop=smart&auto=webp&s=4985ae0a36403a81cdaa9a6afb5dd8b3b13edd3d',
    invites: [],
    cvs: [],
    locations: [],
    planStatus: 'free',
    planTier: 'free',
    muchskillsMembers: [],
    muchskillsIntegration: {
      connected: true,
      token:
        'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0ZWFtIjoiNjFhYmQwZmQzN2NhZTRlNTk4YzRkMzZmIiwidG9rZW4iOiIyYTM3MWEwYS0wM2E1LTQwZDMtYmQyMi1mYTM2NDNlZGU0ZTUiLCJpYXQiOjE3NTQ0MDM3NDZ9.ONyGRuypViFAo2Y6UrD9r5kSSU4fKIS5VASb7Ie3C7U',
    },
    createdAt: fromNow(0),
    updatedAt: fromNow(0),
    __v: 0,
  },
  // Empty Organization
  {
    _id: emptyOrgId,
    name: 'Empty Organization',
    invites: [],
    cvs: [],
    locations: [],
    planStatus: 'free',
    planTier: 'free',
    muchskillsMembers: [],
    createdAt: fromNow(0),
    updatedAt: fromNow(0),
    __v: 0,
  },
  {
    _id: org1Id,
    name: 'Test Org 1',
    photo: 'https://i.redd.it/28gpb4c9m3w51.jpg',
    invites: [],
    cvs: [],
    locations: [],
    planStatus: 'free',
    planTier: 'free',
    muchskillsMembers: [],
    muchskillsIntegration: {
      connected: true,
      token:
        'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0ZWFtIjoiNjFhYmQwZmQzN2NhZTRlNTk4YzRkMzZmIiwidG9rZW4iOiI0MDQ2OGQ2NS1kZWI2LTRmNzAtOWY2MS1jNzlmNGE0ZDRmMmMiLCJpYXQiOjE3NTE0NDk5MzV9.zTJCqBRM5WoTyuyiFg0V2iIQtnLkKK7M_9t1nWcJKLg',
    },
    createdAt: fromNow(0),
    updatedAt: fromNow(0),
    __v: 0,
  },
  {
    _id: org2Id,
    name: 'Test Org 2',
    photo:
      'https://preview.redd.it/oc72qsj0gmo61.jpg?width=1080&crop=smart&auto=webp&s=f7244ab4398a9422a27b5ce5a797603db47dc8d4',
    invites: [],
    cvs: [],
    locations: [],
    planStatus: 'free',
    planTier: 'free',
    muchskillsMembers: [],
    muchskillsIntegration: {
      connected: true,
      token:
        'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0ZWFtIjoiNjFhYmQwZmQzN2NhZTRlNTk4YzRkMzZmIiwidG9rZW4iOiIyN2E1N2ZkOS04YTdlLTQwZDItYjY1Zi1mNTkxODg2ODgwM2YiLCJpYXQiOjE3NDk4MjkzNDZ9.LFtXh2MTtVjm5FysOHQTYEHcPaGXoRVDtySTXk1wOYYg',
    },
    createdAt: fromNow(0),
    updatedAt: fromNow(0),
    __v: 0,
  },
  {
    _id: org4Id,
    name: 'Zamahaw Pro Euro',
    invites: [],
    cvs: [],
    locations: [],
    planStatus: 'active',
    planTier: 'pro',
    muchskillsMembers: [],
    createdAt: fromNow(-1),
    updatedAt: fromNow(0),
    __v: 0,
    stripeCustomerId: 'cus_SbBENRUZfc4H6p',
    currentPeriodEnds: fromNow(30),
    planId: 'price_1RVXuTPPczFzf6WlZZOsMViu',
    stripeSubscriptionId: 'sub_1RfysMPPczFzf6WleBLnwVgQ',
  },
];

// --- Create Users ---
// Using a pre-hashed password for 'password123' for simplicity from your example.
const hashedPassword =
  '$2b$10$.Nd6d.QGRlUD42ULl1mhKOJT5hCC4kw4bFRY.w/F6Ad3Pv5gysYKy';

const usersToInsert = [
  // super admin
  {
    _id: superAdminId,
    email: '<EMAIL>',
    firstName: 'Super',
    lastName: 'Admin',
    password: hashedPassword,
    status: 1,
    organization: mainOrgId,
    availableOrganizations: [
      {
        orgId: emptyOrgId,
        role: 'OWNER',
        joinedDate: fromNow(0),
      },
      {
        orgId: mainOrgId,
        role: 'OWNER',
        joinedDate: fromNow(0),
      },
      {
        orgId: org1Id,
        role: 'ADMIN',
        joinedDate: fromNow(0),
      },
      {
        orgId: org2Id,
        role: 'ADMIN',
        joinedDate: fromNow(0),
      },
      {
        orgId: org4Id,
        role: 'ADMIN',
        joinedDate: fromNow(0),
      },
    ],
    avatar: 'https://i.redd.it/x8aaxbjh8r6a1.jpg',
    createdAt: fromNow(0),
    updatedAt: fromNow(0),
    __v: 0,
  },
  // Users for Organization 1
  {
    _id: org1UserOwnerId,
    email: '<EMAIL>',
    firstName: 'Owner',
    lastName: 'Org1',
    password: hashedPassword,
    status: 1,
    organization: org1Id,
    availableOrganizations: [
      {
        orgId: org1Id,
        role: 'OWNER',
        joinedDate: fromNow(0),
      },
    ],
    avatar:
      'https://preview.redd.it/99erl81sxr091.jpg?width=1080&crop=smart&auto=webp&s=5d95db434b4831e574c45d49c7367d19a6f0e1f3',
    createdAt: fromNow(0),
    updatedAt: fromNow(0),
    __v: 0,
  },
  {
    _id: org1UserAdminId,
    email: '<EMAIL>',
    firstName: 'Admin',
    lastName: 'Org1',
    password: hashedPassword,
    status: 1,
    organization: org1Id,
    availableOrganizations: [
      {
        orgId: org1Id,
        role: 'ADMIN',
        joinedDate: fromNow(0),
      },
    ],
    avatar:
      'https://preview.redd.it/99erl81sxr091.jpg?width=1080&crop=smart&auto=webp&s=5d95db434b4831e574c45d49c7367d19a6f0e1f3',
    createdAt: fromNow(0),
    updatedAt: fromNow(0),
    __v: 0,
  },
  {
    _id: org1UserMemberId,
    email: '<EMAIL>',
    firstName: 'Member',
    lastName: 'Org1',
    password: hashedPassword,
    status: 1,
    organization: org1Id,
    availableOrganizations: [
      {
        orgId: org1Id,
        role: 'MEMBER',
        joinedDate: fromNow(0),
      },
    ],
    avatar:
      'https://preview.redd.it/99erl81sxr091.jpg?width=1080&crop=smart&auto=webp&s=5d95db434b4831e574c45d49c7367d19a6f0e1f3',
    createdAt: fromNow(0),
    updatedAt: fromNow(0),
    __v: 0,
  },
  // Users for Organization 2
  {
    _id: org2UserOwnerId,
    email: '<EMAIL>',
    firstName: 'Owner',
    lastName: 'Org2',
    password: hashedPassword,
    status: 1,
    organization: org2Id,
    availableOrganizations: [
      {
        orgId: org2Id,
        role: 'OWNER',
        joinedDate: fromNow(0),
      },
    ],
    avatar:
      'https://preview.redd.it/99erl81sxr091.jpg?width=1080&crop=smart&auto=webp&s=5d95db434b4831e574c45d49c7367d19a6f0e1f3',
    createdAt: fromNow(0),
    updatedAt: fromNow(0),
    __v: 0,
  },
  {
    _id: org2UserAdminId,
    email: '<EMAIL>',
    firstName: 'Admin',
    lastName: 'Org2',
    password: hashedPassword,
    status: 1,
    organization: org2Id,
    availableOrganizations: [
      {
        orgId: org2Id,
        role: 'ADMIN',
        joinedDate: fromNow(0),
      },
    ],
    avatar:
      'https://preview.redd.it/99erl81sxr091.jpg?width=1080&crop=smart&auto=webp&s=5d95db434b4831e574c45d49c7367d19a6f0e1f3',
    createdAt: fromNow(0),
    updatedAt: fromNow(0),
    __v: 0,
  },
  {
    _id: org2UserMemberId,
    email: '<EMAIL>',
    firstName: 'Member',
    lastName: 'Org2',
    password: hashedPassword,
    status: 1,
    organization: org2Id,
    availableOrganizations: [
      {
        orgId: org2Id,
        role: 'MEMBER',
        joinedDate: fromNow(0),
      },
    ],
    avatar:
      'https://preview.redd.it/99erl81sxr091.jpg?width=1080&crop=smart&auto=webp&s=5d95db434b4831e574c45d49c7367d19a6f0e1f3',
    createdAt: fromNow(0),
    updatedAt: fromNow(0),
    __v: 0,
  },
  // Shared Users
  // Shared User Member
  {
    _id: sharedMemberId,
    email: '<EMAIL>',
    firstName: 'Member',
    lastName: 'Shared',
    password: hashedPassword,
    status: 1,
    organization: org1Id, // Default organization
    availableOrganizations: [
      {
        orgId: org1Id,
        role: 'MEMBER',
        joinedDate: fromNow(0),
      },
      {
        orgId: org2Id,
        role: 'MEMBER',
        joinedDate: fromNow(0),
      },
    ],
    avatar:
      'https://preview.redd.it/99erl81sxr091.jpg?width=1080&crop=smart&auto=webp&s=5d95db434b4831e574c45d49c7367d19a6f0e1f3',
    createdAt: fromNow(0),
    updatedAt: fromNow(0),
    __v: 0,
  },
  // Shared User Owner
  {
    _id: sharedOwnerId,
    email: '<EMAIL>',
    firstName: 'Owner',
    lastName: 'Shared',
    password: hashedPassword,
    status: 1,
    organization: org1Id, // Default organization
    availableOrganizations: [
      {
        orgId: org1Id,
        role: 'OWNER',
        joinedDate: fromNow(0),
      },
      {
        orgId: org2Id,
        role: 'OWNER',
        joinedDate: fromNow(0),
      },
    ],
    avatar:
      'https://preview.redd.it/99erl81sxr091.jpg?width=1080&crop=smart&auto=webp&s=5d95db434b4831e574c45d49c7367d19a6f0e1f3',
    createdAt: fromNow(0),
    updatedAt: fromNow(0),
    __v: 0,
  },
  // User for Organization 4
  {
    _id: org4OwnerId,
    email: MY_USER_EMAIL,
    firstName: 'Owner',
    lastName: 'Zamahaw',
    password: hashedPassword,
    status: 1,
    organization: org4Id, // Default organization
    availableOrganizations: [
      {
        orgId: org4Id,
        role: 'OWNER',
        joinedDate: fromNow(0),
      },
    ],
    avatar:
      'https://preview.redd.it/99erl81sxr091.jpg?width=1080&crop=smart&auto=webp&s=5d95db434b4831e574c45d49c7367d19a6f0e1f3',
    createdAt: fromNow(0),
    updatedAt: fromNow(0),
    __v: 0,
  },
];

// --- Create Members ---
const membersToInsert = [
  // Active Member
  {
    _id: activeMemberId,
    organization: mainOrgId,
    firstName: 'Vladyslav',
    lastName: 'Knysh',
    email: '<EMAIL>',
    source: 'cvinventory',
    languages: ['English', 'Ukrainian'],
    clients: [],
    socials: [],
    cvs: [relevantCvId],
    workExperience: [
      {
        companyName: 'Archive.com',
        roleTitle: 'Middle Software Engineer',
        description:
          'Customer: US-based Product Company\nProject: AI influencer marketing platform\nTeam Size: 7 Full-Stack devs, 3 Designers, 1 Tech Lead\nRole: Full Stack Ruby on Rails & React Developer\n\nTasks performed:\n• Build UI according to the design\n• Implemented GraphQL endpoints\n• Wrote acceptance tests on Cypress\n• Estimation\n• Fixing bugs',
        startDate: fromNow(-90), // Oct 2024
        endDate: fromNow(-30), // Jan 2025
        isCurrent: false,
      },
      {
        companyName: 'Coax Software',
        roleTitle: 'Junior Strong Software Engineer',
        description:
          'Customer: UK-based logistics company\nProject: Job Management App for Drivers, Carlift app for Dubai\nTeam Size: 3 Backend, 2 Frontend, 2Mobile, 2-3 QA, 1 PM\nRole: Ruby on Rails API Developer\n\nTasks performed:\n• Implemented MVP API for Job platform for drivers\n• Connected person verification 3rd party API\n• Fixing bugs\n• Helped with debugging on Mobile Devices\n• Implemented MVP for Bus app\n• Planning & Estimation',
        startDate: fromNow(-270), // Jan 2024
        endDate: fromNow(-90), // Oct 2024
        isCurrent: false,
      },
      {
        companyName: 'SoftServe',
        roleTitle: 'Junior Software Engineer',
        description:
          'Customer: US-based healthcare company\nProject: Development of registration for Canadian users\nTeam Size: 3 Back-end developers, 1 QA Engineer, 1 BA, 1 PM\nRole: Developer\n\nTasks performed:\n• Implemented new registration flow\n• Implemented some front-end features and bugfixes\n• Cross-Team communication\n• Research\n• Communication with product owners\n• Writing technical documentation on confluence\n• Code review\n• Shared project knowledge with newcomers and helped them with local machine setup\n• Assistance with demos\n\nEnvironment: GraphQL APIs, GraphiQL',
        startDate: fromNow(-515), // Aug 2023
        endDate: fromNow(-365), // Jan 2024
        isCurrent: false,
      },
      {
        companyName: 'SoftServe',
        roleTitle: 'Trainee, Junior Software Engineer',
        description:
          'Customer: US-based healthcare company\nProject: Development of messaging system\nTeam Size: 2 Front-end developers, 7 Back-end developers, 3 QA Engineers, 1 BA, 1 PM\nRole: Developer\n\nTasks performed:\n• Implemented new features\n• Created unit and integration tests for new and already existing code\n• Estimated stories and took part in stories grooming, sprints planning\n• Provided assistance to QA, Project Manager, to resolve problems\n• Helped other teams with the integration of company services\n• Refactoring\n• Bugfixes\n\nEnvironment: Ruby on Rails, MySQL, REST, git, Jira, Confluence, GitHub, RubyMine, VScode, Jenkins, Rubocop, React, JavaScript, HTML, CSS, Sidekiq, Redis, Docker, PubNub',
        startDate: fromNow(-880), // Aug 2022
        endDate: fromNow(-605), // Jun 2023
        isCurrent: false,
      },
    ],
    education: [
      {
        schoolName: 'Vasyl Stefanyk Precarpathian National University',
        degree: 'Master of Science in Computer Science',
        description: 'Currently pursuing advanced studies in Computer Science.',
        startDate: fromNow(-365), // 2024
        endDate: null, // Present
      },
      {
        schoolName: 'Vasyl Stefanyk Precarpathian National University',
        degree: 'Bachelor of Science in Computer Science',
        description: 'Completed undergraduate studies in Computer Science.',
        startDate: fromNow(-1825), // 2020
        endDate: fromNow(-365), // 2024
      },
      {
        schoolName: 'SoftServe Academy',
        degree: 'Ruby Development Certificate',
        description:
          'Series DU No 8388/2022 - Specialized training in Ruby development.',
        startDate: fromNow(-880), // 2022
        endDate: fromNow(-790), // 2022
      },
    ],
    certifications: [
      {
        msId: 'cert-001', // You'll need to generate appropriate IDs
        name: 'Ruby Development',
        organization: 'SoftServe Academy',
      },
    ],
    skills: [],
    location: 'Ukraine',
    telephone: '+380960677249',
    currentPosition: 'Full Stack Developer',
    // avatar: 'https://i.redd.it/x8aaxbjh8r6a1.jpg',
    avatar: `https://media.licdn.com/dms/image/v2/C4D03AQHCmkMxuXa7HA/profile-displayphoto-shrink_400_400/profile-displayphoto-shrink_400_400/0/1658303594318?e=1756944000&v=beta&t=F5JGyreLY1Km8VyJObziS2ueEXCV3rOVhDFyq87v49I`,
    costRate: {
      currency: 'usd',
      amount: 10,
      timeRange: 'hour',
      _id: new ObjectId(),
    },
    createdAt: fromNow(0),
    updatedAt: fromNow(0),
    __v: 0,
  },
  {
    _id: blankMemberId,
    organization: mainOrgId,
    firstName: 'Blank',
    lastName: 'Profile',
    email: '<EMAIL>',
    source: 'cvinventory',
    languages: [],
    clients: [],
    socials: [],
    cvs: [],
    workExperience: [],
    education: [],
    certifications: [],
    skills: [],
    location: '',
    telephone: '',
    currentPosition: '',
    yearsOfExperience: 5,
    avatar: '',
    costRate: {
      currency: 'usd',
      amount: 0,
      timeRange: 'month',
      _id: new ObjectId(),
    },
    createdAt: fromNow(0),
    updatedAt: fromNow(0),
    __v: 0,
  },
  // Members for Organization 1
  {
    _id: org1MemberId1,
    organization: org1Id,
    firstName: 'Member1',
    lastName: 'Org1',
    email: '<EMAIL>',
    source: 'cvinventory',
    languages: ['English', 'Ukrainian'],
    clients: [],
    socials: ['https://www.linkedin.com/in/vladyslav-knysh/'],
    cvs: [org1Member1CvId1],
    workExperience: [],
    education: [],
    certifications: [],
    skills: [],
    location: 'Ukraine',
    telephone: '+380123456789',
    currentPosition: 'Software Engineer',
    yearsOfExperience: 5,
    avatar:
      'https://preview.redd.it/99erl81sxr091.jpg?width=1080&crop=smart&auto=webp&s=5d95db434b4831e574c45d49c7367d19a6f0e1f3',
    costRate: {
      currency: 'usd',
      amount: 50,
      timeRange: 'hour',
      _id: new ObjectId(),
    },
    costToCompany: {
      currency: 'usd',
      amount: 0,
      timeRange: 'month',
      _id: new ObjectId(),
    },
    createdAt: fromNow(0),
    updatedAt: fromNow(0),
    __v: 0,
  },
  {
    _id: org1MemberId2,
    organization: org1Id,
    firstName: 'Member2',
    lastName: 'Org1',
    email: '<EMAIL>',
    source: 'cvinventory',
    languages: ['English', 'Ukrainian'],
    clients: [],
    socials: [],
    cvs: [],
    workExperience: [],
    education: [],
    certifications: [],
    skills: [],
    location: 'Ukraine',
    telephone: '+380123456789',
    currentPosition: 'Software Engineer',
    yearsOfExperience: 5,
    avatar:
      'https://preview.redd.it/99erl81sxr091.jpg?width=1080&crop=smart&auto=webp&s=5d95db434b4831e574c45d49c7367d19a6f0e1f3',
    costRate: {
      currency: 'usd',
      amount: 50,
      timeRange: 'hour',
      _id: new ObjectId(),
    },
    createdAt: fromNow(0),
    updatedAt: fromNow(0),
    __v: 0,
  },
  // Members for Organization 2
  {
    _id: org2MemberId1,
    organization: org2Id,
    firstName: 'Member1',
    lastName: 'Org2',
    email: '<EMAIL>',
    source: 'cvinventory',
    languages: ['English', 'Ukrainian'],
    clients: [],
    socials: [],
    cvs: [org2Member1CvId1],
    workExperience: [],
    education: [],
    certifications: [],
    skills: [],
    location: 'Ukraine',
    telephone: '+380123456789',
    currentPosition: 'Software Engineer',
    yearsOfExperience: 5,
    avatar:
      'https://preview.redd.it/99erl81sxr091.jpg?width=1080&crop=smart&auto=webp&s=5d95db434b4831e574c45d49c7367d19a6f0e1f3',
    costRate: {
      currency: 'usd',
      amount: 50,
      timeRange: 'hour',
      _id: new ObjectId(),
    },
    createdAt: fromNow(0),
    updatedAt: fromNow(0),
    __v: 0,
  },
  {
    _id: org2MemberId2,
    organization: org2Id,
    firstName: 'Member2',
    lastName: 'Org2',
    email: '<EMAIL>',
    source: 'cvinventory',
    languages: ['English', 'Ukrainian'],
    clients: [],
    socials: [],
    cvs: [],
    workExperience: [],
    education: [],
    certifications: [],
    skills: [],
    location: 'Ukraine',
    telephone: '+380123456789',
    currentPosition: 'Software Engineer',
    yearsOfExperience: 5,
    avatar:
      'https://preview.redd.it/99erl81sxr091.jpg?width=1080&crop=smart&auto=webp&s=5d95db434b4831e574c45d49c7367d19a6f0e1f3',
    costRate: {
      currency: 'usd',
      amount: 50,
      timeRange: 'hour',
      _id: new ObjectId(),
    },
    createdAt: fromNow(0),
    updatedAt: fromNow(0),
    __v: 0,
  },
];

const cvsToInsert = [
  {
    _id: org1Member1CvId1,
    // createdAt: fromNow(SIX_MONTHS_AGO_IN_DAYS),
    createdAt: fromNow(YEAR_AGO_IN_DAYS),
    member: org1MemberId1,
    organization: org1Id,
    template: 'europass',
    status: 'active',
    preferences: {
      title: 'Org 1 Member 1 CV',
      maxPages: 3,
      costRate: {
        currency: 'usd',
        amount: 50,
        timeRange: 'hour',
        _id: new ObjectId(),
      },
      customer: org1CustomerId1,
      link: 'http://linkedin.com/in/johndoe',
      contractStart: fromNow(-1),
      contractEnd: fromNow(32),
      autoRenewal: true,
      leastExperience: 5,
      maxExperience: 10,
      description:
        '<p>A highly motivated and experienced software engineer with a passion for creating robust and scalable web applications.</p>',
      _id: new ObjectId(),
    },
    sections: {
      personalInfo: {
        order: 1,
        title: 'Personal Information',
        active: true,
        data: {
          firstName: {
            value: 'John',
            active: true,
            _id: new ObjectId(),
          },
          lastName: {
            value: 'Doe',
            active: true,
            _id: new ObjectId(),
          },
          jobTitle: {
            value: 'Senior Software Engineer',
            active: true,
            _id: new ObjectId(),
          },
          location: {
            value: 'New York, USA',
            active: true,
            _id: new ObjectId(),
          },
          nationality: {
            value: 'American',
            active: true,
            _id: new ObjectId(),
          },
          email: {
            value: '<EMAIL>',
            active: true,
            _id: new ObjectId(),
          },
          telephone: {
            value: '+11234567890',
            active: true,
            _id: new ObjectId(),
          },
          socials: {
            inputs: ['https://www.linkedin.com/in/johndoe'],
            active: true,
            _id: new ObjectId(),
          },
          _id: new ObjectId(),
        },
        _id: new ObjectId(),
      },
      aboutMe: {
        order: 2,
        title: 'About Me',
        active: true,
        data: {
          description:
            '<p>I am a software engineer with over 8 years of experience in the industry. I am passionate about building high-quality software and I am always looking for new challenges.</p>',
          _id: new ObjectId(),
        },
        _id: new ObjectId(),
      },
      workHistory: {
        order: 3,
        title: 'Work History',
        active: true,
        data: [
          {
            active: true,
            companyName: 'Tech Solutions Inc.',
            roleTitle: 'Senior Software Engineer',
            description:
              '<p>Led the development of a new e-commerce platform, resulting in a 20% increase in sales. Mentored junior developers and conducted code reviews.</p>',
            startDate: fromNow(-1264),
            endDate: fromNow(-2),
            isCurrent: true,
            _id: new ObjectId(),
          },
          {
            active: true,
            companyName: 'Web Innovations LLC',
            roleTitle: 'Software Engineer',
            description:
              '<p>Developed and maintained features for a large-scale social media application. Collaborated with a team of engineers to deliver high-quality code on time.</p>',
            startDate: fromNow(-2235),
            endDate: fromNow(-1269),
            isCurrent: false,
            _id: new ObjectId(),
          },
        ],
        _id: new ObjectId(),
      },
      education: {
        order: 4,
        title: 'Education',
        active: true,
        data: [
          {
            active: true,
            schoolName: 'State University of New York',
            degree: 'Bachelor of Science in Computer Science',
            description: 'Graduated with honors.',
            startDate: fromNow(-3592),
            endDate: fromNow(-2240),
            _id: new ObjectId(),
          },
        ],
        _id: new ObjectId(),
      },
      certifications: {
        order: 5,
        title: 'Certifications',
        active: true,
        data: {
          description: '<p>AWS Certified Developer - Associate</p>',
          _id: new ObjectId(),
        },
        _id: new ObjectId(),
      },
      skills: {
        order: 6,
        title: 'Skills',
        active: true,
        data: {
          description:
            '<p>JavaScript, TypeScript, React, Node.js, MongoDB, PostgreSQL, Docker, Kubernetes</p>',
          _id: new ObjectId(),
        },
        _id: new ObjectId(),
      },
      languages: {
        order: 7,
        title: 'Languages',
        active: true,
        data: {
          languages: ['English', 'Spanish'],
          _id: new ObjectId(),
        },
        _id: new ObjectId(),
      },
      customSections: [],
      _id: new ObjectId(),
    },
    updatedAt: fromNow(0),
    __v: 0,
  },
  {
    _id: org2Member1CvId1,
    member: org2MemberId1,
    organization: org2Id,
    template: 'europass',
    status: 'active',
    preferences: {
      title: 'Org 2 Member 1 CV',
      maxPages: 2,
      costRate: {
        currency: 'eur',
        amount: 60,
        timeRange: 'hour',
        _id: new ObjectId(),
      },
      customer: org2CustomerId1,
      link: 'http://linkedin.com/in/janesmith',
      contractStart: fromNow(8),
      contractEnd: fromNow(90),
      autoRenewal: false,
      leastExperience: 3,
      maxExperience: 7,
      description:
        '<p>A creative and detail-oriented frontend developer with a strong background in user interface design and development.</p>',
      _id: new ObjectId(),
    },
    sections: {
      personalInfo: {
        order: 1,
        title: 'Personal Information',
        active: true,
        data: {
          firstName: {
            value: 'Jane',
            active: true,
            _id: new ObjectId(),
          },
          lastName: {
            value: 'Smith',
            active: true,
            _id: new ObjectId(),
          },
          jobTitle: {
            value: 'Lead Frontend Developer',
            active: true,
            _id: new ObjectId(),
          },
          location: {
            value: 'London, UK',
            active: true,
            _id: new ObjectId(),
          },
          nationality: {
            value: 'British',
            active: true,
            _id: new ObjectId(),
          },
          email: {
            value: '<EMAIL>',
            active: true,
            _id: new ObjectId(),
          },
          telephone: {
            value: '+441234567890',
            active: true,
            _id: new ObjectId(),
          },
          socials: {
            inputs: ['https://www.linkedin.com/in/janesmith'],
            active: true,
            _id: new ObjectId(),
          },
          _id: new ObjectId(),
        },
        _id: new ObjectId(),
      },
      aboutMe: {
        order: 2,
        title: 'About Me',
        active: true,
        data: {
          description:
            '<p>I am a frontend developer with a passion for creating beautiful and intuitive user interfaces. I have a strong understanding of user experience principles and I am proficient in a variety of frontend technologies.</p>',
          _id: new ObjectId(),
        },
        _id: new ObjectId(),
      },
      workHistory: {
        order: 3,
        title: 'Work History',
        active: true,
        data: [
          {
            active: true,
            companyName: 'Creative Designs Co.',
            roleTitle: 'Lead Frontend Developer',
            description:
              "<p>Led the redesign of the company's flagship product, resulting in a 30% improvement in user engagement. Managed a team of frontend developers and established best practices for code quality and performance.</p>",
            startDate: fromNow(-1575),
            endDate: fromNow(-12),
            isCurrent: true,
            _id: new ObjectId(),
          },
          {
            active: true,
            companyName: 'Digital Agency Pro',
            roleTitle: 'Frontend Developer',
            description:
              '<p>Developed responsive and interactive websites for a variety of clients. Collaborated with designers and backend developers to create seamless user experiences.</p>',
            startDate: fromNow(-2495),
            endDate: fromNow(-1580),
            isCurrent: false,
            _id: new ObjectId(),
          },
        ],
        _id: new ObjectId(),
      },
      education: {
        order: 4,
        title: 'Education',
        active: true,
        data: [
          {
            active: true,
            schoolName: 'University of Arts',
            degree: 'Bachelor of Fine Arts in Graphic Design',
            description: 'Focused on web design and user interface design.',
            startDate: fromNow(-3957),
            endDate: fromNow(-2573),
            _id: new ObjectId(),
          },
        ],
        _id: new ObjectId(),
      },
      certifications: {
        order: 5,
        title: 'Certifications',
        active: true,
        data: {
          description: '<p>Certified UX Designer</p>',
          _id: new ObjectId(),
        },
        _id: new ObjectId(),
      },
      skills: {
        order: 6,
        title: 'Skills',
        active: true,
        data: {
          description:
            '<p>HTML, CSS, JavaScript, React, Vue.js, Figma, Sketch, Adobe Creative Suite</p>',
          _id: new ObjectId(),
        },
        _id: new ObjectId(),
      },
      languages: {
        order: 7,
        title: 'Languages',
        active: true,
        data: {
          languages: ['English', 'French'],
          _id: new ObjectId(),
        },
        _id: new ObjectId(),
      },
      customSections: [],
      _id: new ObjectId(),
    },
    createdAt: fromNow(0),
    updatedAt: fromNow(0),
    __v: 0,
  },
  {
    _id: relevantCvId,
    member: activeMemberId,
    organization: mainOrgId,
    template: 'clean',
    status: 'active',
    preferences: {
      title: 'Knysh Vladyslav - Full Stack Developer CV',
      maxPages: 2,
      costRate: {
        currency: 'eur',
        amount: 60,
        timeRange: 'hour',
        _id: new ObjectId(),
      },
      link: 'https://github.com/vladknysh',
      contractStart: fromNow(8),
      contractEnd: fromNow(90),
      autoRenewal: false,
      leastExperience: 2,
      maxExperience: 5,
      description:
        '<p>A dedicated full-stack developer with strong expertise in Ruby on Rails and React, currently pursuing MS in Computer Science.</p>',
      _id: new ObjectId(),
    },
    sections: {
      personalInfo: {
        order: 1,
        title: 'Personal Information',
        active: true,
        data: {
          firstName: {
            value: 'Vladyslav',
            active: true,
            _id: new ObjectId(),
          },
          lastName: {
            value: 'Knysh',
            active: true,
            _id: new ObjectId(),
          },
          jobTitle: {
            value:
              'Middle Software Engineer - Full Stack Ruby on Rails & React Developer',
            active: true,
            _id: new ObjectId(),
          },
          location: {
            value: 'Ivano-Frankivsk, Ukraine',
            active: true,
            _id: new ObjectId(),
          },
          nationality: {
            value: 'Ukrainian',
            active: true,
            _id: new ObjectId(),
          },
          email: {
            value: '<EMAIL>',
            active: true,
            _id: new ObjectId(),
          },
          telephone: {
            value: '+380957777330',
            active: true,
            _id: new ObjectId(),
          },
          socials: {
            inputs: [
              'https://linkedin.com/in/vladyslav-knysh/',
              'https://github.com/vladknysh',
              'https://t.me/ea_vlad',
            ],
            active: true,
            _id: new ObjectId(),
          },
          _id: new ObjectId(),
        },
        _id: new ObjectId(),
      },
      aboutMe: {
        order: 2,
        title: 'Summary',
        active: true,
        data: {
          description: `I am a student pursuing a MS in Computer Science at Vasyl Stefanyk Precarpathian
National University with more than 2 years of experience as a Ruby developer and up to 10
months of experience with React. I am interested in the position of Ruby on Rails & React
Developer.

My key area of technical experience covers back-end development with Ruby on
Rails, MySql, PostgreSQL, Redis, Sidekiq, ElasticSearch, REST / GraphQL APIs, AWS, and
Docker. Additionally, I understand front-end technologies such as React, Redux, JavaScript,
HTML, and CSS.

In my previous project I have contributed by writing code, refactoring, creating new
features, fixing bugs, technical documentation maintenance, writing unit and integration
tests. I have performed code reviews for software engineers of my level and below.

Also, I am proficient in using Git version control and have experience in deploying
applications and using continuous integration systems.

I am excited about the opportunity to contribute to your organization and further
develop my skills in the field.`,
          _id: new ObjectId(),
        },
        _id: new ObjectId(),
      },
      workHistory: {
        order: 3,
        title: 'Work Experience',
        active: true,
        data: [
          {
            active: true,
            companyName: 'Archive.com',
            roleTitle: 'Middle Software Engineer',
            description:
              '<p><strong>Customer:</strong> US-based Product Company<br><strong>Project:</strong> AI influencer marketing platform<br><strong>Team Size:</strong> 7 Full-Stack devs, 3 Designers, 1 Tech Lead<br><strong>Role:</strong> Full Stack Ruby on Rails & React Developer<br><br><strong>Tasks performed:</strong><br>• Build UI according to the design<br>• Implemented GraphQL endpoints<br>• Wrote acceptance tests on Cypress<br>• Estimation<br>• Fixing bugs</p>',
            startDate: fromNow(-90), // Oct 2024
            endDate: fromNow(-30), // Jan 2025
            isCurrent: false,
            _id: new ObjectId(),
          },
          {
            active: true,
            companyName: 'Coax Software',
            roleTitle: 'Junior Strong Software Engineer',
            description:
              '<p><strong>Customer:</strong> UK-based logistics company<br><strong>Project:</strong> Job Management App for Drivers, Carlift app for Dubai<br><strong>Team Size:</strong> 3 Backend, 2 Frontend, 2Mobile, 2-3 QA, 1 PM<br><strong>Role:</strong> Ruby on Rails API Developer<br><br><strong>Tasks performed:</strong><br>• Implemented MVP API for Job platform for drivers<br>• Connected person verification 3rd party API<br>• Fixing bugs<br>• Helped with debugging on Mobile Devices<br>• Implemented MVP for Bus app<br>• Planning & Estimation</p>',
            startDate: fromNow(-270), // Jan 2024
            endDate: fromNow(-90), // Oct 2024
            isCurrent: false,
            _id: new ObjectId(),
          },
          {
            active: true,
            companyName: 'SoftServe',
            roleTitle: 'Junior Software Engineer',
            description:
              '<p><strong>Customer:</strong> US-based healthcare company<br><strong>Project:</strong> Development of registration for Canadian users<br><strong>Team Size:</strong> 3 Back-end developers, 1 QA Engineer, 1 BA, 1 PM<br><strong>Role:</strong> Developer<br><br><strong>Tasks performed:</strong><br>• Implemented new registration flow<br>• Implemented some front-end features and bugfixes<br>• Cross-Team communication<br>• Research<br>• Communication with product owners<br>• Writing technical documentation on confluence<br>• Code review<br>• Shared project knowledge with newcomers and helped them with local machine setup<br>• Assistance with demos<br><br><strong>Environment:</strong> GraphQL APIs, GraphiQL</p>',
            startDate: fromNow(-515), // Aug 2023
            endDate: fromNow(-365), // Jan 2024
            isCurrent: false,
            _id: new ObjectId(),
          },
          {
            active: true,
            companyName: 'SoftServe',
            roleTitle: 'Trainee, Junior Software Engineer',
            description:
              '<p><strong>Customer:</strong> US-based healthcare company<br><strong>Project:</strong> Development of messaging system<br><strong>Team Size:</strong> 2 Front-end developers, 7 Back-end developers, 3 QA Engineers, 1 BA, 1 PM<br><strong>Role:</strong> Developer<br><br><strong>Tasks performed:</strong><br>• Implemented new features<br>• Created unit and integration tests for new and already existing code<br>• Estimated stories and took part in stories grooming, sprints planning<br>• Provided assistance to QA, Project Manager, to resolve problems<br>• Helped other teams with the integration of company services<br>• Refactoring<br>• Bugfixes<br><br><strong>Environment:</strong> Ruby on Rails, MySQL, REST, git, Jira, Confluence, GitHub, RubyMine, VScode, Jenkins, Rubocop, React, JavaScript, HTML, CSS, Sidekiq, Redis, Docker, PubNub</p>',
            startDate: fromNow(-880), // Aug 2022
            endDate: fromNow(-605), // Jun 2023
            isCurrent: false,
            _id: new ObjectId(),
          },
        ],
        _id: new ObjectId(),
      },
      education: {
        order: 4,
        title: 'Education',
        active: true,
        data: [
          {
            active: true,
            schoolName: 'Vasyl Stefanyk Precarpathian National University',
            degree: 'Master of Science in Computer Science',
            description:
              'Currently pursuing advanced studies in Computer Science.',
            startDate: fromNow(-365), // 2024
            endDate: null, // Present
            _id: new ObjectId(),
          },
          {
            active: true,
            schoolName: 'Vasyl Stefanyk Precarpathian National University',
            degree: 'Bachelor of Science in Computer Science',
            description: 'Completed undergraduate studies in Computer Science.',
            startDate: fromNow(-1825), // 2020
            endDate: fromNow(-365), // 2024
            _id: new ObjectId(),
          },
          {
            active: true,
            schoolName: 'SoftServe Academy',
            degree: 'Ruby Development Certificate',
            description:
              'Series DU No 8388/2022 - Specialized training in Ruby development.',
            startDate: fromNow(-880), // 2022
            endDate: fromNow(-790), // 2022
            _id: new ObjectId(),
          },
        ],
        _id: new ObjectId(),
      },
      certifications: {
        order: 5,
        title: 'Certifications',
        active: true,
        data: {
          description:
            '<p>SoftServe Academy - Ruby Development (Series DU No 8388/2022)</p>',
          _id: new ObjectId(),
        },
        _id: new ObjectId(),
      },
      skills: {
        order: 6,
        title: 'Skills',
        active: true,
        data: {
          description:
            '<p><strong>Back-end:</strong> Ruby, Ruby on Rails, RSpec, MySQL, PostgreSQL, ElasticSearch, Redis, Sidekiq, REST/GraphQL APIs<br><br><strong>Front-end:</strong> JavaScript, React, Redux Toolkit, HTML, CSS<br><br><strong>AWS:</strong> Amazon Simple Storage Service (S3)<br><br><strong>Operating systems:</strong> Linux, Windows, MacOS<br><br><strong>Additional tools:</strong> Docker, Rancher<br><br><strong>Deployment & CI:</strong> Jenkins, Heroku, Netlify<br><br><strong>Testing:</strong> RSpec, Unit testing, Integration testing, TDD, API Testing (Postman, GraphiQL), Cypress, Capybara<br><br><strong>Project Management:</strong> JIRA, Trello, Notion<br><br><strong>Documentation:</strong> Confluence, Notion<br><br><strong>Version control:</strong> Git</p>',
          _id: new ObjectId(),
        },
        _id: new ObjectId(),
      },
      languages: {
        order: 7,
        title: 'Languages',
        active: true,
        data: {
          languages: ['Ukrainian', 'English'],
          _id: new ObjectId(),
        },
        _id: new ObjectId(),
      },
      customSections: [
        {
          order: 8,
          title: 'Hackathons & Competitions',
          active: true,
          data: {
            description: '<p>INT20H - Second Place, 2023</p>',
            _id: new ObjectId(),
          },
          _id: new ObjectId(),
        },
        {
          order: 8,
          title: 'Open Source Contributions',
          active: true,
          data: {
            description: '<p>Roocode, Excalidraw</p>',
            _id: new ObjectId(),
          },
          _id: new ObjectId(),
        },
      ],
      _id: new ObjectId(),
    },
    createdAt: fromNow(0),
    updatedAt: fromNow(0),
    __v: 0,
  },
];

const customersToInsert = [
  {
    _id: org1CustomerId1,
    name: 'Customer 1 for Org 1',
    organization: org1Id,
    createdAt: fromNow(0),
    updatedAt: fromNow(0),
    __v: 0,
  },
  {
    _id: org1CustomerId2,
    name: 'Customer 2 for Org 1',
    organization: org1Id,
    createdAt: fromNow(0),
    updatedAt: fromNow(0),
    __v: 0,
  },
  {
    _id: org2CustomerId1,
    name: 'Customer 1 for Org 2',
    organization: org2Id,
    createdAt: fromNow(0),
    updatedAt: fromNow(0),
    __v: 0,
  },
  {
    _id: org2CustomerId2,
    name: 'Customer 2 for Org 2',
    organization: org2Id,
    createdAt: fromNow(0),
    updatedAt: fromNow(0),
    __v: 0,
  },
  {
    _id: org0TeladocCustomerId,
    name: 'Teladoc',
    organization: mainOrgId,
    createdAt: fromNow(0),
    updatedAt: fromNow(0),
    __v: 0,
  },
  {
    _id: org0GithubCustomerId,
    name: 'Gitlab',
    organization: mainOrgId,
    createdAt: fromNow(0),
    updatedAt: fromNow(0),
    __v: 0,
  },
];

const invitesToInsert = [
  {
    _id: org1InviteId1,
    token: 'a10000f1',
    organization: org1Id,
    role: 'ADMIN',
    invitedBy: sharedOwnerId,
    status: 'pending',
    email: '<EMAIL>',
    // expiresAt: fromNow(0),
    expiresAt: fromNow(THREE_DAYS_FROM_NOW_IN_DAYS),
    createdAt: fromNow(0),
    updatedAt: fromNow(0),
    __v: 0,
  },
  {
    _id: org1InviteId2,
    token: 'a1a4b1f2',
    organization: org1Id,
    role: 'ADMIN',
    invitedBy: sharedOwnerId,
    status: 'pending',
    email: MY_USER_EMAIL,
    // expiresAt: fromNow(0),
    expiresAt: fromNow(-1),
    createdAt: fromNow(-4),
    updatedAt: fromNow(-1),
    __v: 0,
  },
];

// --- Insert Data into Database ---
db.organizations.insertMany(organizationsToInsert);
db.users.insertMany(usersToInsert);
db.members.insertMany(membersToInsert);
db.cvs.insertMany(cvsToInsert);
db.customers.insertMany(customersToInsert);
db.invites.insertMany(invitesToInsert);

print('✅ Test data created successfully!');
print(
  'Created 3 organizations, 9 users (including 2 shared), 6 members (2 for each org), 2 cvs (1 for each 1-st member), 4 customers (2 for each org), and 2 invites for org1.',
);
