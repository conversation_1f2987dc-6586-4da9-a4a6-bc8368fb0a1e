import { test, expect } from "@playwright/test";

test("login successful", async ({ page }) => {
  await page.goto("http://localhost:4200/");
  await page.getByPlaceholder("<EMAIL>").fill("<EMAIL>");
  await page.getByPlaceholder("Password").fill("password123");
  await page.getByRole("button", { name: "Log in" }).click();
  await expect(page.getByText("Logged in successfully")).toBeVisible();
  // await page.waitForTimeout(1000);
  await expect(page).toHaveScreenshot("login-successful.png");
});
