import { Injectable } from '@nestjs/common';
import * as fs from 'fs-extra';
import { join } from 'path';
import * as crypto from 'crypto';

export interface UserProfile {
  firstName: string;
  lastName: string;
  email: string;
  image: string;
  alias: string;
}

// const maxAttempts = 10;

@Injectable()
export class GeneratorService {
  private namesData: {
    male: string[];
    female: string[];
    lastNames: string[];
  } | null = null;

  private avatarsData: {
    male: string[];
    female: string[];
  } | null = null;

  constructor() {
    this.loadNamesData();
    this.loadAvatarsData();
  }

  private loadNamesData() {
    try {
      const dataFile = join(process.cwd(), 'src', 'data', 'names.json');
      this.namesData = fs.readJsonSync(dataFile);
    } catch (error) {
      console.error('Error loading names data:', error.message);
      this.namesData = { male: [], female: [], lastNames: [] };
    }
  }

  private loadAvatarsData() {
    try {
      const dataFile = join(process.cwd(), 'src', 'data', 'avatars.json');
      this.avatarsData = fs.readJsonSync(dataFile);
    } catch (error) {
      console.error('Error loading avatars data:', error.message);
      this.avatarsData = { male: [], female: [] };
    }
  }

  generateUserProfiles(count: number): UserProfile[] {
    if (!this.namesData || !this.avatarsData) {
      return [];
    }

    const profiles: UserProfile[] = [];
    const halfCount = Math.ceil(count / 2);

    // Create shuffled copies of the arrays to ensure randomness while maintaining uniqueness
    const shuffledMaleNames = [...this.namesData.male].sort(
      () => Math.random() - 0.5,
    );
    const shuffledFemaleNames = [...this.namesData.female].sort(
      () => Math.random() - 0.5,
    );
    const shuffledLastNames = [...this.namesData.lastNames].sort(
      () => Math.random() - 0.5,
    );
    const shuffledMaleAvatars = [...this.avatarsData.male].sort(
      () => Math.random() - 0.5,
    );
    const shuffledFemaleAvatars = [...this.avatarsData.female].sort(
      () => Math.random() - 0.5,
    );

    // Generate male profiles
    for (let i = 0; i < halfCount; i++) {
      const firstName = shuffledMaleNames[i % shuffledMaleNames.length];
      const lastName = shuffledLastNames[i % shuffledLastNames.length];
      const email = this.generateUniqueEmail(firstName, lastName);
      const alias = email.split('@')[0];
      const image = shuffledMaleAvatars[i % shuffledMaleAvatars.length];

      profiles.push({
        firstName,
        lastName,
        email,
        image: `https://app-muchskills.s3.eu-north-1.amazonaws.com/demo/male/${image}`,
        alias,
      });
    }

    // Generate female profiles
    for (let i = 0; i < count - halfCount; i++) {
      const firstName = shuffledFemaleNames[i % shuffledFemaleNames.length];
      const lastName = shuffledLastNames[i % shuffledLastNames.length];
      const email = this.generateUniqueEmail(firstName, lastName);
      const alias = email.split('@')[0];
      const image = shuffledFemaleAvatars[i % shuffledFemaleAvatars.length];

      profiles.push({
        firstName,
        lastName,
        email,
        image: `https://app-muchskills.s3.eu-north-1.amazonaws.com/demo/female/${image}`,
        alias,
      });
    }

    // Shuffle the profiles to mix male and female
    return profiles.sort(() => Math.random() - 0.5);
  }

  private generateUniqueEmail(firstName: string, lastName: string): string {
    const timestamp = Date.now();
    const randomBytes = crypto.randomBytes(4).toString('hex');
    const baseEmail = `${firstName.toLowerCase()}${lastName.toLowerCase()}`;
    const hash = crypto
      .createHash('sha256')
      .update(`${baseEmail}${timestamp}${randomBytes}`)
      .digest('hex')
      .substring(0, 8);

    return `${baseEmail}${hash}@example.com`;
  }
}
