import { format } from 'date-fns';
import { CalendarIcon, Ellipsis } from 'lucide-react';
import { useForm<PERSON>ontext, Controller } from 'react-hook-form';
import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css';

import {
  Input,
  Label,
  MonthPicker,
  Popover,
  PopoverTrigger,
  Button,
  PopoverContent,
  Checkbox,
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  ButtonSecondary,
} from '../../../../../common';

import {
  MemberProfileFormValues,
  WorkHistoryItemType,
} from '@/components/MemberProfileForm/types';
import { cn } from '@/lib/utils';

interface WorkHistoryItemProps {
  index: number;
  dropDownItems: { title: string; onClick: () => void }[];
  readonly?: boolean;
}

export function WorkHistoryItem({
  index,
  dropDownItems,
  readonly,
}: WorkHistoryItemProps) {
  const id = `${index}`;
  const {
    register,
    setValue,
    formState: { errors },
    control,
    watch,
  } = useFormContext<MemberProfileFormValues>();
  const data = watch(`workExperience.${index}`) as WorkHistoryItemType;
  const itemErrors = errors.workExperience?.[index];

  return (
    <div className="relative p-4 border rounded-sm">
      <div className="absolute flex space-x-2 right-1 top-1">
        <DropdownMenu modal={false}>
          <DropdownMenuTrigger asChild disabled={readonly}>
            <ButtonSecondary variant="icon">
              <Ellipsis size={14} />
            </ButtonSecondary>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            collisionPadding={10}
            align="end"
            className="w-32 prevent-drawer-outside-click"
          >
            <DropdownMenuGroup>
              {dropDownItems.map((item, i) => (
                <DropdownMenuItem key={i} onClick={item.onClick}>
                  <span className="text-smalldoge-3">{item.title}</span>
                </DropdownMenuItem>
              ))}
            </DropdownMenuGroup>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
      <div className="flex flex-col space-y-5">
        <div className="flex flex-col space-y-2">
          <Label
            mandatory={true}
            label="Company Name"
            labelProps={{ htmlFor: `companyName-${index}` }}
          />
          <Input
            id={`companyName-${index}`}
            type="text"
            {...register(`workExperience.${index}.companyName`)}
            disabled={readonly}
            error={itemErrors?.companyName?.message}
          />
        </div>
        <div className="flex flex-col space-y-2">
          <Label
            label="Role Title"
            labelProps={{ htmlFor: `roleTitle-${index}` }}
          />
          <Input
            id={`roleTitle-${index}`}
            type="text"
            {...register(`workExperience.${index}.roleTitle`)}
            disabled={readonly}
          />
        </div>
        <div className="flex flex-col space-y-2">
          <Label label="Description" />
          <Controller
            name={`workExperience.${index}.description`}
            control={control}
            render={({ field }) => (
              <ReactQuill
                modules={{
                  toolbar: [
                    ['bold', 'italic', 'underline'],
                    [{ list: 'ordered' }, { list: 'bullet' }],
                  ],
                }}
                theme="snow"
                value={field.value || ''}
                readOnly={readonly}
                onChange={(value, delta, source) => {
                  if (source === 'user') {
                    field.onChange(value);
                  }
                }}
              />
            )}
          />
        </div>
        <div className="flex space-x-5">
          <div className="flex flex-col w-1/2 space-y-2">
            <Label mandatory={true} label="Start" />
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant={'outline'}
                  disabled={readonly}
                  className={cn(
                    'justify-start text-left font-normal',
                    !data?.startDate && 'text-muted-foreground',
                  )}
                >
                  <CalendarIcon className="w-4 h-4 mr-2" />
                  {data?.startDate ? (
                    format(new Date(data.startDate), 'MMM yyyy')
                  ) : (
                    <span>Pick start month</span>
                  )}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0 prevent-drawer-outside-click">
                <MonthPicker
                  maxDate={data?.endDate}
                  selectedMonth={data?.startDate}
                  onMonthSelect={(date) =>
                    setValue(`workExperience.${index}.startDate`, date, {
                      shouldDirty: true,
                    })
                  }
                />
              </PopoverContent>
            </Popover>
          </div>
          <div className="flex flex-col w-1/2 space-y-2">
            <Label label="End" />
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant={'outline'}
                  disabled={readonly || data?.isCurrent}
                  className={cn(
                    'justify-start text-left font-normal',
                    !data?.endDate && 'text-muted-foreground',
                  )}
                >
                  <CalendarIcon className="w-4 h-4 mr-2" />
                  {data?.endDate ? (
                    format(new Date(data.endDate), 'MMM yyyy')
                  ) : (
                    <span>Pick end month</span>
                  )}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0 prevent-drawer-outside-click">
                <MonthPicker
                  minDate={data?.startDate}
                  selectedMonth={data?.endDate}
                  onMonthSelect={(date) =>
                    setValue(`workExperience.${index}.endDate`, date, {
                      shouldDirty: true,
                    })
                  }
                />
              </PopoverContent>
            </Popover>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Checkbox
            id={`isCurrent${id}`}
            checked={!!data?.isCurrent}
            disabled={readonly}
            onCheckedChange={(val) =>
              setValue(`workExperience.${index}.isCurrent`, !!val, {
                shouldDirty: true,
              })
            }
          />
          <label
            htmlFor={`isCurrent${id}`}
            className="font-bold text-smalldoge-4"
          >
            Currently working in this role
          </label>
        </div>
      </div>
    </div>
  );
}
