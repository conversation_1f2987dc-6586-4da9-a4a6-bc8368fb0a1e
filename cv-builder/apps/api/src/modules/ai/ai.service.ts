import { HttpService } from '@nestjs/axios';
import { BadRequestException, Injectable, Logger } from '@nestjs/common';
import { AxiosError } from 'axios';
import { firstValueFrom } from 'rxjs';
import { Message, MemberSkill } from 'shared/types';

import { CvDocument, CvSections } from '../cvs/cv.schema';
import { MemberDocument } from '../members/member.schema';

const AI_ENDPOINTS = {
  REGENERATE_CV: '/ai/regenerate-cv',
};

@Injectable()
export class AiService {
  private readonly logger = new Logger(AiService.name);

  constructor(private readonly httpService: HttpService) {}

  async regenerateCvSections(
    cv: CvDocument,
    member: MemberDocument,
    customerName: string | null,
    query: string,
    messages?: Message[],
    aiContext?: string,
  ): Promise<Partial<CvSections>> {
    try {
      const result = await firstValue<PERSON>rom(
        this.httpService.post(
          process.env.AI_API_URL + AI_ENDPOINTS.REGENERATE_CV,
          {
            cv,
            member,
            customerName: customerName,
            query,
            messages: messages || [],
            aiContext: aiContext,
            token: process.env.AI_API_TOKEN,
          },
        ),
      );

      const response = result?.data;

      // Check if there was an error from the AI service
      if (response?.error) {
        throw new BadRequestException(
          response.changesSummary || 'AI service error',
        );
      }

      return response;
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }

      if (error instanceof AxiosError && error.response?.status === 400) {
        this.logger.error(
          'AI service returned a bad request.',
          error.stack,
          error.message,
        );
        throw new BadRequestException('AI Service is not available');
      }

      if (error instanceof Error) {
        this.logger.error(
          'An error occurred in regenerateCvSections',
          error.message,
          error.stack,
        );
        throw new BadRequestException(error.message);
      }

      this.logger.error(
        'An unknown error occurred in regenerateCvSections',
        error,
      );
      throw new BadRequestException('An unknown error occurred');
    }
  }

  async generateSkillsSummary(skills: MemberSkill[]): Promise<string> {
    try {
      const result = await firstValueFrom(
        this.httpService.post(
          process.env.AI_API_URL + '/ai/generate-skills-summary',
          {
            skills,
            token: process.env.AI_API_TOKEN,
          },
        ),
      );

      const response = result?.data;

      // Check if there was an error from the AI service
      if (response?.error) {
        throw new BadRequestException('AI service error');
      }

      return response?.summary || '';
    } catch (error) {
      console.log('generateSkillsSummary error: ', error);
      // Return empty string instead of throwing to allow member creation to continue
      return '';
    }
  }
}
