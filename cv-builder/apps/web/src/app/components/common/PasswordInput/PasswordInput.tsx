import { Eye, EyeOff } from 'lucide-react';
import { useState } from 'react';

import { Input, type InputProps } from '../Input';

import { cn } from '@/lib/utils';

export interface PasswordInputProps extends Omit<InputProps, 'type'> {
  showToggle?: boolean;
}

export function PasswordInput({
  showToggle = true,
  className,
  wrapperClassName,
  disabled,
  ...props
}: PasswordInputProps) {
  const [isVisible, setIsVisible] = useState(false);

  const toggleVisibility = () => {
    setIsVisible(!isVisible);
  };

  return (
    <div className={cn('relative', wrapperClassName)}>
      <Input
        {...props}
        disabled={disabled}
        type={isVisible ? 'text' : 'password'}
        className={cn(showToggle && 'pr-10', className)}
        wrapperClassName="flex-1"
      />
      {showToggle && !disabled && (
        <button
          type="button"
          onClick={toggleVisibility}
          className="absolute right-3 top-1/2 -translate-y-1/2 text-msGray-2 hover:text-msBlack transition-colors"
          aria-label={isVisible ? 'Hide password' : 'Show password'}
        >
          {isVisible ? (
            <EyeOff className="h-4 w-4" />
          ) : (
            <Eye className="h-4 w-4" />
          )}
        </button>
      )}
    </div>
  );
}
