import { Combobox, ComboboxOption } from '../Combobox';

interface Customer {
  _id: string;
  name: string;
  createdAt: string;
}

interface CustomerComboboxProps {
  customers: Customer[];
  selectedCustomer?: string;
  onCustomerChange: (customerId: string | undefined) => void;
  onCreateCustomer?: (name: string) => Promise<void>;
  isCreatingCustomer?: boolean;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
}

export function CustomerCombobox({
  customers,
  selectedCustomer,
  onCustomerChange,
  onCreateCustomer,
  isCreatingCustomer = false,
  placeholder = 'Select client...',
  className,
  disabled = false,
}: CustomerComboboxProps) {
  // Convert customers to combobox options
  const options: ComboboxOption[] = customers.map((customer) => ({
    value: customer._id,
    label: customer.name,
  }));

  const handleCreate = async (name: string) => {
    if (onCreateCustomer) {
      await onCreateCustomer(name);
    }
  };

  return (
    <Combobox
      mode="single"
      options={options}
      selected={selectedCustomer || ''}
      onChange={(value) => onCustomerChange((value as string) || undefined)}
      onCreate={onCreateCustomer ? handleCreate : undefined}
      placeholder={placeholder}
      searchPlaceholder="Search clients or add new..."
      className={className}
      disabled={disabled || isCreatingCustomer}
      keepOpen={false}
    />
  );
}
