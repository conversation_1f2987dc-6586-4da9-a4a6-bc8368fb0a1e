import { ChevronDown, ChevronLeft, ChevronRight } from 'lucide-react';

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components';
import { useResponsive } from '@/hooks/useResponsive';
import { cn } from '@/lib/utils';

type ItemsPerPage = 12 | 24 | 48;
const itemsPerPageOptions: ItemsPerPage[] = [12, 24, 48];

interface PaginationProps {
  page: number;
  total?: number;
  itemsPerPage: number;
  minimalMode?: boolean;
  onItemsPerPageChange?: (val: number) => void;
  onPageChange: (page: number) => void;
}

export function Pagination({
  page,
  total = 0,
  itemsPerPage,
  minimalMode,
  onItemsPerPageChange,
  onPageChange,
}: PaginationProps) {
  const { isMobile } = useResponsive();
  const pagesNumber = Math.ceil(total / itemsPerPage) || 1;

  const prevPage = () => {
    const buttonDisabled = page - 1 < 1;

    return (
      <button
        className={cn(buttonDisabled && 'text-msGray-5')}
        disabled={buttonDisabled}
        onClick={() => onPageChange(page - 1)}
      >
        <ChevronLeft size={isMobile ? 16 : 24} />
      </button>
    );
  };

  const nextPage = () => {
    const buttonDisabled = page + 1 > pagesNumber;

    return (
      <button
        className={cn(buttonDisabled && 'text-msGray-5')}
        disabled={buttonDisabled}
        onClick={() => onPageChange(page + 1)}
      >
        <ChevronRight size={isMobile ? 16 : 24} />
      </button>
    );
  };

  return (
    <div className="flex items-center space-x-1">
      {!minimalMode && (
        <div className="flex space-x-1 text-msGray-3 text-smalldoge-4">
          <span>{isMobile ? 'Items/page' : 'Items per page'}</span>
          <DropdownMenu modal={false}>
            <DropdownMenuTrigger asChild>
              <span className="flex font-bold cursor-pointer">
                {itemsPerPage} <ChevronDown size={16} />
              </span>
            </DropdownMenuTrigger>
            <DropdownMenuContent
              collisionPadding={10}
              align="end"
              className="w-20 prevent-drawer-outside-click"
            >
              <DropdownMenuGroup>
                {itemsPerPageOptions.map((option) => (
                  <DropdownMenuItem
                    key={option}
                    onClick={() => onItemsPerPageChange?.(option)}
                  >
                    <span className="text-msGray-3 text-smalldoge-4">
                      {option}
                    </span>
                  </DropdownMenuItem>
                ))}
              </DropdownMenuGroup>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      )}
      <div className="flex items-center">
        {prevPage()}
        <span className="font-bold text-msGray-3 text-smalldoge-4">
          {isMobile
            ? `${page}/${pagesNumber}`
            : `Page ${page} of ${pagesNumber}`}
        </span>
        {nextPage()}
      </div>
      {!minimalMode && (
        <div className="flex items-center text-msGray-3 text-smalldoge-4">
          <span className="">{isMobile ? 'Results' : 'Total results'}</span>
          <span className="ml-2 font-bold">{total}</span>
        </div>
      )}
    </div>
  );
}
