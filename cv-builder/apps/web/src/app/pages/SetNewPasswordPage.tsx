import { zod<PERSON>esolver } from '@hookform/resolvers/zod';
import { useMutation } from '@tanstack/react-query';
import { AxiosError } from 'axios';
import { useForm } from 'react-hook-form';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { ResetPasswordDto } from 'shared/dto/auth/reset-password.dto';
import { z } from 'zod';

import { Button } from '@/components/common';
import { ButtonPrimary } from '@/components/common/ButtonPrimary';
import { Input } from '@/components/common/Input';
import { NAVIGATE_PATH } from '@/helpers/constants';
import { resetPasswordRequest } from '@/helpers/requests';

export function SetNewPasswordPage(): React.JSX.Element {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const token = searchParams.get('token');

  // Define the validation schema with Zod
  const resetPasswordSchema = z
    .object({
      password: z
        .string()
        .min(8, 'Password must be at least 8 characters long'),
      confirmPassword: z.string(),
    })
    .refine((data) => data.password === data.confirmPassword, {
      message: "Passwords don't match",
      path: ['confirmPassword'],
    });

  type ResetPasswordFormValues = z.infer<typeof resetPasswordSchema>;

  // Set up React Hook Form with Zod validation
  const {
    register,
    handleSubmit,
    formState: { errors },
    setError: setFormError,
  } = useForm<ResetPasswordFormValues>({
    resolver: zodResolver(resetPasswordSchema),
    defaultValues: {
      password: '',
      confirmPassword: '',
    },
  });

  // Set up React Query mutation
  const mutation = useMutation({
    mutationFn: (data: ResetPasswordDto) => resetPasswordRequest(data),
    onSuccess: () => {
      navigate(NAVIGATE_PATH.login, {
        state: {
          message:
            'Password has been reset successfully. Please login with your new password.',
        },
      });
    },
    onError: (error: AxiosError) => {
      console.error('Password reset failed:', error);
      setFormError('root', {
        message:
          (error?.response?.data as { message: string })?.message ||
          'Failed to reset password. Please try again.',
      });
    },
  });

  // Form submission handler
  const onSubmit = (data: ResetPasswordFormValues) => {
    if (!token) {
      setFormError('root', {
        message: 'Invalid or missing reset token',
      });
      return;
    }

    mutation.mutate({
      token,
      newPassword: data.password,
    });
  };

  if (!token) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen p-4 bg-msGray-6">
        <div className="w-full max-w-md p-10 mt-16 rounded-lg shadow-xl bg-card md:p-20 md:mt-0">
          <div className="text-center">
            <h1 className="mb-2 text-2xl font-black text-foreground">
              Invalid Reset Link
            </h1>
            <p className="text-sm text-muted-foreground">
              This password reset link is invalid or has expired.
            </p>
            <ButtonPrimary
              className="mt-4"
              onClick={() => navigate(NAVIGATE_PATH.passwordReset)}
            >
              Request New Reset Link
            </ButtonPrimary>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="relative flex flex-col items-center justify-center min-h-screen p-4 bg-msGray-6">
      <div className="absolute top-4 left-4 right-4 md:top-8 md:left-8 md:right-8 flex items-center justify-between">
        <img
          src="/images/logo-with-text.png"
          alt="CV Inventory"
          className="w-48"
        />
        <Button
          variant="default"
          size="sm"
          onClick={() => navigate(NAVIGATE_PATH.login)}
        >
          Back to Login
        </Button>
      </div>

      <div className="w-full max-w-md p-10 mt-16 rounded-lg shadow-xl bg-card md:p-20 md:mt-0">
        <div className="flex justify-center">
          <img width={40} src="/images/logo.png" alt="logo" />
        </div>

        <div className="mb-8 text-center">
          <h1 className="mb-2 text-2xl font-black text-foreground">
            Set New Password
          </h1>
          <p className="text-sm text-muted-foreground">
            Please enter your new password below.
          </p>
        </div>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          <div>
            <Input
              type="password"
              placeholder="New Password"
              className="w-full"
              {...register('password')}
              disabled={mutation.isPending}
            />
            {errors.password && (
              <p className="mt-1 text-sm text-destructive">
                {errors.password.message}
              </p>
            )}
          </div>
          <div>
            <Input
              type="password"
              placeholder="Confirm New Password"
              className="w-full"
              {...register('confirmPassword')}
              disabled={mutation.isPending}
            />
            {errors.confirmPassword && (
              <p className="mt-1 text-sm text-destructive">
                {errors.confirmPassword.message}
              </p>
            )}
          </div>
          {errors.root && (
            <p className="text-sm text-destructive">{errors.root.message}</p>
          )}
          <ButtonPrimary
            type="submit"
            className="w-full rounded-md"
            disabled={mutation.isPending}
          >
            {mutation.isPending ? 'Resetting Password...' : 'Reset Password'}
          </ButtonPrimary>
        </form>
      </div>
    </div>
  );
}
