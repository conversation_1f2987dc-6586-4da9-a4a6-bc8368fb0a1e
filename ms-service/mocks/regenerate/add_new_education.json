{"updatedSections": {"sections": {"education": {"id": "689f2013d50e9ed4127d5ed7", "title": "Education", "active": true, "order": 4, "data": [{"active": true, "schoolName": "<PERSON><PERSON><PERSON>yk Precarpathian National University", "degree": "Master of Science in Computer Science", "description": "Currently pursuing advanced studies in Computer Science.", "startDate": "2024-08-15T11:54:59.943Z", "endDate": null}, {"active": true, "schoolName": "<PERSON><PERSON><PERSON>yk Precarpathian National University", "degree": "Bachelor of Science in Computer Science", "description": "Completed undergraduate studies in Computer Science.", "startDate": "2020-08-16T11:54:59.943Z", "endDate": "2024-08-15T11:54:59.943Z"}, {"active": true, "schoolName": "SoftServe Academy", "degree": "Ruby Development Certificate", "description": "Series DU No 8388/2022 - Specialized training in Ruby development.", "startDate": "2023-03-19T12:54:59.943Z", "endDate": "2023-06-17T11:54:59.943Z"}, {"active": true, "schoolName": "DevOps Foundations (Coursera)", "degree": "Professional Certificate", "description": "<p><strong>Focus:</strong> CI/CD, containerization (Docker), IaC (Terraform), monitoring (Prometheus/Grafana), GitOps concepts—aligned with GitLab workflows.</p>", "startDate": "2025-01-01T00:00:00.000Z", "endDate": "2025-03-31T00:00:00.000Z"}]}}, "customSections": []}, "changesSummary": "• Added a new Education entry: DevOps Foundations (Coursera) — Professional Certificate, with an HTML-formatted description emphasizing CI/CD, Docker, IaC, monitoring, and GitOps to align with GitLab and a DevOps-focused organization. Would you like to keep these edits?"}