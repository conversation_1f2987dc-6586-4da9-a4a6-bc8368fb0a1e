#!/bin/bash

if [ -f .env ]; then
  export $(grep -v '^#' .env | xargs)
else
  exit 1
fi

docker network create cvnetwork 2>/dev/null || true

# Create Redis data directory if it doesn't exist
mkdir -p /data/redis

# Stop and remove existing Redis container if it exists
docker stop redis || true
docker rm redis || true

# Start Redis container with persistence
docker run -d --restart always \
  --name redis \
  --net cvnetwork \
  -v /data/redis:/data \
  -p 6379:6379 \
  redis:7-alpine \
  redis-server --appendonly yes --save 900 1 --save 300 10

# Wait for Redis to be ready
echo "Waiting for Redis to be ready..."
sleep 5

echo "$DOCKER_REGISTRY_TOKEN" | docker login -u "$DOCKER_REGISTRY_USER" --password-stdin "$CI_REGISTRY"
docker pull "$DOCKER_IMAGE_NAME"
docker stop "$CI_PROJECT_NAME" || true
docker rm "$CI_PROJECT_NAME" || true

docker run -d --restart always \
  --name "$CI_PROJECT_NAME" \
  --net cvnetwork \
  -p 3000:3000 \
  --env-file .env \
  -e REDIS_HOST=redis \
  -e REDIS_PORT=6379 \
  "$DOCKER_IMAGE_NAME"

docker ps | grep "$CI_PROJECT_NAME"

docker image prune -a --filter "until=24h" --force

# Send Slack notification about successful deployment
if [ -n "$SLACK_WEBHOOK_URL" ]; then
  echo "Sending Slack notification..."

  # Decode the commit message
  CI_COMMIT_MESSAGE_DECODED=$(echo "$CI_COMMIT_MESSAGE" | base64 --decode)

  # Get short commit SHA (first 8 characters)
  CI_COMMIT_SHORT_SHA=${CI_COMMIT_SHA:0:8}

  # Create JSON payload with proper escaping
  SLACK_PAYLOAD=$(cat <<EOF
{
  "text": "🚀 Deployment Successful!",
  "blocks": [
    {
      "type": "header",
      "text": {
        "type": "plain_text",
        "text": "🚀 Deployment Successful!"
      }
    },
    {
      "type": "section",
      "fields": [
        {
          "type": "mrkdwn",
          "text": "*Project:*\n${CI_PROJECT_NAME}"
        },
        {
          "type": "mrkdwn",
          "text": "*Environment:*\n${CI_COMMIT_REF_SLUG}"
        },
        {
          "type": "mrkdwn",
          "text": "*Commit:*\n\`${CI_COMMIT_SHORT_SHA}\`"
        },
        {
          "type": "mrkdwn",
          "text": "*Message:*\n${CI_COMMIT_MESSAGE_DECODED}"
        }
      ]
    },
    {
      "type": "actions",
      "elements": [
        {
          "type": "button",
          "text": {
            "type": "plain_text",
            "text": "🌐 View Application"
          },
          "url": "${VITE_WEB_BASE_URL}",
          "style": "primary"
        }
      ]
    },
    {
      "type": "context",
      "elements": [
        {
          "type": "mrkdwn",
          "text": "✅ Deployed at $(date '+%Y-%m-%d %H:%M:%S UTC')"
        }
      ]
    }
  ]
}
EOF
)

  # Send to Slack with error handling
  if curl -X POST -H 'Content-type: application/json' \
          --data "$SLACK_PAYLOAD" \
          --max-time 30 \
          --silent \
          --show-error \
          "$SLACK_WEBHOOK_URL"; then
    echo "✅ Slack notification sent successfully"
  else
    echo "⚠️  Failed to send Slack notification (deployment still successful)"
  fi
else
  echo "⚠️  SLACK_WEBHOOK_URL not configured, skipping notification"
fi
