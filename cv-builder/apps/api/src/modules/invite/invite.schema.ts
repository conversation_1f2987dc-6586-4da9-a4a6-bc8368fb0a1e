import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema, Types } from 'mongoose';
import { InviteStatus, UserRole } from 'shared/types';

export type InviteDocument = Invite & Document;

@Schema({ timestamps: true })
export class Invite {
  @Prop({ required: true, type: String, index: true, unique: true })
  token: string;

  @Prop({
    required: true,
    type: MongooseSchema.Types.ObjectId,
    ref: 'Organization',
  })
  organization: Types.ObjectId;

  @Prop({ type: MongooseSchema.Types.ObjectId, ref: 'User' })
  invitedBy: Types.ObjectId;

  @Prop({
    type: String,
    enum: Object.values(InviteStatus),
    default: InviteStatus.PENDING,
  })
  status: InviteStatus;

  @Prop({ type: String })
  email: string;

  @Prop({ type: Date })
  expiresAt: Date;

  @Prop({
    type: String,
    enum: Object.values(UserRole),
    default: UserRole.MEMBER,
  })
  role: UserRole;

  @Prop({ type: String, required: false })
  name?: string;
}

export const InviteSchema = SchemaFactory.createForClass(Invite);
