import {
  CanActivate,
  ExecutionContext,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { JwtService } from '@nestjs/jwt';
import { Request } from 'express';

import { AllowAnonymous } from '../../global/decorators/allow-anonymous.decorator';
import { UserDocument } from '../../users/user.schema';
import { UsersService } from '../../users/users.service';

@Injectable()
export class AuthGuard implements CanActivate {
  constructor(
    private jwtService: JwtService,
    private usersService: UsersService,
    private reflector: Reflector,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const handler = context.getHandler();
    const request = context.switchToHttp().getRequest();
    const token = this.extractTokenFromCookieOrHeader(request);
    const allowAnonymous = this.reflector.get(AllowAnonymous, handler);

    if (!token) {
      if (allowAnonymous) {
        return true;
      }
      throw new UnauthorizedException('No authorization token provided');
    }

    try {
      const payload = await this.jwtService.verifyAsync(token, {
        secret: process.env.JWT_SECRET_KEY,
      });

      const user = await this.usersService.findAuthUser({
        _id: payload.sub,
      });

      if (!user) {
        throw new UnauthorizedException('User not found');
      }

      const organizationId = user.organization;

      if (!organizationId) {
        throw new UnauthorizedException('No organization specified');
      }

      const organizationMembership = user.availableOrganizations.find(
        (org) => org.orgId.toString() === organizationId.toString(),
      );

      if (!organizationMembership && !allowAnonymous) {
        throw new UnauthorizedException(
          'User is not a member of this organization',
        );
      }
      const role = organizationMembership ? organizationMembership.role : null;

      request['user'] = {
        ...(user as UserDocument).toObject(),
        _id: user._id.toString(),
        organization: {
          _id: organizationId,
          role: role,
        },
        role: role,
      };
      return true;
    } catch {
      throw new UnauthorizedException('Unauthorized');
    }
  }

  private extractTokenFromCookieOrHeader(request: Request): string | undefined {
    // First try to get token from cookies
    const cookieToken = request.cookies?.['jwt-token'];
    if (cookieToken) {
      return cookieToken;
    }

    // Fallback to Authorization header for backward compatibility
    const [type, token] = request.headers.authorization?.split(' ') ?? [];
    return type === 'Bearer' ? token : undefined;
  }
}
