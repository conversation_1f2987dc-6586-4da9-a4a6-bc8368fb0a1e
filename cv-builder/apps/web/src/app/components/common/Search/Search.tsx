/**
 * @format
 */
import classNames from 'classnames';
import { Search as SearchIcon, X } from 'lucide-react';
import { useState, useEffect, useRef, useCallback } from 'react';
import { useDebounce } from 'use-debounce';

export interface SearchProps {
  /** Search input value */
  value: string;
  /** Placeholder to sho in input default: Search */
  placeholder?: string;
  /** show input on init or after click */
  startActive?: boolean;
  /** input background color className */
  bgColor?: string;
  /** Callback when search input value changes */
  onChange: (value: string) => void;
  /** Previous button disabled */
  previousButtonDisabled?: boolean;
  /** Next button disabled */
  nextButtonDisabled?: boolean;
  /** Buttons are available */
  buttons?: boolean;
  /** is close icon should be hidden */
  hideCloseIcon?: boolean;
  /** Previous button click Callback */
  previousClick?: () => void;
  /** Next button click Callback */
  nextClick?: () => void;
}
export function Search({
  value,
  placeholder,
  startActive,
  bgColor,
  onChange,
  previousButtonDisabled,
  nextButtonDisabled,
  buttons,
  hideCloseIcon,
  previousClick,
  nextClick,
}: SearchProps) {
  const [searchActive, setSearchActive] = useState<boolean>(
    startActive || false,
  );
  const [searchValue, setSearchValue] = useState<string>('');
  const [debouncedSearch] = useDebounce(searchValue, 500);
  const wrapper = useRef<HTMLDivElement>(null);

  const handleClickOutside = useCallback(
    (event: MouseEvent | TouchEvent) => {
      if (wrapper.current && wrapper.current.contains(event.target as Node)) {
        return;
      }
      // Only close if not startActive, or if there's no search value
      if (!startActive) {
        setSearchActive(false);
      }
    },
    [startActive],
  );

  useEffect(() => {
    document.addEventListener('mousedown', handleClickOutside, false);
    document.addEventListener('touchstart', handleClickOutside, false);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside, false);
      document.removeEventListener('touchstart', handleClickOutside, false);
    };
  }, [handleClickOutside]);

  useEffect(() => {
    onChange(debouncedSearch);
  }, [debouncedSearch, onChange]);
  useEffect(() => {
    setSearchValue(value);
  }, [value]);

  return (
    <div
      className={classNames(
        searchActive &&
          'md:w-auto fixed md:relative left-0 right-0 md:left-auto md:right-auto top-0 md:top-auto z-50 md:z-auto bg-white md:bg-transparent pt-4 md:pt-0 px-14 md:px-0',
      )}
      ref={wrapper}
    >
      {searchActive ? (
        <div
          className={classNames(
            'flex items-center w-full h-6 space-x-1 rounded md:max-w-72 focus:outline-none',
            'p-1',
            bgColor || 'bg-msGray-6',
          )}
        >
          <SearchIcon size={16} />
          <input
            autoFocus={startActive ? false : true}
            type="text"
            placeholder={placeholder || 'Search'}
            value={searchValue}
            onChange={(e) => {
              setSearchValue(e.target.value);
            }}
            className={classNames(
              'flex-grow placeholder-msGray-3 focus:outline-none truncate min-w-0',
              'text-smalldoge-3',
              bgColor || 'bg-transparent',
            )}
          />
          {buttons && (
            <div>
              <button
                disabled={!!previousButtonDisabled}
                onClick={previousClick}
                className={classNames(
                  'focus:outline-none',
                  searchValue && previousButtonDisabled && 'opacity-25',
                  !searchValue && 'opacity-0 pointer-events-none',
                )}
              >
                <SearchIcon size={16} />
              </button>
              <button
                disabled={!!nextButtonDisabled}
                onClick={nextClick}
                className={classNames(
                  'focus:outline-none',
                  searchValue && nextButtonDisabled && 'opacity-25',
                  !searchValue && 'opacity-0 pointer-events-none',
                )}
              >
                <X size={16} />
              </button>
            </div>
          )}
          {!hideCloseIcon && (
            <>
              {/* Clear search button (desktop) */}
              <button
                onClick={() => {
                  setSearchValue('');
                }}
                className={classNames(
                  'focus:outline-none hidden md:block',
                  !searchValue && 'opacity-0 pointer-events-none',
                )}
              >
                <X size={16} />
              </button>
              {/* Combined clear/close button (mobile) */}
              <button
                onClick={() => {
                  if (searchValue) {
                    setSearchValue('');
                  } else {
                    setSearchActive(false);
                  }
                }}
                className="focus:outline-none md:hidden"
              >
                <X size={16} />
              </button>
            </>
          )}
        </div>
      ) : (
        <button
          onClick={() => {
            setSearchActive(true);
          }}
          className="flex items-center w-full h-6 min-w-0 pl-1 space-x-1 focus:outline-none"
        >
          <SearchIcon size={16} className="flex-shrink-0" />
          <span
            className={classNames(
              'truncate text-msGray-3 text-smalldoge-3 md:inline min-w-0 flex-1',
              searchValue ? 'text-smalldoge-3' : 'hidden',
            )}
            {...(searchValue && {
              title: searchValue,
            })}
          >
            {searchValue || placeholder || 'Search'}
          </span>
        </button>
      )}
    </div>
  );
}
