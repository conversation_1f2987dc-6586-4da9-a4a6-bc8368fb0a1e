import {
  Body,
  Controller,
  HttpCode,
  HttpStatus,
  Post,
  Get,
  Param,
  Res,
  // Session,
  UseGuards,
  Query,
  Headers,
} from '@nestjs/common';
import { Response } from 'express';
import { JWT_EXPIRATION_30_DAYS } from 'shared/constants';
import { ResetPasswordDto } from 'shared/dto/auth/reset-password.dto';
import { SignInDto } from 'shared/dto/auth/sign-in.dto';
import { SignUpDto } from 'shared/dto/auth/sign-up.dto';
import { UpdatePasswordDto } from 'shared/dto/auth/update-password.dto';

import { AuthService } from './auth.service';
import { AuthGuard } from './guards/auth.guard';
import { Action } from '../casl/casl.types';
import { CheckAbilities } from '../casl/decorators/check-policies.decorator';
import { PoliciesGuard } from '../casl/guards/policies.guard';
import { AuthUser } from '../global/decorators/user.decorator';
import { AuthUserDto } from '../global/dto/auth-user.dto';
import { Organization } from '../organization/organization.schema';

@Controller()
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  @HttpCode(HttpStatus.OK)
  @Post('sign-up')
  async signUp(
    @Body() signUpDto: SignUpDto,
    @Query('token') token: string,
    @Res({ passthrough: true }) res: Response,
  ) {
    const result = await this.authService.signUp(signUpDto, token);
    if (result.accessToken) {
      res.cookie('jwt-token', result.accessToken, {
        httpOnly: true,
        secure: process.env.NODE_ENV !== 'development',
        sameSite: 'strict',
        maxAge: JWT_EXPIRATION_30_DAYS,
      });
    }
    return result;
  }

  @Get('activate/:token')
  async activate(@Param('token') token: string, @Res() res: Response) {
    const {
      accessToken,
      // user,
      // organization
    } = await this.authService.activateUser(token);

    res.cookie('jwt-token', accessToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV !== 'development',
      sameSite: 'strict',
      maxAge: JWT_EXPIRATION_30_DAYS,
    });

    // Redirect to home page with success message
    return res.redirect(
      `${process.env.VITE_WEB_BASE_URL}?message=🎉 Email confirmed! Welcome aboard.`,
    );
  }

  @HttpCode(HttpStatus.OK)
  @Post('login')
  async signIn(
    @Body() dto: SignInDto,
    @Res({ passthrough: true }) res: Response,
    @Query('token') token: string,
  ) {
    const { accessToken } = await this.authService.signIn(dto, token);

    res.cookie('jwt-token', accessToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV !== 'development',
      sameSite: 'strict',
      maxAge: JWT_EXPIRATION_30_DAYS,
    });

    return { message: 'Logged in successfully' };
  }

  @UseGuards(AuthGuard)
  @Get('me')
  async getCurrentUser(@AuthUser() user: AuthUserDto) {
    return this.authService.getCurrentUser(user);
  }

  @Post('muchskills/sign-in')
  signInWithMuchSkills(
    @Body() dto: SignInDto,
    // @Session() session: Record<string, any>,
  ) {
    // Not a priority for now
    console.log('signInWithMuchSkills', dto);
    // const result = await this.authService.signInWithMuchSkills(dto, session);
    return {
      status: 'ok',
      message: 'signInWithMuchSkills',
    };
  }

  @Post('sign-out')
  logout(@Res({ passthrough: true }) res: Response) {
    res.clearCookie('jwt-token', {
      httpOnly: true,
      secure: process.env.NODE_ENV !== 'development',
      sameSite: 'strict',
    });

    res.status(HttpStatus.OK).send({ message: 'Logged out successfully' });
  }

  @UseGuards(AuthGuard, PoliciesGuard)
  @CheckAbilities({ action: Action.Update, subject: Organization })
  @Post('muchskills/connect/:orgId')
  async connectWithMuchSkills(
    @AuthUser() user: AuthUserDto,
    @Param('orgId') orgId: string,
    @Headers('ms-token') token: string,
  ) {
    const result = await this.authService.connectWithMuchSkills(
      user,
      orgId,
      token,
    );
    return result;
  }

  @UseGuards(AuthGuard, PoliciesGuard)
  @CheckAbilities({ action: Action.Update, subject: Organization })
  @Post('muchskills/disconnect/:orgId')
  async disconnectMuchSkills(
    @AuthUser() user: AuthUserDto,
    @Param('orgId') orgId: string,
  ) {
    await this.authService.disconnectMuchSkills(user, orgId);
  }

  @HttpCode(HttpStatus.OK)
  @Post('reset-password-request')
  async requestPasswordReset(@Body() { email }: { email: string }) {
    return this.authService.requestPasswordReset(email);
  }

  @HttpCode(HttpStatus.OK)
  @Post('reset-password')
  async resetPassword(@Body() resetPasswordDto: ResetPasswordDto) {
    return this.authService.resetPassword(resetPasswordDto);
  }

  @UseGuards(AuthGuard)
  @HttpCode(HttpStatus.OK)
  @Post('update-password')
  async updatePassword(
    @AuthUser() user: AuthUserDto,
    @Body() updatePasswordDto: UpdatePasswordDto,
  ) {
    return this.authService.updatePassword(user, updatePasswordDto);
  }
}
