{"$schema": "./node_modules/nx/schemas/nx-schema.json", "namedInputs": {"default": ["{projectRoot}/**/*", "sharedGlobals"], "production": ["default", "!{projectRoot}/**/?(*.)+(spec|test).[jt]s?(x)?(.snap)", "!{projectRoot}/tsconfig.spec.json", "!{projectRoot}/src/test-setup.[jt]s", "!{projectRoot}/.eslintrc.json", "!{projectRoot}/eslint.config.cjs", "!{projectRoot}/jest.config.[jt]s", "!{projectRoot}/test-setup.[jt]s"], "sharedGlobals": ["{workspaceRoot}/.gitlab-ci.yml"]}, "plugins": [{"plugin": "@nx/vite/plugin", "options": {"buildTargetName": "build", "testTargetName": "test", "serveTargetName": "serve", "previewTargetName": "preview", "serveStaticTargetName": "serve-static", "typecheckTargetName": "typecheck"}}, {"plugin": "@nx/eslint/plugin", "options": {"targetName": "lint"}}, {"plugin": "@nx/jest/plugin", "options": {"targetName": "test"}, "exclude": ["apps/api-e2e/**/*"]}, {"plugin": "@nx/webpack/plugin", "options": {"buildTargetName": "build", "serveTargetName": "serve", "previewTargetName": "preview"}}], "generators": {"@nx/react": {"application": {"babel": true, "style": "tailwind", "linter": "eslint", "bundler": "vite"}, "component": {"style": "tailwind"}, "library": {"style": "tailwind", "linter": "eslint"}}}, "cli": {"packageManager": "yarn"}}