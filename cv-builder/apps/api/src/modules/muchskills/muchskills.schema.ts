/**
 * CV Profile Type Definitions
 */

export interface Experience {
  name: string;
  role?: string;
  description?: string;
  startTime: string; //date string
  endTime?: string | null; //date string
}

export interface Education {
  name: string;
  degree?: string;
  description?: string;
  startTime: string; //date string
  endTime?: string | null; //date string
}

export interface SkillType {
  type: string;
  skills: Skill[];
}
export interface Skill {
  id: string;
  name: string;
  level: number;
  isValidated?: boolean;
  levelString?: string;
  added?: string; //date string
  validatedLevel?: number;
  validatedLevelString?: string;
  validatedBy?: string;
  validated?: string; //date string
  icon?: string;
  image?: string;
}

export interface Certification {
  id: string;
  name: string;
  issuingOrganization: string;
  notExpiring?: boolean;
  issuedAt?: string; //date string
  expiredAt?: string; //date string
  added?: string; //date string
  license?: string;
  url?: string;
}

export interface Profile {
  email: string;
  alias: string;
  name: string;
  image: string;
  department: string;
  location: string;
  title: string;
  directManager: string;
  experience?: Experience[];
  education?: Education[];
  tags?: string[];
  links?: string[];
}

export interface CVProfile {
  profile: Profile;
  skillTypes: SkillType[];
  certifications: Certification[];
}
