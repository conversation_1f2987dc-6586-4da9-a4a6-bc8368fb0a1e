import {
  Body,
  Controller,
  Get,
  Post,
  UnauthorizedException,
} from '@nestjs/common';
import { lookup } from 'geoip-lite';

import { AppService } from './app.service';

@Controller()
export class AppController {
  constructor(private readonly appService: AppService) {}

  @Get()
  getHello(): string {
    return this.appService.getHello();
  }

  @Post('location')
  location(@Body() body) {
    if (body.token !== process.env.API_TOKEN) {
      throw new UnauthorizedException();
    }
    if (!body.ip) {
      throw new Error('no ip param');
    }

    try {
      const data = lookup(body.ip);
      return { data, error: false };
    } catch (error) {
      console.log('error ', error);
      return { data: null, error: true };
    }
  }
}
