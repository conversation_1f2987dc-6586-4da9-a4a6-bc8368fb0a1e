import { ApiStripeProduct, Plan, StripePlanMapping } from 'shared/types';

import {
  SettingsBadge,
  TypographyMuted,
  TypographyPageTitle,
  TypographyLabel,
} from '../Settings';

import { ButtonPrimary, ButtonSecondary, Separator } from '@/components/common';
import { useProcessedPlans } from '@/hooks/useProcessedPlans';

function openCalendlyPopup() {
  window.open(
    'https://calendly.com/muchskillswow/muchskills-demo-book',
    '_blank',
  );
}

// PlanCard component is removed and its logic will be inlined.

interface PlansListProps {
  stripeProducts: ApiStripeProduct[] | undefined;
  isLoadingProducts: boolean;
  productsError: Error | null;
  onUpgrade: (plan: Plan) => Promise<void>;
  organizationPlanTier: StripePlanMapping;
  billingInterval: 'month' | 'year';
  selectedCurrency: 'usd' | 'eur';
}

export function PlansList({
  stripeProducts,
  isLoadingProducts,
  productsError,
  onUpgrade,
  organizationPlanTier,
  billingInterval,
  selectedCurrency,
}: PlansListProps) {
  const { processedPlans: dynamicPlans } = useProcessedPlans({
    stripeProducts,
    billingInterval,
    selectedCurrency,
  });

  if (isLoadingProducts) {
    return <TypographyMuted>Loading plans...</TypographyMuted>;
  }

  if (productsError) {
    return (
      <TypographyMuted className="text-red-500">
        Error loading plans: {productsError.message}
      </TypographyMuted>
    );
  }

  return (
    <div className="flex flex-col">
      {dynamicPlans.length > 0 ? (
        dynamicPlans.map((plan: Plan, index: number) => {
          const isLast = index === dynamicPlans.length - 1;
          const isCurrentPlan = plan.tier === organizationPlanTier;
          const upgradablePlans = [
            StripePlanMapping.PRO,
            StripePlanMapping.BUSINESS,
          ];

          return (
            <div key={plan.priceId || plan.name}>
              <div
                className={`flex flex-col md:flex-row justify-between items-start gap-4`}
              >
                <div className="flex flex-col flex-1 gap-1">
                  <div className="flex items-center gap-2">
                    <TypographyPageTitle>{plan.name}</TypographyPageTitle>
                    {isCurrentPlan && (
                      <SettingsBadge>Current Plan</SettingsBadge>
                    )}
                  </div>
                  <TypographyLabel>{plan.price}</TypographyLabel>
                  <TypographyMuted>{plan.description}</TypographyMuted>
                  {!(plan.tier === StripePlanMapping.FREE) &&
                    !isCurrentPlan &&
                    (upgradablePlans.includes(
                      plan.tier as StripePlanMapping,
                    ) ? (
                      <ButtonPrimary
                        variant="blackCompact"
                        onClick={() => onUpgrade(plan)}
                      >
                        Upgrade
                      </ButtonPrimary>
                    ) : (
                      <ButtonSecondary
                        variant="white"
                        text="normal"
                        onClick={openCalendlyPopup}
                      >
                        Contact Sales
                      </ButtonSecondary>
                    ))}
                </div>
                <div className="flex flex-col flex-1 gap-2 md:max-w-1/2">
                  {plan.features.map((feature, featureIndex) => (
                    <div
                      key={featureIndex}
                      className="flex items-center justify-between"
                    >
                      <TypographyMuted>{feature.name}</TypographyMuted>
                      <TypographyLabel>{feature.value}</TypographyLabel>
                    </div>
                  ))}
                </div>
              </div>
              {!isLast && <Separator className="my-6" />}
            </div>
          );
        })
      ) : (
        <TypographyMuted>
          No plans available at the moment. Please check back later.
        </TypographyMuted>
      )}
    </div>
  );
}
