import { z } from 'zod';

import { UserRole } from '../types';

export const createInviteSchema = z.object({
  email: z.string().email('Please enter a valid email address'),
  name: z
    .string()
    .min(2, 'Name must be at least 2 characters')
    .max(100, 'Name must be no more than 100 characters')
    .regex(
      /^[\p{L}\s'-]+$/u,
      'Name can only contain letters, spaces, apostrophes, and dashes',
    )
    .optional()
    .or(z.literal('')),
  role: z.nativeEnum(UserRole),
});

export type CreateInviteInput = z.infer<typeof createInviteSchema>;
