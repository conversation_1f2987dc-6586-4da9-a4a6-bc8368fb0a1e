import axios from 'axios';

import { NAVIGATE_PATH } from '@/helpers/constants';

const api = axios.create({
  baseURL: `${import.meta.env.VITE_API_BASE_URL}`,
  timeout: 5000,
});

api.interceptors.request.use((config) => {
  config.withCredentials = true;
  return config;
});

api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (
      error.response?.status === 401 &&
      error.response?.data?.message === 'No authorization token provided'
    ) {
      const isGetCurrentUserRequest = error.config?.url?.includes('/me');

      if (!isGetCurrentUserRequest) {
        window.location.href = NAVIGATE_PATH.login + '?sessionExpired=true';
      }
    }

    return Promise.reject(error);
  },
);

export default api;
