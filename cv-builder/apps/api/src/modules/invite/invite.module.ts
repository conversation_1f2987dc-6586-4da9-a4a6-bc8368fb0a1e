import { forwardRef, Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

import { InviteController } from './invite.controller';
import { Invite, InviteSchema } from './invite.schema';
import { InviteService } from './invite.service';
import { MailService } from '../mail/mail.service';
import { OrganizationModule } from '../organization/organization.module';

@Module({
  imports: [
    forwardRef(() => OrganizationModule),
    MongooseModule.forFeature([{ name: Invite.name, schema: InviteSchema }]),
  ],
  providers: [InviteService, MailService],
  exports: [InviteService],
  controllers: [InviteController],
})
export class InviteModule {}
