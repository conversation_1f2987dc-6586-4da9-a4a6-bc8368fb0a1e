import { Modu<PERSON> } from '@nestjs/common';

import { AppController } from './app.controller';
import { AppService } from './app.service';
import { ParserModule } from './modules/parser/parser.module';
import { AiModule } from './modules/ai/ai.module';
import { GeneratorModule } from './modules/generator/generator.module';

@Module({
  imports: [ParserModule, AiModule, GeneratorModule],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
