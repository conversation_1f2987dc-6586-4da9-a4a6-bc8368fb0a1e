import { format } from 'date-fns';
import { uniqueId } from 'lodash';
import { CalendarIcon, Ellipsis } from 'lucide-react';
import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css';
import { WorkHistoryDataItem } from 'shared/types';

import {
  Input,
  Label,
  MonthPicker,
  Popover,
  PopoverTrigger,
  Button,
  PopoverContent,
  Checkbox,
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  ButtonSecondary,
} from '../../../../../common';

import { cn } from '@/lib/utils';

interface WorkHistoryItemProps {
  data: WorkHistoryDataItem;
  dropDownItems: { title: string; onClick: () => void }[];
  readonly?: boolean;
  onChange: (updatedHistoryRecord: WorkHistoryDataItem) => void;
}

export function WorkHistoryItem({
  data,
  dropDownItems,
  readonly,
  onChange,
}: WorkHistoryItemProps) {
  const id = uniqueId();

  return (
    <div className="relative p-4 border rounded-sm">
      <div className="absolute flex space-x-2 right-1 top-1">
        <Checkbox
          checked={data.active}
          disabled={readonly}
          onCheckedChange={(val) => onChange({ ...data, active: !!val })}
        />

        <DropdownMenu modal={false}>
          <DropdownMenuTrigger asChild disabled={readonly}>
            <ButtonSecondary variant="icon">
              <Ellipsis size={14} />
            </ButtonSecondary>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            collisionPadding={10}
            align="end"
            className="w-32 prevent-drawer-outside-click"
          >
            <DropdownMenuGroup>
              {dropDownItems.map((item, i) => (
                <DropdownMenuItem key={i} onClick={item.onClick}>
                  <span className="text-smalldoge-3">{item.title}</span>
                </DropdownMenuItem>
              ))}
            </DropdownMenuGroup>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
      <div className="flex flex-col space-y-5">
        <div className="flex flex-col space-y-2">
          <Label label="Company Name" labelProps={{ htmlFor: 'companyName' }} />
          <Input
            id="companyName"
            type="text"
            value={data.companyName}
            disabled={readonly}
            onChange={(e) => onChange({ ...data, companyName: e.target.value })}
          />
        </div>
        <div className="flex flex-col space-y-2">
          <Label label="Role Title" labelProps={{ htmlFor: 'roleTitle' }} />
          <Input
            id="roleTitle"
            type="text"
            value={data.roleTitle}
            disabled={readonly}
            onChange={(e) => onChange({ ...data, roleTitle: e.target.value })}
          />
        </div>
        <div className="flex flex-col space-y-2">
          <Label label="Description" />
          <ReactQuill
            modules={{
              toolbar: [
                ['bold', 'italic', 'underline'],
                [{ list: 'ordered' }, { list: 'bullet' }],
              ],
            }}
            theme="snow"
            value={data.description}
            readOnly={readonly}
            onChange={(value, delta, source) => {
              if (source === 'user') {
                onChange({ ...data, description: value });
              }
            }}
          />
        </div>
        <div className="flex space-x-5">
          <div className="flex flex-col w-1/2 space-y-2">
            <Label label="Start" />
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant={'outline'}
                  disabled={readonly}
                  className={cn(
                    'justify-start text-left font-normal',
                    !data.startDate && 'text-muted-foreground',
                  )}
                >
                  <CalendarIcon className="w-4 h-4 mr-2" />
                  {data.startDate ? (
                    format(data.startDate, 'MMM yyyy')
                  ) : (
                    <span>Pick start month</span>
                  )}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0 prevent-drawer-outside-click">
                <MonthPicker
                  maxDate={data.endDate}
                  selectedMonth={data.startDate}
                  onMonthSelect={(date) => {
                    onChange({ ...data, startDate: date });
                  }}
                />
              </PopoverContent>
            </Popover>
          </div>
          <div className="flex flex-col w-1/2 space-y-2">
            <Label label="End" />
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant={'outline'}
                  disabled={readonly || data.isCurrent}
                  className={cn(
                    'justify-start text-left font-normal',
                    !data.endDate && 'text-muted-foreground',
                  )}
                >
                  <CalendarIcon className="w-4 h-4 mr-2" />
                  {data.endDate ? (
                    format(data.endDate, 'MMM yyyy')
                  ) : (
                    <span>Pick end month</span>
                  )}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0 prevent-drawer-outside-click">
                <MonthPicker
                  minDate={data.startDate}
                  selectedMonth={data.endDate}
                  onMonthSelect={(date) => onChange({ ...data, endDate: date })}
                />
              </PopoverContent>
            </Popover>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Checkbox
            id={`isCurrent${id}`}
            checked={data.isCurrent}
            disabled={readonly}
            onCheckedChange={(val) => onChange({ ...data, isCurrent: !!val })}
          />
          <label
            htmlFor={`isCurrent${id}`}
            className="font-bold text-smalldoge-4"
          >
            Currently working in this role
          </label>
        </div>
      </div>
    </div>
  );
}
