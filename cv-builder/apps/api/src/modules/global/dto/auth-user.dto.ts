import { Types } from 'mongoose';
import { UserRole } from 'shared/types';

export class AuthUserDto {
  readonly _id: Types.ObjectId;
  readonly email: string;
  readonly firstName: string;
  readonly lastName: string;
  readonly status: number;
  readonly role: UserRole;
  readonly organization: {
    readonly _id: Types.ObjectId;
    readonly name: string;
    readonly photo?: string;
    readonly planTier?: string;
    readonly planStatus?: string;
    readonly muchskillsIntegration?: {
      readonly token: string;
      readonly connected: boolean;
      readonly lastSync?: Date;
    };
  };
}
