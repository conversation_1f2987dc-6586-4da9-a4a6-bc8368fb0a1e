import { useEffect } from 'react';
import { Outlet, useLocation, useNavigate } from 'react-router-dom';

import { useAuth } from '@/contexts/AuthContext';
import { NAVIGATE_PATH } from '@/helpers/constants';

const OrganizationRequiredRoute = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { user, needsOrganizationCreation, loading } = useAuth();

  useEffect(() => {
    // If user is not authenticated, redirect to login
    if (!loading && !user) {
      navigate(NAVIGATE_PATH.login, { replace: true });
      return;
    }

    // If user needs to create an organization, redirect to organization creation page
    if (
      !loading &&
      user &&
      needsOrganizationCreation &&
      location.pathname !== NAVIGATE_PATH.createOrganization
    ) {
      navigate(NAVIGATE_PATH.createOrganization, { replace: true });
    }
  }, [navigate, user, loading, needsOrganizationCreation, location.pathname]);

  // Don't render anything while loading
  if (loading) {
    return null;
  }

  return <Outlet />;
};

export default OrganizationRequiredRoute;
