import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import mongoose, { Document, Types } from 'mongoose';
import { Cur<PERSON>cy, CvStatus, Template, TimeRange } from 'shared/types';

import { Customer } from '../customers/customer.schema';
import { Member } from '../members/member.schema';
import { Organization } from '../organization/organization.schema';

@Schema()
export class HidableInput {
  @Prop({ type: String })
  value?: string;

  @Prop({ type: Boolean })
  active?: boolean;
}

@Schema()
export class SectionBase {
  @Prop({ type: Number, required: true })
  order: number;

  @Prop({ type: String, required: true })
  title: string;

  @Prop({ type: Boolean })
  active?: boolean;
}

@Schema()
export class PersonalInfoData {
  @Prop({ type: HidableInput })
  firstName?: HidableInput;

  @Prop({ type: HidableInput })
  lastName?: HidableInput;

  @Prop({ type: HidableInput })
  jobTitle?: HidableInput;

  @Prop({ type: HidableInput })
  location?: HidableInput;

  @Prop({ type: HidableInput })
  nationality?: HidableInput;

  @Prop({ type: HidableInput })
  email?: HidableInput;

  @Prop({ type: HidableInput })
  telephone?: HidableInput;

  @Prop({
    type: {
      inputs: [String],
      active: Boolean,
    },
  })
  socials?: { inputs: string[]; active: boolean };
}

@Schema()
export class AboutMeData {
  @Prop({ type: String })
  description?: string;
}

@Schema()
export class WorkHistoryDataItem {
  @Prop({ type: Boolean })
  active?: boolean;

  @Prop({ type: String })
  companyName?: string;

  @Prop({ type: String })
  roleTitle?: string;

  @Prop({ type: String })
  description?: string;

  @Prop({ type: Date })
  startDate?: Date;

  @Prop({ type: Date })
  endDate?: Date;

  @Prop({ type: Boolean })
  isCurrent?: boolean;
}

@Schema()
export class EducationDataItem {
  @Prop({ type: Boolean })
  active?: boolean;

  @Prop({ type: String })
  schoolName?: string;

  @Prop({ type: String })
  degree?: string;

  @Prop({ type: String })
  description?: string;

  @Prop({ type: Date })
  startDate?: Date;

  @Prop({ type: Date })
  endDate?: Date;
}

@Schema()
export class CertificationsData {
  @Prop({ type: String })
  description?: string;
}

@Schema()
export class SkillsData {
  @Prop({ type: String })
  description?: string;
}

@Schema()
export class LanguagesData {
  @Prop({ type: [String] })
  languages?: string[];
}

@Schema()
export class CustomDataItem {
  @Prop({ type: String })
  description?: string;
}

@Schema()
export class PersonalInfo extends SectionBase {
  @Prop({ type: PersonalInfoData })
  data?: PersonalInfoData;
}

@Schema()
export class AboutMe extends SectionBase {
  @Prop({ type: AboutMeData })
  data?: AboutMeData;
}

@Schema()
export class WorkHistory extends SectionBase {
  @Prop({ type: [WorkHistoryDataItem] })
  data?: WorkHistoryDataItem[];
}

@Schema()
export class Education extends SectionBase {
  @Prop({ type: [EducationDataItem] })
  data?: EducationDataItem[];
}

@Schema()
export class Certifications extends SectionBase {
  @Prop({ type: CertificationsData })
  data?: CertificationsData;
}

@Schema()
export class Skills extends SectionBase {
  @Prop({ type: SkillsData })
  data?: SkillsData;
}

@Schema()
export class Languages extends SectionBase {
  @Prop({ type: LanguagesData })
  data?: LanguagesData;
}

@Schema()
export class CustomSection extends SectionBase {
  @Prop({ type: CustomDataItem })
  data?: CustomDataItem;
}

@Schema()
export class CvSections {
  @Prop({ type: PersonalInfo })
  personalInfo?: PersonalInfo;

  @Prop({ type: AboutMe })
  aboutMe?: AboutMe;

  @Prop({ type: WorkHistory })
  workHistory?: WorkHistory;

  @Prop({ type: Education })
  education?: Education;

  @Prop({ type: Certifications })
  certifications?: Certifications;

  @Prop({ type: Skills })
  skills?: Skills;

  @Prop({ type: Languages })
  languages?: Languages;

  @Prop({ type: [CustomSection] })
  customSections?: CustomSection[];
}

@Schema()
export class CostRate {
  @Prop({ type: String, enum: Currency, required: true })
  currency: Currency;

  @Prop({ type: Number, required: true })
  amount: number;

  @Prop({ type: String, enum: TimeRange, required: true })
  timeRange: TimeRange;
}

@Schema()
export class CvPreferences {
  @Prop({
    type: String,
    required: true,
  })
  title: string;

  @Prop({ type: Number })
  maxPages?: number;

  @Prop({ type: CostRate })
  costRate?: CostRate;

  @Prop({
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Customer',
  })
  customer?: Types.ObjectId | Customer;

  @Prop({ type: String })
  link?: string;

  @Prop({ type: Date })
  contractStart?: Date;

  @Prop({ type: Date })
  contractEnd?: Date;

  @Prop({ type: Boolean })
  autoRenewal?: boolean;

  @Prop({ type: Number })
  leastExperience?: number;

  @Prop({ type: Number })
  maxExperience: number;

  //TODO: add these fields
  // skills: { type: [{ type: String }] },
  // role: { type: String },
  // level: { type: String },

  @Prop({ type: String })
  description?: string;
}

@Schema({ timestamps: true })
export class Cv {
  _id: Types.ObjectId;

  @Prop({
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Member',
    required: true,
  })
  member: Types.ObjectId | Member;

  @Prop({
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Organization',
    required: true,
  })
  organization: Types.ObjectId | Organization;

  @Prop({
    type: String,
    enum: Template,
    required: true,
  })
  template: Template;

  @Prop({
    type: String,
    enum: CvStatus,
    required: true,
  })
  status: CvStatus;

  @Prop({
    type: CvPreferences,
    required: true,
  })
  preferences: CvPreferences;

  @Prop({
    type: CvSections,
    required: true,
  })
  sections: CvSections[];
}
export type CvDocument = Cv & Document;
export const CvSchema = SchemaFactory.createForClass(Cv);

CvSchema.index({ member: 1, 'preferences.title': 1 }, { unique: true });
