import { Module } from '@nestjs/common';
import { ScheduleModule } from '@nestjs/schedule';

import { CronService } from './cron.service';
import { OrganizationModule } from '../organization/organization.module';
import { StripeModule } from '../stripe/stripe.module';

@Module({
  imports: [ScheduleModule.forRoot(), OrganizationModule, StripeModule],
  providers: [CronService],
  exports: [CronService],
})
export class CronModule {}
