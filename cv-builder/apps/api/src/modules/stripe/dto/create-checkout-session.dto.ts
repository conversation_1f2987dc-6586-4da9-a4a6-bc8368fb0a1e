import {
  IsNotEmpty,
  IsOptional,
  IsString,
  IsUrl,
  ValidateIf,
  IsIn,
} from 'class-validator';

export class CreateCheckoutSessionDto {
  @IsNotEmpty()
  @IsString()
  priceId: string; // Stripe Price ID (e.g., price_xxxx)

  @IsOptional()
  @ValidateIf((o) => o.successUrl !== '')
  @IsUrl()
  successUrl?: string;

  @IsOptional()
  @ValidateIf((o) => o.cancelUrl !== '')
  @IsUrl()
  cancelUrl?: string;

  @IsOptional()
  @IsString()
  @IsIn(['usd', 'eur'])
  currency?: string;
}
