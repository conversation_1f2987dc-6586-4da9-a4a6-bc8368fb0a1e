import { zodResolver } from '@hookform/resolvers/zod';
import { useMutation } from '@tanstack/react-query';
import { AxiosError } from 'axios';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { useNavigate } from 'react-router-dom';
import { toast } from 'sonner';
import { z } from 'zod';

import { Button } from '@/components/common/Button';
import { Input } from '@/components/common/Input';
import { Loader } from '@/components/common/Loader';
import { useAuth } from '@/contexts/AuthContext';
import { NAVIGATE_PATH } from '@/helpers/constants';
import { createOrganizationRequest } from '@/helpers/requests';

// Schema for organization creation
const createOrganizationSchema = z.object({
  name: z
    .string()
    .min(1, 'Organization name is required')
    .max(100, 'Organization name cannot exceed 100 characters'),
});

type CreateOrganizationFormData = z.infer<typeof createOrganizationSchema>;

export function CreateOrganizationPage() {
  const navigate = useNavigate();
  const { switchOrganization, logout } = useAuth();
  const [isLoading, setIsLoading] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<CreateOrganizationFormData>({
    resolver: zodResolver(createOrganizationSchema),
    defaultValues: {
      name: '',
    },
  });

  const createOrganizationMutation = useMutation({
    mutationFn: createOrganizationRequest,
    onSuccess: async (data) => {
      // Switch to the newly created organization
      await switchOrganization(data._id);
      toast.success('Organization created successfully');
      navigate(NAVIGATE_PATH.home);
    },
    onError: (error: AxiosError) => {
      const message = (error?.response?.data as { message: string })?.message;
      toast.error(message || 'Failed to create organization');
      setIsLoading(false);
    },
  });

  const onSubmit = (data: CreateOrganizationFormData) => {
    setIsLoading(true);
    createOrganizationMutation.mutate(data);
  };

  const handleLogout = () => {
    logout();
  };

  return (
    <div className="relative flex flex-col items-center justify-center min-h-screen p-4 bg-msGray-6">
      {/* Header elements */}
      <div className="absolute top-4 left-4 right-4 md:top-8 md:left-8 md:right-8 flex items-center justify-between">
        <img
          src="/images/logo-with-text.png"
          alt="CV Inventory"
          className="w-48"
        />
        <Button variant="default" size="sm" onClick={handleLogout}>
          Log Out
        </Button>
      </div>

      <div className="w-full max-w-md p-8 mt-16 rounded-lg shadow-md bg-msWhite md:mt-0">
        <div className="flex flex-col items-center mb-8">
          <div className="flex justify-center">
            <img width={40} src="/images/logo.png" alt="logo" />
          </div>
          <h1 className="mb-2 text-2xl font-bold text-center text-msBlack">
            Create New Organization
          </h1>
          <p className="text-center text-msGray-3">
            Your previous organization is no longer available. Please create a
            new one to continue.
          </p>
        </div>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          <div className="space-y-2">
            <label
              htmlFor="name"
              className="block text-sm font-medium text-msGray-2"
            >
              Organization Name
            </label>
            <Input
              id="name"
              placeholder="Enter organization name"
              {...register('name')}
              error={errors.name?.message}
              disabled={isLoading}
              autoFocus
            />
          </div>

          <Button type="submit" className="w-full" disabled={isLoading}>
            {isLoading ? <Loader size={20} /> : 'Create Organization'}
          </Button>
        </form>
      </div>
    </div>
  );
}
