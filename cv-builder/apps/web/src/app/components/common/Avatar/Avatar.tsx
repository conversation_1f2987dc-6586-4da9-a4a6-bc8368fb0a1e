interface AvatarProps {
  url?: string | null;
  name?: string;
  size?: number;
}

export function Avatar({ url, name, size = 48 }: AvatarProps) {
  // Priority 1: If url exists and is valid, render image
  if (url && !/dummy-user\.png$/.test(url)) {
    return (
      <div
        style={{
          width: size,
          height: size,
        }}
        className="flex-shrink-0 rounded-full"
      >
        <img
          src={url}
          onError={(e) => {
            e.currentTarget.src = '/images/person.png'; // fallback to default image
          }}
          alt="avatar"
          className="w-full h-full rounded-full object-cover"
        />
      </div>
    );
  }

  // Priority 2: If name exists, render initials
  if (name) {
    return (
      <div
        className="flex items-center justify-center flex-shrink-0 rounded-full bg-msMagicAi-3 text-msMagicAi-5"
        style={{
          width: size,
          height: size,
          fontSize: size * 0.4, // Responsive font size based on size
        }}
      >
        {name.split(' ').map((word, index) => {
          if (index > 1 || !word || !word[0]) return;
          return word[0].toUpperCase();
        })}
      </div>
    );
  }

  // Priority 3: Fallback to default icon
  return (
    <div
      style={{
        width: size,
        height: size,
      }}
      className="flex-shrink-0 rounded-full"
    >
      <img
        src={
          !url || url === '/icons/dummy-user.png' ? '/images/person.png' : url
        }
        onError={(e) => {
          e.currentTarget.src = '/images/person.png'; // fallback
        }}
        alt="Something is wrong with the avatar"
        className="w-full h-full rounded-full object-cover"
      />
    </div>
  );
}
