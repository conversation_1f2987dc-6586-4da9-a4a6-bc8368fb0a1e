import { Body, Controller, Post, UnauthorizedException } from '@nestjs/common';

import { ParserService } from './parser.service';

@Controller()
export class ParserController {
  constructor(private readonly parserService: ParserService) {}

  @Post('metadata')
  async metadata(@Body() body) {
    if (body.token !== process.env.API_TOKEN) {
      throw new UnauthorizedException();
    }
    if (!body.url) {
      throw new Error('no url param');
    }

    try {
      const data = await this.parserService.getMetadata(body.url);
      if (!data) {
        return { error: true };
      }
      return { data, error: false };
    } catch (error) {
      console.log('metadata error: ', error);
      return { data: null, error: true };
    }
  }
}
