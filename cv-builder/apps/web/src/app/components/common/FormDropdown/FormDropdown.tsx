import { ChevronDown } from 'lucide-react';

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuGroup,
  DropdownMenuTrigger,
} from '../DropdownMenu';
import { ReadOnlyDisplay } from '../ReadOnlyDisplay';

import { cn } from '@/lib/utils';

interface FormDropdownProps {
  value?: string;
  options: { value: string; label: string }[];
  selectableOptions?: { value: string; label: string }[]; // Options that can be selected (for permissions)
  onValueChange?: (value: string) => void;
  placeholder?: string;
  error?: string;
  isReadOnly?: boolean;
  disabled?: boolean;
  className?: string;
}

export function FormDropdown({
  value,
  options,
  selectableOptions,
  onValueChange,
  placeholder = 'Select an option',
  error,
  isReadOnly,
  disabled,
  className,
}: FormDropdownProps) {
  // Use the full options list to find the selected option label
  const selectedOption = options.find((opt) => opt.value === value);

  // Use selectableOptions if provided, otherwise fall back to all options
  const dropdownOptions = selectableOptions || options;

  if (isReadOnly) {
    return (
      <div>
        <ReadOnlyDisplay
          value={selectedOption?.label}
          placeholder={placeholder}
          className={className}
        />
        {error && <p className="mt-1 text-smalldoge-2 text-msRed-1">{error}</p>}
      </div>
    );
  }

  return (
    <div>
      <DropdownMenu modal={false}>
        <DropdownMenuTrigger asChild disabled={disabled}>
          <div
            className={cn(
              'flex items-center cursor-pointer font-bold',
              error && 'text-msRed-1',
              disabled && 'opacity-50 cursor-not-allowed',
              className,
            )}
          >
            <span className="text-smalldoge-3">
              {selectedOption?.label || placeholder}
            </span>
            <ChevronDown className="w-4 h-4 ml-1" />
          </div>
        </DropdownMenuTrigger>
        <DropdownMenuContent
          collisionPadding={10}
          align="start"
          className="w-fit prevent-drawer-outside-click"
        >
          <DropdownMenuGroup>
            {dropdownOptions.map((option) => (
              <DropdownMenuItem
                key={option.value}
                onClick={() => onValueChange?.(option.value)}
                className="font-bold text-smalldoge-3"
              >
                {option.label}
              </DropdownMenuItem>
            ))}
          </DropdownMenuGroup>
        </DropdownMenuContent>
      </DropdownMenu>
      {error && <p className="mt-1 text-smalldoge-2 text-msRed-1">{error}</p>}
    </div>
  );
}
