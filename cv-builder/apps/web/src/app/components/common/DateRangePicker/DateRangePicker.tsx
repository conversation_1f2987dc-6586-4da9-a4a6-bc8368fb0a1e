import { format } from 'date-fns';
import { CalendarIcon } from 'lucide-react';
import * as React from 'react';
import { DateRange } from 'react-day-picker';

import {
  Button,
  Calendar,
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '../../common';

import { useResponsive } from '@/hooks/useResponsive';
import { cn } from '@/lib/utils';

interface DateRangePickerProps {
  date?: DateRange;
  className?: string;
  onDateChange: (date: DateRange | undefined) => void;
}

export function DateRangePicker({
  date,
  className,
  onDateChange,
}: DateRangePickerProps) {
  const { isMobile } = useResponsive();
  return (
    <div className={cn('w-full', className)}>
      <Popover>
        <PopoverTrigger asChild>
          <Button
            id="date"
            variant={'outline'}
            className={cn(
              'w-full h-9 px-3 py-1 justify-start text-left font-bold text-smalldoge-3',
              !date && 'text-msGray-3',
              date && 'text-msGray-2',
            )}
          >
            <CalendarIcon size={16} />
            {date?.from ? (
              date.to ? (
                <>
                  {format(date.from, 'LLL dd, y')} -{' '}
                  {format(date.to, 'LLL dd, y')}
                </>
              ) : (
                format(date.from, 'LLL dd, y')
              )
            ) : (
              <span>Pick a date</span>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent
          className="w-auto p-0 prevent-drawer-outside-click"
          align="start"
          collisionPadding={10}
        >
          <Calendar
            initialFocus
            mode="range"
            defaultMonth={date?.from}
            selected={date}
            onSelect={onDateChange}
            numberOfMonths={isMobile ? 1 : 2}
          />
        </PopoverContent>
      </Popover>
    </div>
  );
}
