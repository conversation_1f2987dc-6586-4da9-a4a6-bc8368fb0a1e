# MongoDB Custom ID Structure Rules

## Overview
This project uses a hierarchical custom ID system where organization IDs serve as prefixes for all related entities.

## ID Structure Rules

### 1. Organization IDs (Top Level)
- **Format**: `[2-char prefix]` + 22 zeros
- **Example**: `a10000000000000000000000`
- Organizations are the root entities and their 2-character prefix is used for all child entities

### 2. Child Entity IDs
All entities belonging to an organization must use the organization's 2-character prefix:

- **Format**: `[2-char org prefix]` + 20 zeros + `[entity_type_suffix]`
- **Organization prefix**: First 2 characters of the org ID (e.g., `a1`)
- **Entity suffixes**:
  - Members: `c1`, `c2`, `c3`, etc.
  - Customers: `e1`, `e2`, `e3`, etc.
  - Invites: `f1`, `f2`, `f3`, etc.

### Examples
```javascript
// Organization
const org1Id = new ObjectId('a10000000000000000000000');

// Members of org1
const org1MemberId1 = new ObjectId('a100000000000000000000c1');
const org1MemberId2 = new ObjectId('a100000000000000000000c2');

// Customers of org1
const org1CustomerId1 = new ObjectId('a100000000000000000000e1');
const org1CustomerId2 = new ObjectId('a100000000000000000000e2');

// Invites for org1
const org1InviteId1 = new ObjectId('a100000000000000000000f1');
const org1InviteId2 = new ObjectId('a100000000000000000000f2');
```

## Invite Token Structure

Invite tokens follow a specific 8-character format that encodes relationship information:

### Token Format: `[org_id][sender_id][invite_id]`

- **Characters 1-2**: Target organization ID prefix (e.g., `a1`)
- **Characters 3-4**: Sender's ID suffix OR `00` if no sender
- **Characters 5-6**: Sender's ID suffix (repeated) OR invite type identifier
- **Characters 7-8**: Invite ID suffix

### Token Examples
```javascript
// Invite from org1 with invite ID f1
{
  _id: new ObjectId('a100000000000000000000f1'),
  token: 'a10000f1'  // a1 + 00 + 00 + f1
}

// Invite from user a4b1 to org a1 with invite ID f2
{
  _id: new ObjectId('a100000000000000000000f2'),
  token: 'a1a4b1f2'  // a1 + a4 + b1 + f2
}
```

## Implementation Guidelines

### When Creating New Entities:
1. **Always** start with the organization ID prefix (first 22 characters)
2. Append the appropriate entity type suffix
3. Ensure sequential numbering within each entity type per organization

### When Creating Invite Tokens:
1. Extract the 2-character org prefix (e.g., `a1`)
2. Determine sender information (use `00` if system-generated)
3. Extract the 2-character invite ID suffix
4. Concatenate following the 8-character format

### Validation Rules:
- Organization IDs must be exactly 24 characters, starting with 2-character prefix followed by 22 zeros
- Child entity IDs must use their parent org's 2-character prefix + 20 zeros + entity suffix
- Entity type suffixes must follow the established patterns (`c`, `e`, `f`)
- Invite tokens must be exactly 8 characters
- All IDs must be valid MongoDB ObjectId format

## Code Examples

```javascript
// Helper function to generate child entity ID
function generateChildEntityId(orgId, entityType, sequenceNumber) {
  const orgPrefix = orgId.toString().substring(0, 2);
  const zeros = '0'.repeat(20);
  const suffix = entityType + sequenceNumber;
  return new ObjectId(orgPrefix + zeros + suffix);
}

// Helper function to generate invite token
function generateInviteToken(orgId, senderId, inviteId) {
  const orgPrefix = orgId.toString().substring(0, 2);
  const senderSuffix = senderId ? senderId.toString().slice(-4) : '0000';
  const inviteSuffix = inviteId.toString().slice(-2);
  return orgPrefix + senderSuffix + inviteSuffix;
}
```

## Important Notes
- This ID structure creates a clear hierarchy and relationship mapping
- The 2-character org prefix (e.g., `a1`) is the meaningful identifier, followed by padding zeros
- IDs encode organizational belonging directly in their structure
- Tokens provide a compact way to encode invite relationship data
- Always validate ID format before database operations
- Consider indexing strategies that leverage the 2-character prefix structure