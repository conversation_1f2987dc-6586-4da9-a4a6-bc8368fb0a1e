# compiled output
/dist
/node_modules

# Logs
logs
*.log
npm-debug.log*
pnpm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# OS
.DS_Store

# Tests
/coverage
/.nyc_output

# IDEs and editors
/.idea
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace

# IDE - VSCode
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

.env

# Yarn files to include
!.yarn/*
!.yarn/releases
!.yarn/plugins
!.yarnrc.yml

# Yarn files to ignore
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz

localHelpers
