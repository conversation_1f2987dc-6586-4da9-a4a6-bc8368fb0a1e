import { useQuery } from '@tanstack/react-query';
import { uniqueId } from 'lodash';
import { Plus } from 'lucide-react';
import { useContext, useEffect, useMemo, useState } from 'react';
import { useNavigate, useParams, useSearchParams } from 'react-router-dom';
import { Section } from 'shared/types';

import { CvList, CvPreview, CvSettingsForm, Drawer } from '../components';
import {
  getCvsByMemberRequest,
  getMemberByIdRequest,
} from '../helpers/requests';

import { ButtonPrimary, MemberBreadcrumb } from '@/components';
import { LayoutContext } from '@/components/Layout/Layout';
import { NAVIGATE_PATH } from '@/helpers/constants';
import { createTruncatedDisplayName } from '@/helpers/nameUtils';
import { useDeleteCv, useDuplicateCv } from '@/hooks/useCvMutations';
import { useResponsive } from '@/hooks/useResponsive';
import { cn } from '@/lib/utils';

export function MemberCVsListPage() {
  const navigate = useNavigate();
  const { memberId } = useParams();
  const [searchParams, setSearchParams] = useSearchParams();
  const { setHeaderCallback } = useContext(LayoutContext);
  const { isSmallScreen } = useResponsive();

  const [activeCvId, setActiveCvId] = useState<string | null>(null);
  const [previewActive, setPreviewActive] = useState<boolean>(false);
  const [newCvFormActive, setNewCvFormActive] = useState<boolean>(false);

  const { data: member } = useQuery({
    enabled: !!memberId,
    queryKey: ['member', { memberId }],
    queryFn: () => getMemberByIdRequest(memberId as string),
  });

  const { data: memberCvs, isLoading: cvsLoading } = useQuery({
    enabled: !!memberId,
    queryKey: ['memberCvs', { memberId }],
    queryFn: () => getCvsByMemberRequest(memberId as string),
  });

  const { mutate: duplicateCv } = useDuplicateCv();
  const { mutate: deleteCv } = useDeleteCv();

  useEffect(() => {
    if (member) {
      const displayName = createTruncatedDisplayName(
        member.firstName,
        member.lastName,
        20,
      );
      setHeaderCallback(`${displayName}'s CVs`);
    } else {
      setHeaderCallback('CVs');
    }

    return () => setHeaderCallback('');
  }, [setHeaderCallback, member]);

  const [activeCv, activeCvSections] = useMemo(() => {
    const cvToDisplay = memberCvs?.find((cv) => cv._id === activeCvId);

    if (!cvToDisplay) return [];

    const defaultSections = Object.entries(cvToDisplay.sections)
      .filter(([key, value]) => key !== '_id' && key !== 'customSections')
      .map(([key, value]) => ({
        id: key,
        ...value,
      }));

    const customSections = cvToDisplay.sections.customSections.map((sec) => ({
      id: 'customSection' + uniqueId(),
      ...sec,
    }));

    return [cvToDisplay, [...defaultSections, ...customSections] as Section[]];
  }, [memberCvs, activeCvId]);

  // Handle query parameters for editCv and previewCv
  useEffect(() => {
    const previewCvId = searchParams.get('previewCv');

    if (previewCvId) {
      setActiveCvId(previewCvId);
      setPreviewActive(true);

      searchParams.delete('previewCv');
      setSearchParams(searchParams, { replace: true });
    }
  }, [memberCvs, searchParams, setSearchParams]);

  const redirectToEditPage = (cvId: string) =>
    navigate(
      `${NAVIGATE_PATH.cvList}/${memberId}/${NAVIGATE_PATH.cvEdit}/${cvId}`,
    );

  const cvSettings = () => {
    if (!memberId) return;

    return (
      <CvSettingsForm
        memberId={memberId}
        onCanceled={() => setNewCvFormActive(false)}
        onSubmitted={(cvId) => redirectToEditPage(cvId as string)}
      />
    );
  };

  const cvPreview = () => {
    if (!activeCv || !member || !activeCvSections) return;

    return (
      <CvPreview
        cv={activeCv}
        member={member}
        sections={activeCvSections}
        onEdit={() => redirectToEditPage(activeCv._id)}
      />
    );
  };

  if (!memberId) return null;

  return (
    <div className="flex h-full">
      <div className="flex flex-col w-full">
        <div className="flex items-center justify-between h-10 px-4 py-2 border-b border-msGray-5">
          <MemberBreadcrumb
            memberName={createTruncatedDisplayName(
              member?.firstName,
              member?.lastName,
              20,
            )}
            isSmallScreen={isSmallScreen}
            mode="list"
          />
          <ButtonPrimary
            variant="blackCompact"
            className="flex items-center"
            onClick={() => {
              setNewCvFormActive(true);
              setActiveCvId(null);
              setPreviewActive(false);
            }}
          >
            <Plus size={16} />
            New CV
          </ButtonPrimary>
        </div>
        <CvList
          loading={cvsLoading}
          cvs={memberCvs}
          activeCv={previewActive ? activeCvId : null}
          onCvEdit={(cvId) => redirectToEditPage(cvId)}
          onCvDelete={(cvId) => {
            if (activeCvId === cvId) {
              setActiveCvId(null);
              setPreviewActive(false);
            }

            deleteCv({ cvId, memberId });
          }}
          onCvDuplicate={(cvId) => duplicateCv({ cvId, memberId })}
          onCvClick={(cvId) => {
            setActiveCvId(cvId);
            setPreviewActive(true);
            setNewCvFormActive(false);
          }}
          onCreateNewCv={() => {
            setNewCvFormActive(true);
            setActiveCvId(null);
            setPreviewActive(false);
          }}
        />
      </div>

      {isSmallScreen ? (
        <>
          <Drawer
            active={previewActive}
            onClose={() => setPreviewActive(false)}
          >
            {cvPreview()}
          </Drawer>
          <Drawer
            active={newCvFormActive}
            onClose={() => setNewCvFormActive(false)}
          >
            <div className="overflow-auto">{cvSettings()}</div>
          </Drawer>
        </>
      ) : (
        <div
          className={cn(
            'hidden border-l border-msGray-5 w-[640px] flex-shrink-0',
            (newCvFormActive || previewActive) && 'block',
          )}
        >
          {previewActive && cvPreview()}
          {newCvFormActive && <div className="p-5">{cvSettings()}</div>}
        </div>
      )}
    </div>
  );
}
