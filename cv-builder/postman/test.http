### Login to get session cookie
POST http://localhost:3000/api/sign-in
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}

### Cancel profile deletion
POST http://localhost:3000/api/users/me/cancel-deletion
Content-Type: application/json

### Cancel organization deletion
POST http://localhost:3000/api/organization/cancel-deletion
Content-Type: application/json

### Get members
GET http://localhost:3000/api/members

### Delete member
DELETE http://localhost:3000/api/members/MEMBER_ID_HERE

### Update organization settings
PUT http://localhost:3000/api/organization
Content-Type: application/json

{
  "name": "Updated Organization Name",
  "description": "Updated description",
  "website": "https://example.com",
  "timezone": "America/New_York",
  "locations": ["New York", "San Francisco"]
}

### Upload organization photo
POST http://localhost:3000/api/organization/photo-update
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW

------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="photo"; filename="logo.png"
Content-Type: image/png

< ./path/to/your/logo.png
------WebKitFormBoundary7MA4YWxkTrZu0gW--

### Remove organization photo
POST http://localhost:3000/api/organization/photo-update
Content-Type: application/x-www-form-urlencoded

removePhoto=true