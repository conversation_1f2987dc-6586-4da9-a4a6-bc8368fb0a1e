### Tools

Prefer yarn over npm
When you need to add a new shadcn component, do it like this: `yarn add sonner`, because cli is not working for some reason
When you need to generate something for nest(like a module), we use nx with nest plugin. So do it like this:
`nx g @nx/nest:module ...` and add a flag to skip tests. Because we want to launch as soon as possible, so we skip tests
When you need to add new npm package, do it like this `yarn add <package_name>`. Because we have a shared package.json between our frontend and backend.
When you need to start either the frontend or backend, use `yarn nx serve web`, `yarn nx serve api`.
Do not run build, lint or start, just ask the user, and he will provide the needed info.
Sometimes you can just use helper method from lodash, instead of writing your own custom

### When you write Pages.tsx:

- do it like this
  `export function MemberPage() {`
- add the path to @apps/web/src/app/helpers/constants.ts to NAVIGATE_PATH, or use the NAVIGATE_PATH if there is path already

### Text Styling

<!-- We use css variables
so write like this:
`<div className="bg-background text-foreground" />`
instead of
`<div className="bg-zinc-950 dark:bg-white" />` -->

We prefer using utility classes
so for example, write like this:
`<div className="bg-msBlack" />`
instead of
`<div className="bg-background" />`

### Features For Later

If you need to implement something, that is in the array of later_features, do not implement it
later_features = ['google auth']

### Additional Parameters

If in the prompt you receive Do not write comments, this means that after you completed the task, remove all the comments from the file that you added for yourself. Do not remove comments that are already in the file.

### Frontend-Backend Communication

When you need to communicate with the backend, use the requests from @apps/web/src/app/helpers/requests.ts. If the request is not there, create it. Also, add a return type, like in the apps/web/src/app/helpers/requests.ts:331-331
```
Promise<Invite[]> 
```
Use @tanstack/react-query, always disable the button if the mutation is pending.
All authentication requests should be done through just /api, like /api/sign-up.

### Types

If you need some types, use them from shared/types.ts.

<!-- If you need some DTOs, use them from shared/dto.ts. Like this `import { UpdateUserDto } from 'shared/dto/user/update-user.dto';` -->

If the needed dto is not in the shared folder, move it there. Do not import from `@shared/types`, there should be no "@" symbol, like this `shared/types`

### Icons

We use lucide-react for icons. If you need custom icons, add them to @apps/web/src/app/components/common/Icon/icons and use the Icon component from there.

### Components

We use shadcn
When you need a button, use either ButtonPrimary or ButtonSecondary
Leverage the TypographyMuted when you need a gray text
Do not extract props to interface, if there is only 1 prop.
Do not use string interpolation in classNames. Instead of `className={`text-msGray ${className || ''}`}>` use cn `classNames={cn('text-msGray', someClassName)}`.
Use date-fns to format dates.

<!-- After new building block component was added, add it to apps/web/src/app/pages/CommonComponentsTestPage.tsx. -->

### Imports

Firstly, write imports from libraries. Then blank line. Then imports from local files.
Do not import React from react, this is not needed in our version of React
Use path aliases, like @/components/common/Input or @/helpers/constants

### Forms

If you need to implement a form, use react-hook-form and zod for validation.
Create a Input type for the form in apps/shared/inputs.ts

### Routes

Use react-router-dom.
Prefer using useNavigate instead of <Navigate>

### Research

If you got some new documentation from context7 mcp, you can create a new file in the for-ai folder, and store it there

### Error Handling

in the service file, make a constant with all the possible errors, and use that
do not throw an error of class Error, throw some nestjs errors, like BadRequestException, and so on
On the frontend, use errors from mutations, like this

```typescript
onError: (error: AxiosError) => {
  const message = (error?.response?.data as { message: string })?.message;
  setError(message);
},
```

Here is an example of handling errors in the backend services

```typescript
} catch (error) {
  this.logger.error(
    `Error constructing Stripe event: ${error.message}`,
    error.stack,
  );
  // It's often better to throw a specific HTTP exception
  throw new BadRequestException(`Webhook Error: ${error.message}`);
}
```

### Env Variables

You do not have an access to .env file. So when you need to add a new env variable, add a name of the variable to .env-template and setup_env.sh. User will manually add a value to .env later.

### Explanation

When user asks you to explain something, like `explain <topic>`, create a new md file in for-ai folder.

### Testing

We are using Rest Client vscode extension. So when you need to test the endpoint, write the request in postman/test.http, and wait for the user to give you the response. Rest client supports cookie, And we usually do a login request to set the cookie, before sending other requests.

Add data-testid only where necessary: use it on key interactive elements and stable containers (e.g., buttons, inputs, modals) when other selectors are fragile. Avoid overuse. Prioritize test clarity and maintainability.

### Fonts

Use Doge instead of the default tailwind styles like xs, sm and so on. apps/web/tailwind.config.js take a look at tailwind config at the theme fontWeight, fontSize.
For example, write like `text-smalldoge-3` instead of `text-sm` and so on.

### Figma MCP

When you implement design based on figma, take a look if you have the needed typography. For example, @apps/web/src/app/components/Settings/Typography/Typography.tsx. If there is no needed typography style, create one.

### Database

We use mongodb. In MongoDB (and Mongoose), for a one-to-many relationship like:
One Organization has many CVs
You generally only store the reference in the "many" side — i.e., in the CV document.
When add new index, add info about it to DbIndexes.md

### Clean Code

The code should be like a newspaper, meaning the most important pieces at the top, less important(like the ones that top level-ones use) at the bottom

### Hooks

Hooks declared before other state management

### Authorization

We use a two-layer permission architecture.
Layer 1 (Guard Level): General permissions check based on user role in the controller using `@CheckAbilities`.
Layer 2 (Service Level): Entity-specific permissions check within the service method using `ForbiddenError.from(ability).throwUnlessCan()`.
This ensures separation of concerns, reusability, and maintainability.

### Colors
Use the colors defined in apps/web/tailwind.config.js:45-45
```
colors: {
```, do not write custom ones like `text-red-500` and so on.
