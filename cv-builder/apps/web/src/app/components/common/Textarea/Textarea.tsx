import * as React from 'react';

import { ReadOnlyDisplay } from '../ReadOnlyDisplay';

import { cn } from '@/lib/utils';

interface TextareaProps extends React.ComponentProps<'textarea'> {
  error?: string;
  isReadOnly?: boolean;
}

const Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(
  ({ className, error, isReadOnly, ...props }, ref) => {
    if (isReadOnly) {
      return (
        <div>
          <ReadOnlyDisplay
            value={props.value?.toString()}
            placeholder={props.placeholder}
          />
          {error && (
            <p className="mt-1 text-smalldoge-4 text-msRed-1">{error}</p>
          )}
        </div>
      );
    }

    return (
      <div>
        <textarea
          className={cn(
            'flex min-h-[60px] w-full rounded-md border border-input bg-transparent px-3 py-2 text-smalldoge-3 shadow-sm placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm',
            error && 'border-msRed-1',
            className,
          )}
          ref={ref}
          {...props}
        />
        {error && <p className="mt-1 text-smalldoge-4 text-msRed-1">{error}</p>}
      </div>
    );
  },
);
Textarea.displayName = 'Textarea';

export { Textarea };
