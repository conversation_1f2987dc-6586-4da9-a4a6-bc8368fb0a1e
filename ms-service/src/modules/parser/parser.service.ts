import { Injectable } from '@nestjs/common';
import { <PERSON><PERSON><PERSON>, <PERSON>, firefox } from 'playwright';

const metascraper = require('metascraper')([
  require('metascraper-url')(),
  require('metascraper-title')(),
  require('metascraper-image')(),
  require('metascraper-description')(),
  require('metascraper-logo-favicon')(),
]);

interface WebsiteMetadata {
  url: string;
  title?: string;
  image?: string;
  description?: string;
  logo?: string;
}
@Injectable()
export class ParserService {
  async getHTML(url: string): Promise<string | null> {
    let browser: Browser | null = null;

    try {
      browser = await firefox.launch({
        headless: true,
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--disable-gpu',
          '--disable-background-timer-throttling',
          '--disable-backgrounding-occluded-windows',
          '--disable-renderer-backgrounding',
          '--blink-settings=imagesEnabled=false',
          '--ignore-certificate-errors',
        ],
      });

      const context = await browser.newContext({
        userAgent:
          'Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Mobile/15E148 Safari/604.1',
        viewport: { width: 375, height: 812 },
      });

      const page: Page = await context.newPage();

      await page.route('**/*', (route) => {
        const resourceType = route.request().resourceType();
        if (['stylesheet', 'font', 'image'].includes(resourceType)) {
          route.abort();
        } else {
          route.continue();
        }
      });

      await page.goto(url, { waitUntil: 'domcontentloaded', timeout: 30000 });

      await page.waitForTimeout(3000);

      const html: string = await page.content();

      await browser.close();
      return html;
    } catch (error) {
      console.error('Error fetching HTML:', error.message);
      return null;
    }
  }

  async getMetadata(url: string): Promise<any> {
    const html = await this.getHTML(url);

    if (!html) {
      return null;
    }

    const metadata: WebsiteMetadata = await metascraper({ html, url });

    return {
      title: metadata.title,
      sourceFavicon: metadata.logo,
      sourceName: new URL(url).hostname,
      url,
      thumbnail: metadata.image,
      description: metadata.description,
    };
  }
}
