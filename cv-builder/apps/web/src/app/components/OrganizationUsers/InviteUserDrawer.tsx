import { zodResolver } from '@hookform/resolvers/zod';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { AxiosError } from 'axios';
import { ChevronDown, Info } from 'lucide-react';
import React, { useMemo } from 'react';
import { useForm } from 'react-hook-form';
import { createInviteSchema, CreateInviteInput } from 'shared/schemas';
import { UserRole } from 'shared/types';
import { toast } from 'sonner';

import {
  ButtonPrimary,
  Drawer,
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuTrigger,
  Input,
  Tip,
} from '@/components/common';
import { TypographyLabel } from '@/components/Settings/Typography/Typography';
import { createOrganizationInviteRequest } from '@/helpers/requests';

const roleOptions = [
  { value: UserRole.ADMIN, label: 'Admin' },
  { value: UserRole.MEMBER, label: 'Member' },
];

interface InviteUserDrawerProps {
  isOpen: boolean;
  onClose: () => void;
}

export function InviteUserDrawer({ isOpen, onClose }: InviteUserDrawerProps) {
  const queryClient = useQueryClient();

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
    reset,
    setError,
  } = useForm<CreateInviteInput>({
    resolver: zodResolver(createInviteSchema),
    defaultValues: {
      email: '',
      name: '',
      role: UserRole.MEMBER,
    },
  });

  const watchedRole = watch('role');

  const renderField = (
    label: string | React.ReactElement,
    children: React.ReactNode,
    error?: string,
  ) => (
    <div className="grid grid-cols-[120px_1fr] md:grid-cols-[200px_1fr] items-center gap-x-4 py-2">
      <div className="font-bold text-smalldoge-3 text-msGray-3">
        {typeof label === 'string' ? label : label}
      </div>
      <div>
        {children}
        {error && <p className="mt-1 text-smalldoge-4 text-msRed-1">{error}</p>}
      </div>
    </div>
  );

  const { mutate: createInvite, isPending: isCreatingInvite } = useMutation({
    mutationFn: createOrganizationInviteRequest,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['organizationInvites'] });
      reset();
      onClose();
      toast.success('Invite sent successfully');
    },
    onError: (error: AxiosError) => {
      const message = (error?.response?.data as { message: string })?.message;
      const errorMessage = message || 'Failed to send invite';
      setError('root', { message: errorMessage });
      toast.error(errorMessage);
    },
  });

  const selectedRoleLabel = useMemo(() => {
    return (
      roleOptions.find((option) => option.value === watchedRole)?.label ||
      watchedRole
    );
  }, [watchedRole]);

  const onSubmit = (data: CreateInviteInput) => {
    const payload = {
      email: data.email,
      role: data.role,
      ...(data.name && data.name.trim() && { name: data.name.trim() }),
    };
    createInvite(payload);
  };

  const handleClose = () => {
    reset();
    onClose();
  };

  return (
    <Drawer active={isOpen} onClose={handleClose} title="Invite new member">
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
        {renderField(
          'Email',
          <Input
            {...register('email')}
            id="email"
            type="email"
            placeholder="<EMAIL>"
            className="w-full font-bold text-smalldoge-3 text-msBlack"
            error={errors.email?.message}
          />,
        )}
        {renderField(
          <div className="flex items-center gap-2">
            <span>Name (optional)</span>
            <Tip content="This will only be applicable when inviting a new user">
              <Info className="w-4 h-4 text-msGray-3 cursor-help" />
            </Tip>
          </div>,
          <Input
            {...register('name')}
            id="name"
            type="text"
            placeholder="John Doe"
            className="w-full font-bold text-smalldoge-3 text-msBlack"
            error={errors.name?.message}
          />,
        )}
        {renderField(
          'Role',
          <DropdownMenu modal={false}>
            <DropdownMenuTrigger asChild>
              <div className="flex items-center ml-1 cursor-pointer bg-background text-foreground">
                <TypographyLabel className="text-smalldoge-3">
                  {selectedRoleLabel}
                </TypographyLabel>
                <ChevronDown className="w-4 h-4 ml-1" />
              </div>
            </DropdownMenuTrigger>
            <DropdownMenuContent
              collisionPadding={10}
              align="start"
              className="w-fit prevent-drawer-outside-click"
            >
              <DropdownMenuGroup>
                {roleOptions.map((option) => (
                  <DropdownMenuItem
                    key={option.value}
                    onClick={() => setValue('role', option.value)}
                    className="font-bold text-smalldoge-3"
                  >
                    <TypographyLabel className="text-smalldoge-3">
                      {option.label}
                    </TypographyLabel>
                  </DropdownMenuItem>
                ))}
              </DropdownMenuGroup>
            </DropdownMenuContent>
          </DropdownMenu>,
          errors.role?.message,
        )}
        {errors.root && (
          <div className="p-3 border rounded-md bg-msRed-3 border-msRed-2">
            <p className="text-smalldoge-3 text-msRed-1">
              {errors.root.message}
            </p>
          </div>
        )}
        <div className="flex justify-end pt-4">
          <ButtonPrimary
            type="submit"
            disabled={isCreatingInvite}
            className="px-2 py-1"
            variant="blackCompact"
          >
            {isCreatingInvite ? 'Sending Invite...' : 'Send Invite'}
          </ButtonPrimary>
        </div>
      </form>
    </Drawer>
  );
}
