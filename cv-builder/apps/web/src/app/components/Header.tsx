import classNames from 'classnames';

interface HeaderProps {
  menuActive: boolean;
  headerText?: string;
  onMenuClick: () => void;
}

export function Header({ menuActive, headerText, onMenuClick }: HeaderProps) {
  return (
    <header className="fixed z-50 top-0 bg-msWhite left-0 w-screen flex items-center justify-between p-2 h-14 border-b md:hidden">
      {/* Burger menu */}
      <button
        className="size-7 bg-msGray-6 rounded-sm focus:outline-none p-1 prevent-nav-outside-click"
        onClick={onMenuClick}
      >
        <div className="flex flex-col w-full h-full justify-evenly">
          <div
            className={classNames(
              'w-full h-0.5 bg-msBlack rounded-md transition-transform duration-300',
              menuActive && 'transform rotate-45 translate-y-[5.5px]',
            )}
          />
          <div
            className={classNames(
              'w-full h-0.5 bg-msBlack rounded-md transition-opacity duration-300',
              menuActive && 'opacity-0',
            )}
          />
          <div
            className={classNames(
              'w-full h-0.5 bg-msBlack rounded-md transition-transform duration-300',
              menuActive && 'transform -rotate-45 -translate-y-[5.5px]',
            )}
          />
        </div>
      </button>
      <span className="text-smalldoge-1 font-black">{headerText}</span>
      <img width={40} src="/images/logo.png" alt="logo" />
    </header>
  );
}
