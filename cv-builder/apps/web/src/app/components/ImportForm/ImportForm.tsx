import { useState } from 'react';
import { MemberSource } from 'shared/types';

import { MemberProfileForm } from '../../components';
import { Label } from '../common';
import { MuchskillsView } from './components/MuchskillsView';

import { OrganizationData } from '@/contexts/AuthContext';
import { cn } from '@/lib/utils';

interface ImportFormProps {
  organization: OrganizationData;
  onClose: () => void;
}

export function ImportForm({ organization, onClose }: ImportFormProps) {
  const [importType, setImportType] = useState<MemberSource>(
    MemberSource.cvinventory,
  );

  return (
    <div className="flex flex-col h-full">
      <Label label="Create from" />
      <div className="flex mt-2 mb-4 space-x-2 overflow-x-auto flex-shrink-0">
        <button
          className={cn(
            'flex shrink-0 space-x-1 items-center px-2 py-1 rounded-[8px] text-foreground',
            importType === MemberSource.muchskills && 'bg-msBlack text-msWhite',
          )}
          onClick={() => setImportType(MemberSource.muchskills)}
        >
          <div
            className={cn(
              'flex justify-center items-center w-8 h-8 rounded-full',
              importType !== MemberSource.muchskills && 'bg-msBlack',
            )}
          >
            <img width={18} src="/icons/muchskills.svg" alt="muchskillsLogo" />
          </div>
          <div className="flex flex-col items-start">
            <span className="text-smalldoge-3">Muchskills</span>
            {/* <span className="text-smalldoge-5">202 candidates</span> */}
          </div>
        </button>
        <button
          disabled
          title="Not available yet"
          className={cn(
            'flex shrink-0 space-x-1 items-center px-2 py-1 rounded-[8px] text-msGray-4',
            'opacity-50 cursor-not-allowed',
          )}
          onClick={() => setImportType(MemberSource.linkedin)}
        >
          <div
            className={cn(
              'flex justify-center items-center w-8 h-8 rounded-full bg-msGray-4',
            )}
          >
            <img width={18} src="/icons/linkedin.svg" alt="linkedinLogo" />
          </div>
          <div className="flex flex-col items-start">
            <span className="text-smalldoge-3">LinkedIn</span>
            {/* <span className="text-smalldoge-5">98 candidates</span> */}
          </div>
        </button>
        <button
          className={cn(
            'flex shrink-0 space-x-1 items-center px-2 py-1 rounded-[8px] text-foreground',
            importType === MemberSource.cvinventory &&
              'bg-msBlack text-msWhite',
          )}
          onClick={() => setImportType(MemberSource.cvinventory)}
        >
          <div
            className={cn(
              'flex justify-center items-center w-8 h-8 rounded-full',
              importType !== MemberSource.cvinventory && 'bg-msBlack',
            )}
          >
            <img width={18} src="/icons/plus.svg" alt="plus" />
          </div>
          <div className="flex flex-col items-start">
            <span className="text-smalldoge-3">Add member</span>
            {/* <span className="text-smalldoge-5">5 candidates</span> */}
          </div>
        </button>
      </div>

      {importType === MemberSource.cvinventory ? (
        <MemberProfileForm orgId={organization._id} onClose={onClose} />
      ) : importType === MemberSource.muchskills ? (
        <MuchskillsView organization={organization} />
      ) : importType === MemberSource.linkedin ? (
        <>LinkedIn import form</>
      ) : null}
    </div>
  );
}
