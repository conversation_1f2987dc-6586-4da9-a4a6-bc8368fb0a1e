import { StripePlanMapping } from 'shared/types';

export interface PlanLimits {
  name: string;
  stripeMappingId: StripePlanMapping;
  description?: string;
  profiles: number;
  maxCVs: number;
  users: number;
  aiGeneratedCVs?: boolean;
  cvDatabase?: boolean;
  skillsIntegration?: string;
  skillsSearchAndMatching?: boolean | string;
  dedicatedSupport?: boolean;
  apiIntegrations?: boolean;
  ssoSamlScim?: boolean;
  sla?: boolean;
}

export const PLAN_LIMITS: Record<StripePlanMapping, PlanLimits> = {
  [StripePlanMapping.FREE]: {
    name: 'Free Plan',
    stripeMappingId: StripePlanMapping.FREE,
    description: 'Ideal for tryouts, small agencies',
    profiles: 20,
    maxCVs: 20,
    users: 5,
    aiGeneratedCVs: true,
    cvDatabase: true,
    skillsIntegration: 'MuchSkills',
    skillsSearchAndMatching: true,
    dedicatedSupport: false,
    apiIntegrations: false,
    ssoSamlScim: false,
    sla: false,
  },
  [StripePlanMapping.PRO]: {
    name: 'Pro Plan',
    stripeMappingId: StripePlanMapping.PRO,
    description: 'Ideal for small recruiting teams or boutiques.',
    profiles: 100,
    maxCVs: 50,
    users: 5,
    aiGeneratedCVs: true,
    cvDatabase: true,
    skillsIntegration: 'MuchSkills',
    skillsSearchAndMatching: true,
    dedicatedSupport: false,
    apiIntegrations: false,
    ssoSamlScim: false,
    sla: false,
  },
  [StripePlanMapping.BUSINESS]: {
    name: 'Business Plan',
    stripeMappingId: StripePlanMapping.BUSINESS,
    description: 'Medium agencies, internal recruitment teams.',
    profiles: 500,
    maxCVs: 100,
    users: 20,
    aiGeneratedCVs: true,
    cvDatabase: true,
    skillsIntegration: 'MuchSkills',
    skillsSearchAndMatching: true,
    dedicatedSupport: false,
    apiIntegrations: true,
    ssoSamlScim: false,
    sla: false,
  },
  [StripePlanMapping.ENTERPRISE]: {
    name: 'Enterprise Plan',
    stripeMappingId: StripePlanMapping.ENTERPRISE,
    description: 'Global complex orgs, needing full control',
    profiles: Infinity,
    maxCVs: Infinity,
    users: Infinity,
    aiGeneratedCVs: true,
    cvDatabase: true,
    skillsIntegration: 'MuchSkills',
    skillsSearchAndMatching: true,
    dedicatedSupport: true,
    apiIntegrations: true,
    ssoSamlScim: true,
    sla: true,
  },
};
