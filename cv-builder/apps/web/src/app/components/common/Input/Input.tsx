import { cva, type VariantProps } from 'class-variance-authority';
import * as React from 'react';

import { ReadOnlyDisplay } from '../ReadOnlyDisplay';

import { cn } from '@/lib/utils';

const inputVariants = cva(
  'flex h-9 w-full text-smalldoge-3 rounded-md px-3 py-1 transition-colors file:border-0 file:bg-transparent placeholder:text-muted-foreground focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50',
  {
    variants: {
      variant: {
        default:
          'border border-input bg-transparent shadow-sm focus-visible:border-msBlack',
        ghost:
          'bg-transparent text-msGray-2 px-0.5 py-1 hover:bg-msGray-6 focus-visible:bg-msGray-6',
      },
      errorState: {
        true: 'border-msRed-1',
        false: '',
      },
    },
    compoundVariants: [
      {
        variant: 'ghost',
        errorState: true,
        className: 'border',
      },
    ],
    defaultVariants: {
      variant: 'default',
      errorState: false,
    },
  },
);

export interface InputProps
  extends React.InputHTMLAttributes<HTMLInputElement>,
    VariantProps<typeof inputVariants> {
  prefixElement?: React.ReactElement;
  wrapperClassName?: string;
  error?: string;
  isReadOnly?: boolean;
  errorClassName?: string;
}

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  (
    {
      className,
      type,
      prefixElement,
      wrapperClassName,
      error,
      variant,
      isReadOnly,
      errorClassName,
      ...props
    },
    ref,
  ) => {
    const errorState = !!error;

    if (isReadOnly) {
      return (
        <div className={cn(wrapperClassName)}>
          <ReadOnlyDisplay
            value={props.value?.toString()}
            placeholder={props.placeholder}
          />
          {error && (
            <span
              className={cn('text-smalldoge-4 text-msRed-1', errorClassName)}
            >
              {error}
            </span>
          )}
        </div>
      );
    }

    return (
      <div className={cn(wrapperClassName, 'relative')}>
        {prefixElement}
        <input
          type={type}
          className={cn(inputVariants({ variant, errorState, className }))}
          ref={ref}
          {...props}
        />
        {error && (
          <span className={cn('text-smalldoge-4 text-msRed-1', errorClassName)}>
            {error}
          </span>
        )}
      </div>
    );
  },
);
Input.displayName = 'Input';

export { Input, inputVariants };
