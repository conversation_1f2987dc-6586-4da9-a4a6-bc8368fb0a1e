{"updatedSections": {"sections": {"aboutMe": {"id": "689f2013d50e9ed4127d5ece", "title": "Summary", "active": true, "order": 2, "data": {"description": "<p><strong>Full-stack Ruby on Rails & React developer</strong> with a startup mindset: fast execution, ownership, and shipping value early. ~2+ years hands-on with Rails/GraphQL/REST, PostgreSQL/MySQL, Redis/Sidekiq, Docker, and CI; up to 10 months in React/Redux. Currently pursuing an MS in Computer Science.</p><ul><li><strong>Bias to action:</strong> Delivered MVPs end-to-end (APIs, data models, UI) and iterated quickly based on feedback.</li><li><strong>Product impact:</strong> Built features that reduced manual ops, improved conversion, and accelerated releases.</li><li><strong>Collaboration:</strong> Comfortable in small, cross-functional teams; clear communication with PMs/designers.</li><li><strong>Ownership:</strong> Testing (RSpec/Cypress), observability basics, and deployment to Heroku/Netlify/AWS S3.</li></ul><p><em>Looking to join an early-stage team where I can own problems, move fast, and scale product features.</em></p>"}}, "workHistory": {"id": "689f2013d50e9ed4127d5ed3", "title": "Work Experience", "active": true, "order": 3, "data": [{"id": "689f2013d50e9ed4127d5ecf", "title": "Middle Software Engineer", "companyName": "Archive.com", "description": "<p><strong>Customer:</strong> US-based Product Company | <strong>Project:</strong> AI influencer marketing platform | <strong>Role:</strong> Full Stack Rails & React</p><ul><li>Shipped React UI components from Figma to prod, cutting design-to-dev turnaround by <strong>30%</strong>.</li><li>Implemented <strong>GraphQL</strong> endpoints in Rails, reducing client round-trips and improving feed latency by <strong>~20%</strong>.</li><li>Added <strong>Cypress</strong> e2e coverage for critical flows, preventing regressions and stabilizing releases (failed deploys ↓ <strong>to near 0</strong>).</li><li>Drove sizing/estimations and fixed high-priority bugs to unblock weekly releases.</li></ul>", "startDate": "2025-05-17T11:54:59.943Z", "endDate": "2025-07-16T11:54:59.943Z", "location": "", "isCurrentJob": false}, {"id": "689f2013d50e9ed4127d5ed0", "title": "Junior Strong Software Engineer", "companyName": "Coax Software", "description": "<p><strong>Customer:</strong> UK logistics | <strong>Projects:</strong> Job Management App, Carlift (Dubai) | <strong>Role:</strong> Rails API</p><ul><li>Delivered <strong>MVP API</strong> for driver job platform in <strong>6 weeks</strong>, enabling first paid pilot.</li><li>Integrated third‑party <strong>KYC/ID verification</strong>, reducing manual review time by <strong>60%</strong>.</li><li>Diagnosed mobile API issues with on-device debugging; production crash rate ↓ <strong>35%</strong>.</li><li>Bootstrapped Bus app MVP (auth, jobs, notifications) to support early user testing.</li></ul>", "startDate": "2024-11-18T12:54:59.943Z", "endDate": "2025-05-17T11:54:59.943Z", "location": "", "isCurrentJob": false}, {"id": "689f2013d50e9ed4127d5ed1", "title": "Junior Software Engineer", "companyName": "SoftServe", "description": "<p><strong>Customer:</strong> US healthcare | <strong>Project:</strong> Canadian user registration | <strong>Role:</strong> Developer</p><ul><li>Built new registration flow and UI tweaks that increased sign‑up completion by <strong>~12%</strong>.</li><li>Wrote tech docs and <strong>GraphQL</strong> schema notes; improved onboarding time for newcomers by <strong>~25%</strong>.</li><li>Partnered with PO/QA to prioritize bugs; cycle time on critical tickets ↓ <strong>20%</strong>.</li></ul>", "startDate": "2024-03-18T12:54:59.943Z", "endDate": "2024-08-15T11:54:59.943Z", "location": "", "isCurrentJob": false}, {"id": "689f2013d50e9ed4127d5ed2", "title": "Trainee, Junior Software Engineer", "companyName": "SoftServe", "description": "<p><strong>Customer:</strong> US healthcare | <strong>Project:</strong> Messaging system | <strong>Role:</strong> Developer</p><ul><li>Implemented Rails features and refactors with <strong>RSpec</strong> coverage; defect leakage ↓ <strong>15%</strong>.</li><li>Created unit/integration tests and CI checks (<strong>Jenkins</strong>), speeding PR merge time by <strong>~20%</strong>.</li><li>Supported cross‑team integrations (REST, PubNub, Redis, Sidekiq) to unblock releases.</li></ul>", "startDate": "2023-03-19T12:54:59.943Z", "endDate": "2023-12-19T12:54:59.943Z", "location": "", "isCurrentJob": false}]}, "skills": {"id": "689f2013d50e9ed4127d5edb", "title": "Skills", "active": true, "order": 6, "data": {"categories": [{"id": "sk1", "name": "Backend", "skills": [{"name": "<PERSON>", "level": "advanced"}, {"name": "Ruby on Rails", "level": "advanced"}, {"name": "GraphQL / REST APIs", "level": "advanced"}, {"name": "PostgreSQL / MySQL", "level": "intermediate"}, {"name": "Redis / Sidekiq", "level": "intermediate"}, {"name": "Elasticsearch", "level": "intermediate"}]}, {"id": "sk2", "name": "Frontend", "skills": [{"name": "JavaScript", "level": "intermediate"}, {"name": "React", "level": "intermediate"}, {"name": "Redux Toolkit", "level": "intermediate"}, {"name": "HTML/CSS", "level": "intermediate"}]}, {"id": "sk3", "name": "DevOps & QA", "skills": [{"name": "<PERSON>er", "level": "intermediate"}, {"name": "CI/CD (Jenkins, GitHub Actions)", "level": "intermediate"}, {"name": "Heroku / Netlify / AWS S3", "level": "intermediate"}, {"name": "RSpec / Cypress / Capybara", "level": "intermediate"}]}, {"id": "sk4", "name": "Product & Collaboration", "skills": [{"name": "Agile (Scrum)", "level": "intermediate"}, {"name": "Estimation & Scoping", "level": "intermediate"}, {"name": "Documentation (Confluence/Notion)", "level": "intermediate"}]}], "description": "<p><strong>Keywords:</strong> Ruby, Ruby on Rails, GraphQL, REST, PostgreSQL, MySQL, Redis, Sidekiq, Elasticsearch, JavaScript, React, Redux Toolkit, HTML, CSS, Docker, CI/CD, Jenkins, GitHub Actions, Cypress, RSpec, Capybara, AWS S3, Heroku, Netlify, JIRA, Confluence, Notion, Git.</p>"}}, "languages": {"id": "689f2013d50e9ed4127d5edd", "title": "Languages", "active": true, "order": 7, "data": {"languages": ["Ukrainian", "English"]}}}, "customSections": [{"id": "689f2013d50e9ed4127d5edf", "title": "Hackathons & Competitions", "active": true, "order": 8, "data": {"description": "<p>INT20H 2023 – Second Place (built and pitched a functional prototype in 24h)</p>"}}, {"id": "689f2013d50e9ed4127d5ee1", "title": "Open Source Contributions", "active": true, "order": 8, "data": {"description": "<p>Roocode, Excalidraw – bug fixes, small features, and documentation improvements.</p>"}}]}, "changesSummary": "I tailored your CV for startups by: 1) Rewriting the Summary to emphasize speed, ownership, and MVP delivery. 2) Adding quantified, impact-driven bullets across roles (e.g., −35% crashes, +12% sign-up). 3) Converting Skills into ATS-friendly categories with startup-relevant keywords and deployment/testing tools. 4) Cleaning Languages to only Ukrainian and English. 5) Clarifying custom sections with concise, outcome-focused phrasing. Would you like to keep these edits?"}