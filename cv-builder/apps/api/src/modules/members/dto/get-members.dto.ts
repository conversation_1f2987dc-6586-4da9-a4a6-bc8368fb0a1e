import { Transform } from 'class-transformer';
import { IsNumber, IsOptional, IsEnum, IsString } from 'class-validator';
import { SortOrder } from 'shared/dto/common.dto';
import { GetMembersSortBy } from 'shared/dto/members/get-members.dto';
import { MemberSource } from 'shared/types';

export class GetMembersDto {
  @IsOptional()
  @IsEnum(MemberSource, { message: 'Unknown member source' })
  source?: MemberSource;

  @IsOptional()
  @IsString()
  search?: string;

  @Transform(({ value }) => parseInt(value, 10))
  @IsNumber()
  page!: number;

  @Transform(({ value }) => parseInt(value, 10))
  @IsNumber()
  itemsPerPage!: number;

  @IsOptional()
  @IsEnum(GetMembersSortBy)
  sortBy?: GetMembersSortBy = GetMembersSortBy.NAME;

  @IsOptional()
  @IsEnum(SortOrder)
  sortOrder?: SortOrder = SortOrder.ASC;
}
