#!/usr/bin/env bash

# Exit immediately if a command exits with a non-zero status.
set -e

# Print a message to indicate that migrations are starting.
echo "Running database migrations..."

# Run the database migrations using npx directly
npx migrate-mongo-ts up -f apps/api/migrate-mongo-config.ts

# Print a message to indicate that migrations have finished.
echo "Migrations finished successfully."

# Execute the command passed as arguments to the script.
# This will run the CMD from the Dockerfile.
exec "$@"