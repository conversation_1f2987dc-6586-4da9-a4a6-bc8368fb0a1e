/**
 * @format
 */

body {
  font-family: '<PERSON><PERSON>', sans-serif;
  margin: 0;
  background-color: #e5e5e5;
}

a {
  color: #527aaa;
  text-decoration: none;
}

.header {
  height: 3.5rem;
  background: #000000;
  width: 100%;
  align-items: center;
  display: flex;
  padding-left: 1.875rem;
  padding-right: 1.875rem;
  box-sizing: border-box;
}

.content_container {
  display: flex;
  flex-direction: column;
  align-items: center;
  background: #ffffff;
  padding: 3rem 1.5rem;
  width: 100%;
  max-width: 33rem;
  margin: 3.25rem auto;
  border-radius: 0.5rem;
  box-sizing: border-box;
}

.title {
  font-size: 1.75rem;
  line-height: 130%;
  font-weight: 900;
  margin-top: 3.5rem;
}
.description {
  font-size: 1.125rem;
  line-height: 135%;
  font-weight: 400;
  margin-top: 1rem;
}

.text_highlight {
  font-weight: 700;
  color: #45b68d;
}

@media (max-width: 576px) {
  .content_container {
    width: 100%;
    max-width: 100%;
    border-radius: 0;
  }
}
