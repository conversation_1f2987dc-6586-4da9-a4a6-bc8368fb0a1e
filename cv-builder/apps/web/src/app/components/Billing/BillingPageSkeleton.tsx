import { Skeleton } from '@/components/common/Skeleton';

export function BillingPageSkeleton() {
  return (
    <div className="w-full md:max-w-[600px] mx-auto py-6 px-2 flex flex-col gap-6">
      <div className="flex flex-col gap-2">
        {/* Header Skeleton */}
        <div className="flex items-center justify-between">
          <Skeleton className="w-1/3 h-10" /> {/* "Billing" title */}
          <div className="flex items-center gap-4">
            <Skeleton className="w-32 h-10" /> {/* Manage Billing Button */}
            <Skeleton className="w-24 h-10" /> {/* Currency Selector */}
            <Skeleton className="w-32 h-10" /> {/* Interval Toggle Button */}
          </div>
        </div>
      </div>

      {/* Skeleton for available plans list */}
      {/* This structure should also align with how PlansList renders multiple PlanCard items if needed */}
      <div className="flex flex-col gap-2">
        <Skeleton className="w-1/3 h-8" /> {/* Title like "Available Plans" */}
        <div className="flex flex-col gap-6">
          {/* Simulating multiple PlanCardSkeletons as they would appear in PlansList */}
          {[1, 2, 3].map((i) => (
            <div
              key={`available-${i}`}
              className={`flex flex-col md:flex-row justify-between items-start gap-4`}
            >
              <div className="flex flex-col flex-1 gap-1">
                <Skeleton className="w-1/2 h-8" /> {/* Plan Name */}
                <Skeleton className="w-1/4 h-6" /> {/* Plan Price */}
                <Skeleton className="w-3/4 h-4" /> {/* Plan Description */}
                <Skeleton className="w-1/3 h-10 mt-2" /> {/* Action Button */}
              </div>
              <div className="flex flex-col flex-1 gap-2 md:max-w-1/2">
                <div className="flex items-center justify-between">
                  <Skeleton className="w-2/3 h-4" />
                  <Skeleton className="w-1/4 h-4" />
                </div>
                <div className="flex items-center justify-between">
                  <Skeleton className="w-3/4 h-4" />
                  <Skeleton className="w-1/5 h-4" />
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
