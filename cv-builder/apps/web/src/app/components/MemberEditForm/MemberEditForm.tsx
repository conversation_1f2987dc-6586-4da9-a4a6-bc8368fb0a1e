import { useNavigate } from 'react-router-dom';
import { Member, MemberSource, memberSourceData } from 'shared/types';

import { MemberProfileForm } from '../../components';
import { ButtonSecondary, MemberAvatarWithSource } from '../common';

import { NAVIGATE_PATH } from '@/helpers/constants';

interface MemberEditFormProps {
  orgId: string;
  member: Member;
  onClose: () => void;
}

export function MemberEditForm({
  orgId,
  member,
  onClose,
}: MemberEditFormProps) {
  const navigate = useNavigate();

  return (
    <div className="flex flex-col h-full">
      <div className="mb-5">
        <div className="flex flex-col sm:flex-row sm:items-center mb-2">
          <div className="flex items-center">
            <MemberAvatarWithSource member={member} />
            <div className="flex flex-col ml-2">
              <span
                className="font-bold text-smalldoge-3 max-w-[180px] sm:max-w-sm truncate"
                title={`${member.firstName} ${member.lastName}`}
              >
                {`${member.firstName} ${member.lastName}`}
              </span>
              {member.source !== MemberSource.cvinventory && (
                <span className="truncate text-smalldoge-5 text-msGray-2">
                  Skills profile synced to{' '}
                  {memberSourceData[member.source].name}
                </span>
              )}
            </div>
          </div>
          <ButtonSecondary
            text="normal"
            className="mt-2 sm:mt-0 sm:ml-auto"
            onClick={() => navigate(`${NAVIGATE_PATH.cvList}/${member._id}`)}
          >
            VIEW ALL CVs
          </ButtonSecondary>
        </div>
        {member.source === MemberSource.muchskills && (
          <div className="italic text-smalldoge-3 font-bold text-msGray-3">
            This profile is managed through your HR system. To update basic
            details, please make the changes in your HRIS tool, then resync
            members to pull updated data.
          </div>
        )}
      </div>
      <MemberProfileForm orgId={orgId} member={member} onClose={onClose} />
    </div>
  );
}
