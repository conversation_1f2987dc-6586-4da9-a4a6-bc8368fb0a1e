import { cn } from '@/lib/utils';

interface CustomerBadgeProps {
  name?: string;
  className?: string;
  onClick?: (e: React.MouseEvent) => void;
}

export function CustomerBadge({
  name,
  className,
  onClick,
}: CustomerBadgeProps) {
  const hasName = !!name;

  return (
    <div
      className={cn(
        'w-fit bg-msGray-6 rounded-[100px] px-2 truncate min-w-0 text-msBlue-1 text-smalldoge-3',
        className,
        onClick && hasName && 'cursor-pointer hover:bg-msGray-5',
      )}
      onClick={hasName ? onClick : undefined}
      title={name || 'No Customer'}
    >
      {name || 'No Customer'}
    </div>
  );
}
