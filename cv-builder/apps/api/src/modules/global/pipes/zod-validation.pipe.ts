import {
  PipeTransform,
  ArgumentMetadata,
  BadRequestException,
} from '@nestjs/common';
import { ZodSchema, ZodError } from 'zod';

export class ZodValidationPipe implements PipeTransform {
  constructor(private schema: ZodSchema) {}

  transform(value: unknown, metadata: ArgumentMetadata) {
    // Only validate body parameters
    if (metadata.type !== 'body') {
      return value;
    }

    try {
      const parsedValue = this.schema.parse(value);
      return parsedValue;
    } catch (error) {
      if (error instanceof ZodError) {
        // Extract validation messages in the same format as class-validator
        const messages = error.issues.map((issue) => issue.message);
        throw new BadRequestException(messages);
      }

      // For non-Zod errors, throw a generic validation error
      throw new BadRequestException('Validation failed');
    }
  }
}
