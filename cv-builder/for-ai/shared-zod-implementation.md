# Shared Zod Validation Schema Implementation

## Overview
Successfully implemented shared Zod validation schemas between frontend and backend for the invite functionality, replacing the previous class-validator approach with a unified validation system.

## What Was Implemented

### 1. Shared Schema Structure
- **Created**: `cv-builder/apps/shared/schemas/` directory
- **Added**: `invite.schema.ts` with shared validation schema
- **Added**: `index.ts` for easy imports

### 2. Shared Schema (`shared/schemas/invite.schema.ts`)
```typescript
import { z } from 'zod';
import { UserRole } from '../types';

export const createInviteSchema = z.object({
  email: z.string().email('Please enter a valid email address'),
  name: z
    .string()
    .min(2, 'Name must be at least 2 characters')
    .max(100, 'Name must be no more than 100 characters')
    .regex(
      /^[\p{L}\s'-]+$/u,
      'Name can only contain letters, spaces, apostrophes, and dashes',
    )
    .optional()
    .or(z.literal('')),
  role: z.nativeEnum(UserRole),
});

export type CreateInviteInput = z.infer<typeof createInviteSchema>;
```

### 3. Backend Changes
- **Created**: Generic `ZodValidationPipe` at `cv-builder/apps/api/src/modules/global/pipes/zod-validation.pipe.ts`
- **Updated**: `invite.controller.ts` to use `@UsePipes(new ZodValidationPipe(createInviteSchema))`
- **Updated**: `invite.service.ts` to use `CreateInviteInput` type
- **Removed**: Old `CreateInviteDto` class-validator based DTO

### 4. Frontend Changes
- **Updated**: `InviteUserDrawer.tsx` to import and use shared schema
- **Removed**: Inline Zod schema definition
- **Updated**: All type references from `InviteFormData` to `CreateInviteInput`

## Benefits Achieved

1. **Single Source of Truth**: One schema defines validation for both FE and BE
2. **Type Safety**: Automatically generated TypeScript types from Zod schema
3. **Consistency**: Identical validation rules and error messages across the stack
4. **Maintainability**: Changes to validation logic only need to be made in one place
5. **Reusability**: Generic `ZodValidationPipe` can be used for other endpoints

## Technical Details

### TypeScript Configuration
- ✅ `strictNullChecks` already enabled via `"strict": true` in both API and web tsconfig
- ✅ Zod dependency already present in package.json (v3.24.4)

### File Structure
```
cv-builder/apps/shared/schemas/
├── index.ts
└── invite.schema.ts
```

### Generic Validation Pipe
The `ZodValidationPipe` follows NestJS best practices and can be reused across all endpoints. It only validates `@Body()` parameters, leaving other decorators like `@AuthUser()`, `@Param()`, etc. untouched. It also provides detailed error messages for validation failures:

```typescript
export class ZodValidationPipe implements PipeTransform {
  constructor(private schema: ZodSchema) {}
  
  transform(value: unknown, metadata: ArgumentMetadata) {
    // Only validate body parameters
    if (metadata.type !== 'body') {
      return value;
    }

    try {
      const parsedValue = this.schema.parse(value);
      return parsedValue;
    } catch (error) {
      if (error instanceof ZodError) {
        // Extract validation messages in the same format as class-validator
        const messages = error.issues.map((issue) => issue.message);
        throw new BadRequestException(messages);
      }
      
      // For non-Zod errors, throw a generic validation error
      throw new BadRequestException('Validation failed');
    }
  }
}
```

#### Error Response Format
When validation fails, the API returns an error response in the same format as class-validator:
```json
{
  "message": [
    "Please enter a valid email address",
    "Name can only contain letters, spaces, apostrophes, and dashes",
    "Invalid enum value. Expected 'OWNER' | 'ADMIN' | 'MEMBER', received 'ASDF'"
  ],
  "error": "Bad Request",
  "statusCode": 400
}
```

## Future Migration Path
This implementation provides a template for migrating other DTOs to shared Zod schemas:

1. Create schema in `shared/schemas/`
2. Export from `shared/schemas/index.ts`
3. Update controller to use `@UsePipes(new ZodValidationPipe(schema))`
4. Update service to use inferred type
5. Update frontend to use shared schema
6. Remove old DTO file

## Status
✅ **Complete** - Ready for testing and deployment