import { useMutation, useQueryClient } from '@tanstack/react-query';
import { AxiosError } from 'axios';
import { useState } from 'react';
import { ApiErrorResponse } from 'shared/types';
import { toast } from 'sonner';

import {
  ButtonSecondary,
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  PasswordInput,
} from '@/components/common';
import { SettingsBadge } from '@/components/Settings/Badge/Badge';
import { useAuth, OrganizationData } from '@/contexts/AuthContext';
import { connectToMuchskills, disconnectMuchskills } from '@/helpers/requests';

interface MuchskillsFormProps {
  organization: OrganizationData;
}

export function MuchskillsForm({ organization }: MuchskillsFormProps) {
  const queryClient = useQueryClient();
  const { refreshUserData, canUpdateOrganization } = useAuth();
  const [token, setToken] = useState<string>('');
  const [errorDialogActive, setErrorDialogActive] = useState<boolean>(false);

  const { mutate: updateToken, isPending: pendingUpdateToken } = useMutation({
    mutationFn: () => connectToMuchskills(organization._id, token),
    onSuccess: async () => {
      queryClient.invalidateQueries({ queryKey: ['organization'] });
      await refreshUserData();
      setToken('');
    },
    onError: (err) => {
      const error = err as AxiosError<ApiErrorResponse>;
      if (error.response?.data?.statusCode === 409) {
        setErrorDialogActive(true);
        setToken('');
        return;
      }

      toast.error(error.response?.data?.message);
    },
  });

  const { mutate: disconnectMuchskillsIntegration } = useMutation({
    mutationFn: () => disconnectMuchskills(organization._id),
    onSuccess: async () => {
      queryClient.invalidateQueries({
        queryKey: ['organizationFullInfo'],
      });
      await refreshUserData();
      setToken('');
    },
  });

  return (
    <>
      <div>
        <div className="flex items-center">
          <img
            width={24}
            src="/images/muchskills-logo-purple.png"
            alt="muchskills_logo"
          />
          <b className="ml-2 text-smalldoge-1">MuchSkills</b>
          <div className="ml-auto">
            {!organization?.muchskillsIntegration ? (
              <SettingsBadge variant="neutral">Not Connected</SettingsBadge>
            ) : organization.muchskillsIntegration.connected ? (
              <SettingsBadge variant="default">Connected</SettingsBadge>
            ) : (
              <SettingsBadge variant="destructive">Invalid Token</SettingsBadge>
            )}
          </div>
        </div>
        <span className="block mt-4 text-smalldoge-4">
          Connect your MuchSkills skills inventory and keep your skills profiles
          up to date for more accurate CV generation.
        </span>
        <div className="flex flex-col py-4 mt-4 border-t border-msGray-5">
          <div className="flex flex-col items-start mb-4 space-y-1 sm:flex-row sm:items-center sm:space-x-1 sm:space-y-0">
            <b className="flex-shrink-0 w-40 font-bold text-smalldoge-3 text-msGray-3">
              API Token
            </b>
            <PasswordInput
              value={token}
              placeholder={
                organization.muchskillsIntegration?.token || 'Your API Token'
              }
              className="text-smalldoge-3"
              wrapperClassName="flex-grow w-full"
              onChange={(e) => setToken(e.target.value)}
              disabled={!canUpdateOrganization}
            />
          </div>
          <div className="flex ml-auto space-x-2">
            {canUpdateOrganization ? (
              <>
                {organization.muchskillsIntegration &&
                  organization.muchskillsIntegration.connected && (
                    <ButtonSecondary
                      className="ml-auto"
                      onClick={() => disconnectMuchskillsIntegration()}
                    >
                      Disconnect
                    </ButtonSecondary>
                  )}
                <ButtonSecondary
                  className="ml-auto"
                  disabled={pendingUpdateToken || !token.length}
                  onClick={() => updateToken()}
                >
                  {organization.muchskillsIntegration
                    ? organization.muchskillsIntegration.connected
                      ? 'Set New Token'
                      : 'Reconnect'
                    : 'Set Token'}
                </ButtonSecondary>
              </>
            ) : (
              <p className="mb-4 text-smalldoge-3 text-msRed-1">
                Only Owners and Admins can manage integrations.
              </p>
            )}
          </div>
        </div>
      </div>

      <Dialog open={errorDialogActive} onOpenChange={setErrorDialogActive}>
        <DialogContent className="prevent-drawer-outside-click">
          <DialogHeader>
            <DialogTitle>Cannot Connect</DialogTitle>
            <DialogDescription>
              This MuchSkills token is already linked to another organization.
              If you’re not sure why, please reach out to our{' '}
              <a
                href="mailto:<EMAIL>"
                className="font-bold text-msBlue-1"
              >
                support team
              </a>
              .
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <ButtonSecondary onClick={() => setErrorDialogActive(false)}>
              Dismiss
            </ButtonSecondary>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
