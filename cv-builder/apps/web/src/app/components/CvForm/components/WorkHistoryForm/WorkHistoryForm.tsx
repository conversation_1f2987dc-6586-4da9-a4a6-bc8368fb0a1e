import { cloneDeep } from 'lodash';
import { memo } from 'react';
import { SectionData, WorkHistoryDataItem } from 'shared/types';

import { WorkHistoryItem } from './components';
import { ButtonSecondary } from '../../../common';

import { FormReducerAction, ReducerActionType } from '@/pages/CVEditPage';

export const emptyWorkHistoryRecord: WorkHistoryDataItem = {
  active: true,
  companyName: '',
  roleTitle: '',
  description: '',
  startDate: new Date(),
  endDate: undefined,
  isCurrent: false,
};

interface WorkHistoryFormProps {
  sectionId: string;
  data: WorkHistoryDataItem[];
  readonly?: boolean;
  onDataChange: React.Dispatch<FormReducerAction>;
}

export const WorkHistoryForm = memo(
  ({ sectionId, data, readonly, onDataChange }: WorkHistoryFormProps) => {
    function updateHistoryRecord(
      index: number,
      updatedHistoryRecord: WorkHistoryDataItem,
    ) {
      const tempData = cloneDeep(data);
      tempData[index] = updatedHistoryRecord;

      handleChange(tempData);
    }

    function handleChange(data: SectionData) {
      onDataChange({
        type: ReducerActionType.updateField,
        sectionId: sectionId,
        data,
      });
    }

    function handleCreateRecord() {
      handleChange(data.concat(emptyWorkHistoryRecord));
    }

    if (!data.length) {
      return (
        <div className="flex flex-col space-y-2 items-center justify-center border border-dashed rounded-[8px] px-2 py-6">
          <img src="/images/notes.svg" alt="notes" />
          <span className="text-smalldoge-3">
            There are no work experience records yet
          </span>
          <ButtonSecondary disabled={readonly} onClick={handleCreateRecord}>
            Add new record
          </ButtonSecondary>
        </div>
      );
    }

    return (
      <div>
        <div className="flex flex-col p-px space-y-5">
          {data.map((historyRecord, i) => (
            <WorkHistoryItem
              key={i}
              data={historyRecord}
              dropDownItems={[
                {
                  title: 'Remove',
                  onClick: () => {
                    const clonedData = cloneDeep(data);
                    clonedData.splice(i, 1);

                    handleChange(clonedData);
                  },
                },
              ]}
              readonly={readonly}
              onChange={(val) => updateHistoryRecord(i, val)}
            />
          ))}
        </div>
        <ButtonSecondary
          disabled={readonly}
          className="mt-4"
          onClick={handleCreateRecord}
        >
          Add new record
        </ButtonSecondary>
      </div>
    );
  },
);
