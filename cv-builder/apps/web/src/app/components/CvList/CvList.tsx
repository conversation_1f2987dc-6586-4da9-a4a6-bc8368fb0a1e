import { FileUser } from 'lucide-react';
import { Cv } from 'shared/types';

import { CVListTab } from '../../components';
import { Loader } from '../common';

interface CvListProps {
  cvs?: Cv[];
  activeCv?: string | null;
  loading: boolean;
  onCvEdit: (cvId: string) => void;
  onCvDelete: (cvId: string) => void;
  onCvDuplicate: (cvId: string) => void;
  onCvClick: (cvId: string) => void;
  onCreateNewCv: () => void;
}

export function CvList({
  cvs,
  activeCv,
  loading,
  onCvEdit,
  onCvDelete,
  onCvDuplicate,
  onCvClick,
  onCreateNewCv,
}: CvListProps) {
  if (loading) {
    return (
      <div className="flex justify-center w-full h-full py-20 overflow-auto">
        <Loader size={60} />
      </div>
    );
  }

  if (!cvs?.length) {
    return (
      <div className="flex flex-col h-full overflow-auto">
        <div className="flex flex-col h-full p-6">
          <span className="mb-2 font-bold text-smalldoge-4">
            Member has no CVs yet.
          </span>
          <div
            className="h-32 flex flex-col space-y-2 items-center justify-center border border-msGray-5 rounded-[8px] text-msGray-4 hover:border-msBlue-2 hover:text-msBlue-2 hover:bg-msBlue-4 transition-all cursor-pointer"
            onClick={onCreateNewCv}
          >
            <FileUser size={32} />
            <span className="font-bold uppercase text-smalldoge-4">
              +New CV
            </span>
          </div>
          <img
            className="mt-auto"
            width={200}
            src="images/explorers-on-boat.svg"
            alt="placeholder"
          />
        </div>
      </div>
    );
  }
  return (
    <div className="flex flex-col h-full overflow-auto">
      {cvs.map((cv) => (
        <CVListTab
          key={cv._id}
          active={activeCv === cv._id}
          cv={cv}
          onClick={() => onCvClick(cv._id)}
          onEdit={() => onCvEdit(cv._id)}
          onDelete={() => onCvDelete(cv._id)}
          onDuplicate={() => onCvDuplicate(cv._id)}
        />
      ))}
    </div>
  );
}
