1. Make your UI changes. 2. Run yarn test to see the visual differences. 3. Review the diff in the Playwright report. 4. If the changes are intentional and correct, run yarn test:update-snapshots to update your golden snapshots.

If you only want to approve some of the snapshot changes, you
have a few options:

1.  Update snapshots for a specific test file:
    You can run the update command on just the file with the changes you want to keep.

1 npx playwright test your-test-file.spec.ts --update-snapshots

2.  Update snapshots for a specific test:
    You can use the test title to target a specific test.

1 npx playwright test -g "The test title you want to update"
--update-snapshots

3.  Use the Interactive UI (Recommended):
    This is the easiest way to handle this. Run Playwright in UI mode:
    1 npx playwright test --ui
    This will open a browser window where you can see all your tests. You can then
    look at the failed tests, see the visual diff, and click a button to accept the new
    snapshot for each test individually.
