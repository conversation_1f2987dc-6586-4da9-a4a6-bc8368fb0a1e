# Understanding Validation Groups in `class-validator`

`class-validator` is a powerful library for object validation in TypeScript/JavaScript applications, especially common in frameworks like NestJS. One of its most useful features is **Validation Groups**.

## What are Validation Groups?

Validation groups allow you to apply different validation rules to the same object (DTO) based on a specific context or scenario. By default, `class-validator` validates all decorators on a DTO. However, sometimes you need to validate only a subset of properties.

For example, when creating a user, you might require a `password` field, but when updating a user's profile, you might not want to validate the `password` field at all, or you might have different rules for it.

## Why are they useful? (Use Cases)

1.  **Create vs. Update Operations:** This is the most common use case.
    *   **Create:** A user must provide a password. The `id` should not be present.
    *   **Update:** A user might update their `email` or `name`, but the `password` is optional. The `id` must be a valid identifier.

2.  **Multi-Step Forms (Wizards):** In a multi-step registration process, you can validate each step's data independently using different groups for each step.

3.  **Role-Based Validation:** You might have different validation rules based on the user's role (e.g., an `admin` can update more fields than a regular `user`).

## How to Use Validation Groups

Using validation groups involves two main steps:
1.  **Assigning groups to validation decorators** in your DTO.
2.  **Specifying which group(s) to use** when performing the validation.

### Step 1: Defining Groups in a DTO

You assign a property to one or more groups by passing a `groups` array in the validation options. A group is just a simple string.

Let's create a `UserDto.ts` to illustrate. We'll define two groups: `CREATE` and `UPDATE`.

```typescript
// user.dto.ts
import { IsEmail, IsNotEmpty, IsOptional, IsString, MinLength } from 'class-validator';

export const CREATE_USER_GROUP = 'create';
export const UPDATE_USER_GROUP = 'update';

export class UserDto {
  @IsString({ groups: [UPDATE_USER_GROUP] }) // Only validate 'id' on update
  @IsNotEmpty({ groups: [UPDATE_USER_GROUP] })
  id: string;

  @IsString()
  @IsNotEmpty() // No group means it runs for ALL validations (unless a group is specified)
  firstName: string;

  @IsString()
  @IsNotEmpty()
  lastName: string;

  @IsEmail({}, { groups: [CREATE_USER_GROUP, UPDATE_USER_GROUP] }) // Validate email in both cases
  @IsNotEmpty({ groups: [CREATE_USER_GROUP, UPDATE_USER_GROUP] })
  email: string;

  @IsString({ groups: [CREATE_USER_GROUP] }) // Only validate 'password' on create
  @IsNotEmpty({ groups: [CREATE_USER_GROUP] })
  @MinLength(8, { groups: [CREATE_USER_GROUP] })
  password: string;

  @IsString({ groups: [UPDATE_USER_GROUP] }) // Only validate 'role' on update
  @IsOptional({ groups: [UPDATE_USER_GROUP] })
  role: string;
}
```

**Key Points from the DTO:**
*   `firstName` and `lastName` have no `groups` option, so they will **only be validated if no group is specified** during the validation call. If you want a property to be validated for a specific group *and* in the default case, you must explicitly add it to the groups. A decorator without a group is considered part of the `'default'` group.
*   `email` is in both `CREATE` and `UPDATE` groups.
*   `password` is only required for the `CREATE` group.
*   `id` is only required for the `UPDATE` group.

A better way to handle properties that should always be validated is to make them part of every group.

**Revised `UserDto.ts` for clarity:**

```typescript
// user.dto.ts
import { IsEmail, IsNotEmpty, IsOptional, IsString, MinLength } from 'class-validator';

export const CREATE_USER_GROUP = 'create';
export const UPDATE_USER_GROUP = 'update';

export class UserDto {
  @IsString({ groups: [UPDATE_USER_GROUP] })
  id: string;

  @IsString({ groups: [CREATE_USER_GROUP, UPDATE_USER_GROUP] })
  firstName: string;

  @IsString({ groups: [CREATE_USER_GROUP, UPDATE_USER_GROUP] })
  lastName: string;

  @IsEmail({}, { groups: [CREATE_USER_GROUP, UPDATE_USER_GROUP] })
  email: string;

  @IsString({ groups: [CREATE_USER_GROUP] })
  @MinLength(8, { groups: [CREATE_USER_GROUP] })
  password: string;
}
```
In this revised version, `firstName`, `lastName`, and `email` will be validated for both `create` and `update` actions.

### Step 2: Applying Validation with Groups

In a NestJS application, you can specify the validation group directly in your controller using a `ValidationPipe`.

```typescript
// users.controller.ts
import { Controller, Post, Body, Put, Param, ValidationPipe, UsePipes } from '@nestjs/common';
import { UserDto, CREATE_USER_GROUP, UPDATE_USER_GROUP } from './user.dto';

@Controller('users')
export class UsersController {

  @Post()
  @UsePipes(new ValidationPipe({ groups: [CREATE_USER_GROUP], whitelist: true }))
  createUser(@Body() userDto: UserDto) {
    // Logic to create a user
    // At this point, userDto is validated against the 'create' group.
    // 'password' is required, 'id' is stripped out.
    return 'User created';
  }

  @Put(':id')
  @UsePipes(new ValidationPipe({ groups: [UPDATE_USER_GROUP], whitelist: true }))
  updateUser(@Param('id') id: string, @Body() userDto: UserDto) {
    // Logic to update a user
    // At this point, userDto is validated against the 'update' group.
    // 'password' is ignored, 'id' is allowed (and would typically be set from the param).
    return `User ${id} updated`;
  }
}
```

**Explanation:**
*   In `createUser`, we use `new ValidationPipe({ groups: [CREATE_USER_GROUP] })`. This tells `class-validator` to only run the validation decorators that belong to the `CREATE_USER_GROUP`.
*   In `updateUser`, we use `new ValidationPipe({ groups: [UPDATE_USER_GROUP] })`. This validates the DTO against the `UPDATE_USER_GROUP`.
*   `whitelist: true` is a good practice to automatically remove any properties from the DTO that do not have any validation decorators in the specified group.

### Validation without Groups

If you call the `ValidationPipe` without specifying any groups, it will only run validators that do **not** have any group assigned to them. This is the "default" behavior. If all your validators have groups, then nothing will be validated by default.

### Conclusion

Validation groups are an essential feature for building robust and maintainable APIs. They allow you to reuse the same DTO for different purposes (like creating and updating resources) while applying distinct validation rules for each scenario, keeping your code clean and DRY (Don't Repeat Yourself).
