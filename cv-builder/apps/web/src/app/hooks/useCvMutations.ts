import {
  useMutation,
  useQueryClient,
  QueryClient,
} from '@tanstack/react-query';
import { Cv, OrganizationFullInfo, CvStatus } from 'shared/types';

import { deleteCvRequest, duplicateCvRequest } from '@/helpers/requests';

/**
 * Updates the updatedAt timestamp for a draft CV in the draftCvs cache
 */
export const updateDraftCvTimestamp = (
  queryClient: QueryClient,
  cvId: string,
  cvStatus: CvStatus,
) => {
  if (cvStatus === CvStatus.draft) {
    queryClient.setQueriesData(
      { queryKey: ['draftCvs'] },
      (oldData: { cvs: Cv[]; total: number } | undefined) => {
        if (!oldData?.cvs) return oldData;
        return {
          ...oldData,
          cvs: oldData.cvs.map((draftCv: Cv) => {
            if (draftCv._id === cvId) {
              console.log('Updating draft CV timestamp for CV ID:', cvId);
              return { ...draftCv, updatedAt: new Date() };
            }
            return draftCv;
          }),
        };
      },
    );
  }
};

interface UseDuplicateCvOptions {
  onSuccess?: () => void;
}

export const useDuplicateCv = ({ onSuccess }: UseDuplicateCvOptions = {}) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ cvId, memberId }: { cvId: string; memberId: string }) =>
      duplicateCvRequest(cvId),
    onSuccess: (data, { memberId }) => {
      // Always invalidate both memberCvs and draftCvs queries
      queryClient.invalidateQueries({
        queryKey: ['memberCvs', { memberId }],
      });
      queryClient.invalidateQueries({ queryKey: ['draftCvs'] });

      // Update organization full info to increment totalCvs count
      queryClient.setQueryData(
        ['organizationFullInfo'],
        (oldData: OrganizationFullInfo | undefined) => {
          if (!oldData) return oldData;
          return { ...oldData, totalCvs: oldData.totalCvs + 1 };
        },
      );

      // Invalidate paginated members data to refetch updated CV data
      queryClient.invalidateQueries({
        queryKey: ['paginatedMembers'],
      });

      onSuccess?.();
    },
  });
};

interface UseDeleteCvOptions {
  onCvRemoved?: () => void;
  onSuccess?: (
    data: any,
    variables: { cvId: string; memberId: string },
  ) => void;
}

export const useDeleteCv = ({
  onCvRemoved,
  onSuccess,
}: UseDeleteCvOptions = {}) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ cvId, memberId }: { cvId: string; memberId: string }) =>
      deleteCvRequest(cvId),
    onSuccess: (data, { cvId, memberId }) => {
      onCvRemoved?.();

      // Update memberCvs cache
      queryClient.setQueryData(['memberCvs', { memberId }], (oldData: Cv[]) => {
        if (!oldData) return oldData;
        return oldData.filter((cv) => cv._id !== cvId);
      });

      // Always invalidate both memberCvs and draftCvs queries
      queryClient.invalidateQueries({
        queryKey: ['memberCvs', { memberId }],
      });
      queryClient.invalidateQueries({ queryKey: ['draftCvs'] });

      // Update organization full info to decrement totalCvs count
      queryClient.setQueryData(
        ['organizationFullInfo'],
        (oldData: OrganizationFullInfo | undefined) => {
          if (!oldData) return oldData;
          return { ...oldData, totalCvs: Math.max(0, oldData.totalCvs - 1) };
        },
      );

      // Invalidate paginated members data to refetch updated CV data
      queryClient.invalidateQueries({
        queryKey: ['paginatedMembers'],
      });

      onSuccess?.(data, { cvId, memberId });
    },
  });
};
