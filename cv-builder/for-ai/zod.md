# Zod documentation from nestjs documentation

zod library requires the strictNullChecks configuration to be enabled in your tsconfig.json file.

Pipes
A pipe is a class annotated with the @Injectable() decorator, which implements the PipeTransform interface.

Pipes have two typical use cases:

transformation: transform input data to the desired form (e.g., from string to integer)
validation: evaluate input data and if valid, simply pass it through unchanged; otherwise, throw an exception
In both cases, pipes operate on the arguments being processed by a controller route handler. Nest interposes a pipe just before a method is invoked, and the pipe receives the arguments destined for the method and operates on them. Any transformation or validation operation takes place at that time, after which the route handler is invoked with any (potentially) transformed arguments.

Nest comes with a number of built-in pipes that you can use out-of-the-box. You can also build your own custom pipes. In this chapter, we'll introduce the built-in pipes and show how to bind them to route handlers. We'll then examine several custom-built pipes to show how you can build one from scratch.

Hint
Pipes run inside the exceptions zone. This means that when a Pipe throws an exception it is handled by the exceptions layer (global exceptions filter and any exceptions filters that are applied to the current context). Given the above, it should be clear that when an exception is thrown in a Pipe, no controller method is subsequently executed. This gives you a best-practice technique for validating data coming into the application from external sources at the system boundary.
Built-in pipes#
Nest comes with several pipes available out-of-the-box:

ValidationPipe
ParseIntPipe
ParseFloatPipe
ParseBoolPipe
ParseArrayPipe
ParseUUIDPipe
ParseEnumPipe
DefaultValuePipe
ParseFilePipe
ParseDatePipe
They're exported from the @nestjs/common package.

Let's take a quick look at using ParseIntPipe. This is an example of the transformation use case, where the pipe ensures that a method handler parameter is converted to a JavaScript integer (or throws an exception if the conversion fails). Later in this chapter, we'll show a simple custom implementation for a ParseIntPipe. The example techniques below also apply to the other built-in transformation pipes (ParseBoolPipe, ParseFloatPipe, ParseEnumPipe, ParseArrayPipe, ParseDatePipe, and ParseUUIDPipe, which we'll refer to as the Parse\* pipes in this chapter).

Binding pipes#
To use a pipe, we need to bind an instance of the pipe class to the appropriate context. In our ParseIntPipe example, we want to associate the pipe with a particular route handler method, and make sure it runs before the method is called. We do so with the following construct, which we'll refer to as binding the pipe at the method parameter level:

@Get(':id')
async findOne(@Param('id', ParseIntPipe) id: number) {
return this.catsService.findOne(id);
}
This ensures that one of the following two conditions is true: either the parameter we receive in the findOne() method is a number (as expected in our call to this.catsService.findOne()), or an exception is thrown before the route handler is called.

For example, assume the route is called like:

GET localhost:3000/abc
Nest will throw an exception like this:

{
"statusCode": 400,
"message": "Validation failed (numeric string is expected)",
"error": "Bad Request"
}
The exception will prevent the body of the findOne() method from executing.

In the example above, we pass a class (ParseIntPipe), not an instance, leaving responsibility for instantiation to the framework and enabling dependency injection. As with pipes and guards, we can instead pass an in-place instance. Passing an in-place instance is useful if we want to customize the built-in pipe's behavior by passing options:

@Get(':id')
async findOne(
@Param('id', new ParseIntPipe({ errorHttpStatusCode: HttpStatus.NOT_ACCEPTABLE }))
id: number,
) {
return this.catsService.findOne(id);
}
Binding the other transformation pipes (all of the Parse\* pipes) works similarly. These pipes all work in the context of validating route parameters, query string parameters and request body values.

For example with a query string parameter:

@Get()
async findOne(@Query('id', ParseIntPipe) id: number) {
return this.catsService.findOne(id);
}
Here's an example of using the ParseUUIDPipe to parse a string parameter and validate if it is a UUID.

JS

@Get(':uuid')
async findOne(@Param('uuid', new ParseUUIDPipe()) uuid: string) {
return this.catsService.findOne(uuid);
}
Hint
When using ParseUUIDPipe() you are parsing UUID in version 3, 4 or 5, if you only require a specific version of UUID you can pass a version in the pipe options.
Above we've seen examples of binding the various Parse\* family of built-in pipes. Binding validation pipes is a little bit different; we'll discuss that in the following section.

Hint
Also, see Validation techniques for extensive examples of validation pipes.
Custom pipes#
As mentioned, you can build your own custom pipes. While Nest provides a robust built-in ParseIntPipe and ValidationPipe, let's build simple custom versions of each from scratch to see how custom pipes are constructed.

We start with a simple ValidationPipe. Initially, we'll have it simply take an input value and immediately return the same value, behaving like an identity function.

validation.pipe.tsJS

import { PipeTransform, Injectable, ArgumentMetadata } from '@nestjs/common';

@Injectable()
export class ValidationPipe implements PipeTransform {
transform(value: any, metadata: ArgumentMetadata) {
return value;
}
}
Hint
PipeTransform<T, R> is a generic interface that must be implemented by any pipe. The generic interface uses T to indicate the type of the input value, and R to indicate the return type of the transform() method.
Every pipe must implement the transform() method to fulfill the PipeTransform interface contract. This method has two parameters:

value
metadata
The value parameter is the currently processed method argument (before it is received by the route handling method), and metadata is the currently processed method argument's metadata. The metadata object has these properties:

export interface ArgumentMetadata {
type: 'body' | 'query' | 'param' | 'custom';
metatype?: Type<unknown>;
data?: string;
}
These properties describe the currently processed argument.

type Indicates whether the argument is a body @Body(), query @Query(), param @Param(), or a custom parameter (read more here).
metatype Provides the metatype of the argument, for example, String. Note: the value is undefined if you either omit a type declaration in the route handler method signature, or use vanilla JavaScript.
data The string passed to the decorator, for example @Body('string'). It's undefined if you leave the decorator parenthesis empty.
Warning
TypeScript interfaces disappear during transpilation. Thus, if a method parameter's type is declared as an interface instead of a class, the metatype value will be Object.
Schema based validation#
Let's make our validation pipe a little more useful. Take a closer look at the create() method of the CatsController, where we probably would like to ensure that the post body object is valid before attempting to run our service method.

JS

@Post()
async create(@Body() createCatDto: CreateCatDto) {
this.catsService.create(createCatDto);
}
Let's focus in on the createCatDto body parameter. Its type is CreateCatDto:

create-cat.dto.tsJS

export class CreateCatDto {
name: string;
age: number;
breed: string;
}
We want to ensure that any incoming request to the create method contains a valid body. So we have to validate the three members of the createCatDto object. We could do this inside the route handler method, but doing so is not ideal as it would break the single responsibility principle (SRP).

Another approach could be to create a validator class and delegate the task there. This has the disadvantage that we would have to remember to call this validator at the beginning of each method.

How about creating validation middleware? This could work, but unfortunately, it's not possible to create generic middleware which can be used across all contexts across the whole application. This is because middleware is unaware of the execution context, including the handler that will be called and any of its parameters.

This is, of course, exactly the use case for which pipes are designed. So let's go ahead and refine our validation pipe.

Learn the right way!
80+ chapters
5+ hours of videos
Official certificate
Deep-dive sessions
Explore official courses

Object schema validation#
There are several approaches available for doing object validation in a clean, DRY way. One common approach is to use schema-based validation. Let's go ahead and try that approach.

The Zod library allows you to create schemas in a straightforward way, with a readable API. Let's build a validation pipe that makes use of Zod-based schemas.

Start by installing the required package:

$ npm install --save zod
In the code sample below, we create a simple class that takes a schema as a constructor argument. We then apply the schema.parse() method, which validates our incoming argument against the provided schema.

As noted earlier, a validation pipe either returns the value unchanged or throws an exception.

In the next section, you'll see how we supply the appropriate schema for a given controller method using the @UsePipes() decorator. Doing so makes our validation pipe reusable across contexts, just as we set out to do.

JS

import { PipeTransform, ArgumentMetadata, BadRequestException } from '@nestjs/common';
import { ZodSchema } from 'zod';

export class ZodValidationPipe implements PipeTransform {
constructor(private schema: ZodSchema) {}

transform(value: unknown, metadata: ArgumentMetadata) {
try {
const parsedValue = this.schema.parse(value);
return parsedValue;
} catch (error) {
throw new BadRequestException('Validation failed');
}
}
}
Binding validation pipes#
Earlier, we saw how to bind transformation pipes (like ParseIntPipe and the rest of the Parse\* pipes).

Binding validation pipes is also very straightforward.

In this case, we want to bind the pipe at the method call level. In our current example, we need to do the following to use the ZodValidationPipe:

Create an instance of the ZodValidationPipe
Pass the context-specific Zod schema in the class constructor of the pipe
Bind the pipe to the method
Zod schema example:

import { z } from 'zod';

export const createCatSchema = z
.object({
name: z.string(),
age: z.number(),
breed: z.string(),
})
.required();

export type CreateCatDto = z.infer<typeof createCatSchema>;
We do that using the @UsePipes() decorator as shown below:

cats.controller.tsJS

@Post()
@UsePipes(new ZodValidationPipe(createCatSchema))
async create(@Body() createCatDto: CreateCatDto) {
this.catsService.create(createCatDto);
}

# From reddit post

import {z} from "zod";

export const SomeSchema = z.object({
foo: z.string();
});

export type SomeSchemaType = z.infer<typeof SomeSchema>();
Now just import SomeSchemaType on the frontend and on the backend, import the schema, and you've got yourself an enforced contract

# Dev.to article

End-to-end Typesafe APIs with TypeScript and shared Zod schemas

#

typescript

#

javascript

#

webdev
Validating your data is absolutely necessary. The data could be data fetched from an API, data posted to the API in the request body or any other IO operation. This article will present a method of using TypeScript and Zod to create shared schemas between your frontend and backend. Shared schemas will allow not only achieving the required level of data validation but also provide tooling such as automatically generated shared types that greatly increase productivity!

Let’s create the simplest possible todo app, going through each line of code relevant to data validation.

Follow the code on GitHub, where I have posted a simple example of this app created with Next.js

Why use Zod?
You can use any other validation library, such as the other popular three-letter options, yup or joi. I strongly recommend choosing Zod, as it in my opinion provides a great set of utilities, supports practically every type you could wish to support, has great TypeScript support and a short, easy to remember API.

Especially for people used to TypeScript types, Zod schemas really do their best at making them feel like regular TypeScript schemas. For example, all object properties are required unless explicitly marked as .optional() or .nullable(), similar to how TypeScript types require explicitly declaring a property as optional with ?, | undefined or | null. With other data validation libraries, you might always have to remember to type .string({ required: true }) which just doesn’t feel as natural.

Defining a schema and its auto-generated types
Let’s create a simple Todo app to illustrate how Zod is used both in the backend and the frontend. Let’s first define a schema for a todo item. We declare a Zod object schema with the z.object() method. We can then define each property and its types, each of which can then be refined further with chained methods of those types.

The important part of defining a schema to reach maximum typesafety is to share it between your frontend and backend. This could be done with for example an internal npm package or a monorepo. In some cases, such as with Next.js, you just place the schema for example in a folder such as /lib/schemas and it’s automatically reachable by both your frontend and backend code. Doing this prevents any need for duplicating the schema and maintaining two identical schemas. (Again, an easy source of bugs: “I’ll just quickly change this property to optional…” might be a sentence you hear yourself saying and then forget to change the other copy of the schema).

import { z } from "zod";

export const todoSchema = z.object({
id: z.string(),
done: z.boolean(),
text: z.string().min(1), // min(1) disallows empty strings
important: z.boolean().optional(),
createdAt: z.string(),
updatedAt: z.string(),
})

// For later purposes, we also define a schema for an array of todos
export const todosSchema = z.array(todoSchema);
Next we define a type for todos. Regularly you might start creating a type that matches the schema with export type Todo = { done: boolean; ... }, however that is not only unnecessary but also unproductive as it requires you maintain two versions of the same schema. Instead you can use Zod to autogenerate the schema by type inference (which Zod happens to be very good at).

// .../schemas/todo.schema.ts
export type Todo = z.TypeOf<typeof todoSchema>;
Protip: Instead of exporting these types, you can optionally declare the following type in your types.d.ts file or any other .d.ts file that is included in your tsconfig.json. Doing this means you don’t ever again need to import your types with import { Todo } from "path/to/todo". However declaring types this way means you have to import all dependencies inline. Little ugly, but useful in the long run in my opinion.

// types.d.ts
type Todo = import("zod").TypeOf<
typeof import("./lib/schemas/todo.schema")["todoSchema"]

> ;
> To reflect on the power of shared schemas combined with automatically inferred types: without them, you would have to keep one copy of the both the schema and the interface in your backend and your frontend, which would result in a total of four different schemas defining the exact same thing, each one requiring to be updated when any update is required.

Typesafe fetching with your new schema and types!
Let’s start implementing the API to serve the todos and the frontend functions required to consume them! We create a simple endpoint to serve all todos.

export default async function handler(req: Request, res: Response) {
if (req.method === "GET") {
const todos = await getAllTodosFromDatabase();
res.json(todos);
}
}
The old way of fetching this data in a typesafe manner would involve typecasting. Some typechecking with if (data !== null) or if (typeof data === "object") or multiple other longwinded ways of attempting to validate your data might be present and should automatically signal to the developer that a better solution is required.

// BAD!
async function fetchTodos(): Promise<Todo[]> {
const response = await fetch("/api/todos");
const json = await response.json();
return json as Todo[];
}
Doing this would be an easy source of bugs. You assume your backend delivers objects of a certain shape. Suddenly the array doesn’t even exist, all the objects within it are null, or some of the objects might have a missing id property. You don’t know and you can’t trust until you validate. This is where the schema comes into play.

Validating your data, without any typecasting and with a lot more confidence in your own code can be achieved with just a few lines of code.

// Good.
async function fetchTodos(): Promise<Todo[]> {
const response = await fetch("/api/todos");
const json = await response.json();

    const parsed = todosSchema.safeParse(json);
    if (parsed.success) {
        return parsed.data;
    }

    // Handle errors
    console.error(parsed.error)
    return [];

}
Using the safeParse method, we can easily validate that the data has the correct shape and return it as is, no typecasting necessary - Zod handles typing for you. In case there is an error, parsed.success will be false and the parsed.error property will contain the resulting error. Handling errors is left as an exercise to the reader, however returning an empty array as a default value and logging the error is a start.

There! We now have a simple API to serve data of the correct type and a client function to fetch it with validation. Simple as that!

Updating and creating a todo item
Let’s next tackle the reverse direction: the server validating data received from the client. Again, we can define new shared schemas. This time the schemas define the shape of the object sent as the request body to the API. They can then be used for both validating on the server and defining the correct shape on the frontend. Let’s create schemas for updating and creating todo items (with automatically inferred types).

// lib/schemas/todo.schema.ts

export const todoUpdateSchema = z.object({
id: z.string(),
done: z.boolean(),
});

export const todoCreateSchema = z.object({
text: z.string(),
important: z.boolean(),
});

export type TodoUpdate = z.TypeOf<typeof todoUpdateSchema>;
export type TodoCreate = z.TypeOf<typeof todoCreateSchema>;
Let’s next define simple handlers in the endpoint for these methods.

// pages/api/todos.ts

export default async function handler(req: Request, res: Response) {
// ...
if (req.method === "PATCH") {
const body = todoUpdateSchema.parse(req.body);
updateTodoInDatabase(body.id, body.done);
return res.status(200).end();
}

if (req.method === "POST") {
const body = todoCreateSchema.parse(req.body);
createTodoInDatabase(body);
return res.status(200).end();
}
}
Here instead of using the safeParse() method, we use a more direct method - the parse() method. parse() returns the validated data in its correct shape and throws on invalid data. Sometimes this is preferred, as it results in fewer lines and easier-to-read code but leaves the error handling to be done in a catch-block. For example on a server, where errors may be handled by a common error handler function, this could be a good option (or if you just prefer try { schema.parse(json) } catch (e) { } instead of const parsed = schema.safeParse(json); if (parsed.success) { } else { }!

Consuming these routes on the frontend is as easy as defining the following functions, the types of which are conveniently available already and the exact same types which the backend uses.

async function createTodo(body: TodoCreate) {
await fetch("/api/todos", {
body: JSON.stringify(body),
headers: { "Content-type": "application/json" },
method: "POST",
})
}

async function updateTodo(body: TodoUpdate) {
await fetch("/api/todos", {
body: JSON.stringify(body),
headers: { "Content-type": "application/json" },
method: "PATCH",
})
}
Let’s review
We’re done now! All that’s left to do is hook up the updateTodo, createTodo and fetchTodos functions to the frontend code and to expand upon this idea.

The steps:

Create a schema with Zod (shared between frontend and backend code)
Get automatic types with Zod’s z.TypeOf<typeof schema> type inference (also shared)
Validate data when fetching data or posting data to the server (use .parse() or .safeParse() according to your liking)
And the results:

Automatically generated types
Shared schemas and types between frontend and backend code
Full confidence in your data’s validity
Complete data validation with just a few lines of code
No maintaining symmetric schemas and types in multiple places
The code shown in this article is obviously the bare minimum code of the simplest possible example, but expanding this to larger applications is not only easy but quite honestly a necessity. No application larger than a todo app created for learning a new JS framework should ever communicate data that has not been validated. Moreover, attempting to validate data without shared Zod schemas is just extra work and can even serve as a potential source of bugs.
