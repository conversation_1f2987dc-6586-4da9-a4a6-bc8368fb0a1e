import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

import { Member, MemberSchema } from './member.schema';
import { MembersController } from './members.controller';
import { MembersService } from './members.service';
import { CaslModule } from '../casl/casl.module';
import { Cv, CvSchema } from '../cvs/cv.schema';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Member.name, schema: MemberSchema },
      { name: Cv.name, schema: CvSchema },
    ]),
    CaslModule,
  ],
  controllers: [MembersController],
  providers: [MembersService],
  exports: [MembersService],
})
export class MembersModule {}
